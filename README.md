# DŮM & BYT - WordPress Šablona

Kompletní dokumentace pro WordPress šablonu DŮM & BYT. Tato šablona je postavena na moderních technologiích s důrazem na výkon, přístupnost a flexibilitu.

## Obsah
1. [Úvod](#úvod)
2. [Technologie](#technologie)
3. [Instalace](#instalace)
4. [Struktura šablony](#struktura-šablony)
5. [K<PERSON><PERSON><PERSON><PERSON><PERSON> komponenty](#klíčové-komponenty)
6. [Spr<PERSON>va bannerů](#správa-bannerů)
7. [Správa verzí a cache](#správa-verzí-a-cache)
8. [Vývoj a customizace](#vývoj-a-customizace)
9. [Nasazení](#nasazení)
10. [Řešení problémů](#řešení-problémů)

## Úvod

Šablona DŮM & BYT je navržena pro stejnojmenný webový portál. Šablona poskytuje:
- Responzivní design pro všechna zařízení
- Optimalizovanou hompage s tematickými sekcemi
- Systém pro správu reklamních bannerů
- Integraci s RSS zdroji partnerských webů
- Zobrazení aktuálního čísla časopisu
- Přístupné navigační menu s podporou submenu
- Optimalizovaný obsah článků s generovaným TOC (obsahem)

## Technologie

Šablona využívá tyto technologie:
- **WordPress** - základní CMS
- **Tailwind CSS** - utilita pro styling
- **JavaScript/jQuery** - pro interaktivní prvky (menu, slider, TOC)
- **PHP OOP** - pro modulární architekturu šablony
- **ESBuild** - pro bundlování JavaScriptu
- **NPM** - pro správu závislostí a build procesy

## Instalace

### Vývojové prostředí

1. Naklonujte repozitář do adresáře `wp-content/themes`
2. Spusťte `npm install` pro instalaci všech závislostí
3. Spusťte `npm run dev` pro první build CSS a JS souborů
4. Aktivujte šablonu v administraci WordPress
5. Pro vývoj a sledování změn spusťte `npm run watch`

### Produkční nasazení

1. Proveďte `npm run bundle` pro vytvoření produkčního zip balíčku
2. Nahrajte zip soubor přes WordPress admin rozhraní nebo FTP

## Struktura šablony

Šablona používá modulární strukturu:

```
theme/                      # Hlavní adresář šablony
├── css/                   # Kompilované CSS soubory
├── js/                    # Kompilované JS soubory
├── images/                # Obrázky šablony
├── inc/                   # PHP pomocné funkce a třídy
├── template-parts/        # Znovupoužitelné části šablony
│   ├── content/           # Komponenty pro obsah
│   ├── homepage/          # Sekce pro hompage
│   └── layout/            # Opakující se části layoutu
├── umimeweby/             # Vlastní systém pro bannery
│   ├── assets/            # CSS/JS pro bannery
│   ├── banners/           # Třídy pro správu bannerů
│   └── templates/         # Šablony bannerů
└── languages/             # Překlady
```

## Klíčové komponenty

### Header a navigace

- **Sticky Header** - Při scrollování se hlavička zmenší a zůstane fixní
- **Mobilní menu** - Responzivní menu pro mobilní zařízení
- **Submenu** - Víceúrovňové menu s animovanými rozbalovacími podmenu
- **Drobečková navigace** - Automatická generace na základě struktury webu

Hlavičku lze upravit v souborech:
- `theme/header.php` - Struktura hlavičky
- `theme/template-parts/layout/header-content.php` - Obsah hlavičky
- `theme/css/menu.css` - Styling menu
- `theme/js/menu.js` - Logika menu

Drobečková navigace:
- Automaticky se zobrazuje na všech stránkách kromě homepage
- Zobrazuje cestu od homepage k aktuální stránce
- Podporuje vnořené stránky a hierarchii kategorií
- Soubor: `theme/inc/breadcrumbs.php`

```php
// Použití drobečkové navigace v šabloně
if (function_exists('dumabyt_breadcrumbs')) {
    dumabyt_breadcrumbs();
}
```

### Homepage sekce

Homepage je rozdělena do tematických sekcí:
- **Banner slider** - Hlavní slider v horní části
- **Tematické sekce** - Sekce pro dům, interiér, stavbu, zahradu atd.
- **Postranní panel** - Sidebar s reklamami a doplňkovým obsahem

Všechny sekce jsou v adresáři `theme/template-parts/homepage/sections/`.

### Single post šablona

Článkové stránky obsahují:
- **TOC (Obsah)** - Automaticky generovaný navigační obsah článku
- **Galerie** - Pokud článek obsahuje více obrázků
- **Parametry** - Vlastní meta pole pro specifikace produktů/projektů
- **Sociální sdílení** - Tlačítka pro sdílení
- **Související články** - Podobné články podle kategorie/tagů

Komponenty pro single post jsou v `theme/template-parts/content/components/`.

### Komponenta časopisu

Šablona obsahuje widget pro zobrazení aktuálního čísla časopisu Můj dům:
- Automatické získávání dat o nejnovějším čísle přes web scraping
- Inteligentní cachování dat pomocí WordPress transients
- Zobrazení obálky časopisu s responsive designem
- Tooltip s názvem vydání při najetí myší
- Možnost umístění v hlavičce nebo v postranním panelu

Kód pro tuto komponentu najdete v `theme/inc/class-dumabyt-magazine-widget.php`. Použití:

```php
// Zobrazení časopisu v šabloně
if (function_exists('dumabyt_magazine_widget')) {
    dumabyt_magazine_widget()->render();
}

// Ruční vyčištění cache
dumabyt_magazine_widget()->clear_cache();
```

### RSS Integrace

Šablona obsahuje pokročilý systém pro import článků z partnerských webů:
- Automatické stahování článků z externích RSS
- Cachování pro optimalizaci výkonu
- Vlastní admin interface pro partnery
- Systém cache s možností vyčištění přes CLI nebo admin rozhraní
- Inteligentní zpracování obrázků s fallback variantami

Klíčové soubory:
- `theme/inc/partner-feeds.php` - Hlavní funkcionalita RSS integrace
- `theme/inc/clear-cache.php` - CLI nástroj pro čištění cache
- `theme/inc/image-filters.php` - Zpracování obrázků z RSS a error handling

Použití CLI nástroje pro vyčištění cache:
```bash
wp eval-file wp-content/themes/dumabyt/theme/inc/clear-cache.php
```

## Správa bannerů

Šablona obsahuje propracovaný systém pro správu reklamních bannerů:

### Typy bannerů
- **Square Banner (300x300 px)** - Pro sidebar a vedle carouselu
- **Leaderboard Banner (745x100 px)** - Pro horní část celého webu (globálně)

### Pozice bannerů
- `carousel_sidebar` - Vedle carouselu na homepage (max 2 bannery)
- `homepage_sidebar` - V postranním panelu na homepage
- `homepage_bottom` - V horní části webu (globálně na všech stránkách)
- `article` - V článcích

### Architektura banner systému
Systém používá OOP a Singleton pattern v několika třídách:
- `BannerConfig` - Centrální definice pozic a vlastností
- `BannerPostType` - Registrace vlastního post typu
- `BannerManager` - Práce s databází a výběr bannerů
- `BannerRenderer` - Vykreslování bannerů
- `BannerMetabox`/`NativeMetabox` - Admin rozhraní pro bannery

### Přidání nového banneru
1. V admin rozhraní přejděte na Bannery > Přidat nový
2. Nastavte název, nahrajte obrázek
3. Zvolte typ banneru (Square/Leaderboard)
4. Vyberte pozici zobrazení (pouze jednu pozici na banner)
5. Nastavte URL odkazu
6. Publikujte banner

## Správa verzí a cache

### Verzování šablony

Šablona používá centrální konstantu pro verzování:

```php
define( '_UW_THEME_VERSION', '1.0.1' );
```

Tato hodnota se používá pro:
- Cache busting CSS a JS souborů
- Verzování API
- Identifikaci šablony

### Cache busting

Při změnách v CSS nebo JS souborech:
1. Změňte hodnotu `_UW_THEME_VERSION` v `theme/functions.php` (např. z '1.0.1' na '1.0.2')
2. WordPress automaticky přidá verzi jako query parametr k URL souborů, což vynutí nové načtení

## Vývoj a customizace

### CSS (Tailwind)

Pro úpravy stylů:
1. Editujte soubory v adresáři `tailwind/`
2. Spusťte `npm run watch` pro sledování změn
3. Tailwind automaticky vygeneruje potřebné CSS třídy

Šablona používá tyto hlavní CSS soubory:
- `tailwind.css` - Hlavní Tailwind konfigurace
- `tailwind/custom/*.css` - Vlastní styly a komponenty
- `theme/css/*.css` - Specializované styly pro menu, TOC, atd.

### JavaScript

Pro úpravy skriptů:
1. Editujte soubory v adresáři `javascript/`
2. Watch proces zkompiluje soubory do `theme/js/`

Hlavní JS soubory:
- `javascript/script.js` - Obecné skripty
- `javascript/menu.js` - Logika pro navigaci
- `javascript/slideshow.js` - Sliderová komponenta
- `javascript/single-post.js` - Skripty pro detaily článku

### Template šablony

Šablona používá standardní WordPress template hierarchii:
- `front-page.php` - Homepage
- `single.php` - Detail článku
- `archive.php` - Archivní stránky
- `category.php` - Kategorie
- `page.php` - Standardní stránky

Pro customizaci:
1. Vytvořte kopii odpovídající template šablony
2. Upravte podle potřeby
3. Používejte existující strukturu template parts

## Nasazení

### Produkční build

1. Spusťte `npm run bundle`
2. Použijte vygenerovaný zip soubor:
   - Upload přes WordPress admin
   - Rozbalení přes FTP
   - Nasazení pomocí CI/CD

### Výkonnostní optimalizace

- Obrázky jsou automaticky zpracovány přes WordPress filtry pro optimální velikosti
- CSS je minimalizováno pomocí PostCSS
- JavaScript je bundlován a minifikován pomocí ESBuild
- Pro dodatečné zrychlení zvažte použití cache pluginu

## Řešení problémů

### Cache problémy

Pokud se změny CSS/JS neprojevují:
1. Zvyšte hodnotu `_UW_THEME_VERSION` v `theme/functions.php`
2. Spusťte `wp cache flush` pro vyčištění WordPress cache
3. Vymažte cache vašeho prohlížeče

### Debugování

Pro aktivaci debugování:
1. Nastavte `WP_DEBUG` na `true` v `wp-config.php`
2. Chybové hlášky se zobrazí v admin rozhraní
3. Pro banner systém existují specifické diagnostické zprávy

### Konflikty pluginů

Šablona je testována s běžnými WordPress pluginy. Při potížích:
1. Dočasně deaktivujte všechny pluginy
2. Aktivujte je postupně pro identifikaci problému
3. Zkontrolujte PHP verzi a kompatibilitu

---

Vytvořeno s ❤️ pro DŮM & BYT
