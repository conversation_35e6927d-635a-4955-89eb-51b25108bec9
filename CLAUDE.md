# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands
- Development: `npm run dev` or `npm run development`
- Watch for changes: `npm run watch`
- Production: `npm run prod` or `npm run production`
- Bundle for release: `npm run bundle`

## Lint Commands
- Lint check: `npm run lint`
- Auto-fix: `npm run lint-fix`
- PHP Lint: `phpcs`

## Code Style Guidelines
- JavaScript: Uses ESLint with WordPress coding standards
- PHP: Follows WordPress-Extra and WordPress-Docs coding standards
- CSS: Uses Tailwind CSS with PostCSS processing
- Naming: PHP functions/classes prefixed with `_uw_theme_`
- Formatting: Uses Prettier with WordPress config
- I18n: Text domain is `_uw-theme`
- PHP Version: Minimum 7.4
- WordPress Version: Minimum 6.2