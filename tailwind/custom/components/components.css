/**
 * Custom styles to immediately follow Tailwind's `components` layer
 *
 * "Add more opinionated, complex classes like buttons, form controls, alerts,
 * etc; the sort of pre-built components you often see in other frameworks that
 * you might need to override with utility classes."
 *
 * — from https://tailwindcss.com/docs/plugins#adding-components
 */

/**
 * Simple Header Styles
 */

/* Container for centering content */
.container {
  @apply px-4 mx-auto;
  max-width: 1200px; /* Standard width for the site */
}

/* Basic header styling */
.site-header {
  @apply bg-white shadow-sm w-full;
}

.site-branding {
  @apply bg-gradient-to-r from-amber-50 to-white border-b border-amber-100 w-full;
}

.header-content {
  @apply flex justify-between items-center py-3 mx-auto;
}

/* Logo styling */
.site-logo {
  @apply flex-shrink-0;
}

.site-title {
  @apply m-0 font-bold;
}

.site-title a {
  @apply block no-underline;
}

.logo-img {
  @apply h-14 w-auto;
}

/* Utility navigation */
.utility-nav {
  @apply flex items-center;
}

.utility-links {
  @apply flex list-none m-0 p-0;
}

.utility-links li {
  @apply mx-2;
}

.utility-links a {
  @apply text-sm uppercase text-amber-800 hover:text-amber-950 no-underline;
}

/* Search form styling */
.search-form {
  @apply flex items-center ml-4;
}

.search-form input[type="search"] {
  @apply py-1 px-3 border border-amber-200 rounded-l bg-white text-sm;
  width: 150px;
}

.search-form .search-submit {
  @apply py-1 px-3 bg-amber-800 text-white rounded-r border border-amber-800 text-sm;
}

/* Mobile menu toggle */
.menu-toggle {
  @apply ml-4 p-2 bg-transparent border-0 hidden;
}

@media (max-width: 768px) {
  .menu-toggle {
    @apply block;
  }
  
  .utility-links {
    @apply hidden;
  }
  
  .utility-nav .search-form {
    @apply hidden;
  }
}

.menu-icon {
  @apply block h-0.5 w-6 bg-amber-800 relative;
}

.menu-icon:before,
.menu-icon:after {
  @apply block h-0.5 w-6 bg-amber-800 absolute left-0;
  content: '';
}

.menu-icon:before {
  top: -6px;
}

.menu-icon:after {
  bottom: -6px;
}

/* Main navigation */
.main-navigation {
  @apply bg-white border-b border-amber-100 w-full;
}

.main-navigation .container {
  @apply flex justify-between;
}

#primary-menu {
  @apply flex list-none m-0 p-0;
}

#primary-menu > li {
  @apply relative;
}

#primary-menu > li > a {
  @apply block px-4 py-3 text-amber-900 font-medium uppercase no-underline hover:text-amber-950;
}

#primary-menu > li.current-menu-item > a {
  @apply font-bold;
}

/* Submenu styling - simple dropdown on hover */
.sub-menu {
  @apply absolute hidden bg-white min-w-[200px] shadow-md border border-gray-200 rounded p-2 z-10;
}

#primary-menu > li:hover > .sub-menu {
  @apply block;
}

.sub-menu li {
  @apply border-b border-gray-100 last:border-b-0;
}

.sub-menu a {
  @apply block px-3 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 no-underline;
}

/* Mobile menu */
.mobile-menu {
  @apply bg-white border-t border-amber-100 shadow-lg w-full;
}

.mobile-menu .container {
  @apply mx-auto;
}

#mobile-menu-items {
  @apply list-none m-0 p-0;
}

#mobile-menu-items li {
  @apply border-b border-gray-100;
}

#mobile-menu-items a {
  @apply block px-4 py-3 text-gray-700 no-underline;
}

.mobile-utility-links {
  @apply list-none m-0 p-0 border-t border-amber-100 bg-amber-50;
}

.mobile-utility-links li {
  @apply border-b border-gray-100 last:border-b-0;
}

.mobile-utility-links a {
  @apply block px-4 py-3 text-amber-800 no-underline;
}

/* Breadcrumbs styling */
.breadcrumbs {
  @apply bg-amber-50 border-b border-amber-100 py-2 text-sm text-amber-800 w-full;
}

.breadcrumbs .container {
  @apply w-full;
}

.breadcrumbs a {
  @apply text-amber-800 hover:text-amber-950 no-underline;
}

/**
 * Post title styles
 *
 * These will be applied to all headings with a `page-title` or `entry-title`
 * class on the frontend and to the post title in the block editor.
 *
 * The supplied styles are meant to match the default `h1` classes from
 * Tailwind Typography.
 */
.page-title,
.entry-title {
	@apply max-w-content mx-auto mb-6 text-3xl font-extrabold text-neutral-900;
}

/**
 * Layout styles for centered content areas
 *
 * If you are controlling the width of your content area with styles applied
 * to its container, you can delete the following styles whose selectors begin
 * with `.page-content >` and `.entry-content >`. For more details, please see
 * the following:
 *
 * https://make.wordpress.org/core/2021/06/29/on-layout-and-content-width-in-wordpress-5-8/
 */
.page-content > *,
.entry-content > * {
	/* Content width from the `theme.json` file */
	@apply max-w-content mx-auto;
}

.entry-content > .alignwide {
	/* Wide width from the `theme.json` file */
	@apply max-w-wide;
}

.entry-content > .alignfull {
	@apply max-w-none;
}

.entry-content > .alignleft {
	@apply float-left mr-8;
}

.entry-content > .alignright {
	@apply float-right ml-8;
}
