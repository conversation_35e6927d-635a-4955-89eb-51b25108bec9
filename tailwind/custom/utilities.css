/**
 * Custom utilities for the theme
 */

.full-width {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
}

.bg-gradient-superlight {
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
}

/* Grid adjustments for print */
@media print {
    .print-cols-2 {
        grid-template-columns: repeat(2, 1fr) !important;
    }
    
    .print-cols-3 {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

/* Custom scrollbar in TOC */
.toc-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.toc-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.toc-scrollbar::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.toc-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #666;
}

/* Banner Carousel Alignment Fix */
.carousel-sidebar-fix {
    margin-top: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.carousel-sidebar-fix .uw-banner:first-child,
.carousel-sidebar-fix > div:first-child {
    margin-top: 0 !important;
}

/* Custom alignments for homepage sidebar */
.lg\:col-span-1 .uw-banner {
    margin-top: 0 !important;
    margin-bottom: 1.5rem !important;
}

.lg\:col-span-1 > div:first-child {
    margin-top: 0 !important;
}
