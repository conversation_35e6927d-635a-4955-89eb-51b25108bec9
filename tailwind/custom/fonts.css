/**
 * Custom `@font-face` rules
 *
 * These will be added immediately before Tailwind's `base` layer.
 */

/* Typekit fonts */
/* These are loaded via Typekit script in header.php */

/* Set base font families */
:root {
  --font-heading-primary: "trajan-pro-3", serif;
  --font-heading-secondary: "futura-pt", sans-serif;
  --font-body: "futura-pt", sans-serif;
}

/* Base element styles */
html, body {
  font-family: var(--font-body);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading-secondary);
  font-weight: 500;
}

h1, .entry-title, .page-title {
  font-family: var(--font-heading-primary);
  font-weight: 600;
}

/* Utility classes for font families */
.font-trajan {
  font-family: var(--font-heading-primary);
}

.font-futura {
  font-family: var(--font-heading-secondary);
}
