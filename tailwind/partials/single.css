/**
 * Single post styles
 */

/* Shadow text for better readability on image backgrounds */
.shadow-text {
    text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.8);
}

/* Article content enhancements */
.article-content h2 {
    font-family: var(--font-trajan), serif;
    color: #1f2937;
    margin-top: 2em;
    font-weight: 700;
}

.article-content h3 {
    color: #374151;
    margin-top: 1.5em;
    font-weight: 600;
}

.article-content p {
    margin-bottom: 1.25em;
}

.article-content blockquote {
    border-left-color: #f59e0b;
    background-color: #fffbeb;
}

.article-content figure {
    margin: 2em 0;
}

.article-content figcaption {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
    margin-top: 0.5em;
}

.article-content img {
    border-radius: 0.375rem;
}

.article-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1.5em 0;
}

.article-content table th {
    background-color: #f9fafb;
    font-weight: 600;
}

.article-content table th,
.article-content table td {
    border: 1px solid #e5e7eb;
    padding: 0.75rem;
    text-align: left;
}

/* Gallery image zoom effect */
.gallery-item img {
    transition: transform 0.4s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

/* Card elevation effect */
.card-elevation {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elevation:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Reading progress bar */
.progress-container {
    width: 100%;
    height: 4px;
    background-color: transparent;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transition: opacity 0.3s ease;
}

.progress-bar {
    height: 4px;
    background-color: #f59e0b;
    width: 0%;
    transition: width 0.1s ease;
}

/* Hide scrollbar for the gallery carousel */
.scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;     /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;             /* Chrome, Safari and Opera */
}

/* Fix title alignment on single post page */
.entry-title {
    text-align: left !important;
}
