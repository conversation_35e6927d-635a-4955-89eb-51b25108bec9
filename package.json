{"private": true, "devDependencies": {"@_tw/typography": "^0.5.17", "@tailwindcss/postcss": "^4.0.14", "@wordpress/prettier-config": "^4.20.0", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "cross-env": "^7.0.3", "esbuild": "^0.25.1", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "npm-run-all": "^4.1.5", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "postcss-nesting": "^13.0.1", "postcss-url": "^10.1.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.14"}, "scripts": {"development:tailwind:frontend": "npx postcss ./tailwind.css -o ./theme/style.css --verbose", "development:tailwind:editor": "cross-env _TW_TARGET=editor npx postcss ./tailwind/tailwind-editor.css -o ./theme/style-editor.css", "development:tailwind:editor:extra": "npx postcss ./tailwind/tailwind-editor-extra.css -o ./theme/style-editor-extra.css", "development:esbuild": "npx esbuild ./javascript/script.js ./javascript/block-editor.js ./javascript/single-post.js --target=esnext --bundle --outdir=./theme/js --out-extension:.js=.min.js", "development": "run-p \"development:**\"", "dev": "npm run development", "watch:tailwind:frontend": "npm run development:tailwind:frontend -- --watch", "watch:tailwind:editor": "npm run development:tailwind:editor -- --watch", "watch:tailwind:editor:extra": "npm run development:tailwind:editor:extra -- --watch", "watch:esbuild": "npm run development:esbuild -- --watch", "watch": "run-p \"watch:**\"", "lint:eslint": "npx eslint", "lint:prettier": "npx prettier --check .", "lint": "run-p \"lint:*\"", "lint-fix:eslint": "npx eslint --fix", "lint-fix:prettier": "npx prettier --write .", "lint-fix": "run-p \"lint-fix:*\"", "production:tailwind:frontend": "npm run development:tailwind:frontend -- --minify", "production:tailwind:editor": "npm run development:tailwind:editor -- --minify", "production:tailwind:editor:extra": "npm run development:tailwind:editor:extra -- --minify", "production:esbuild": "npm run development:esbuild -- --minify", "production": "run-p \"production:**\"", "prod": "npm run production", "zip": "node node_scripts/zip.js _uw-theme", "bundle": "run-s production zip"}}