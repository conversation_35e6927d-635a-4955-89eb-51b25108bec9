This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
javascript/
  block-editor.js
  script.js
  slideshow.js
  slideshow.min.js
node_scripts/
  zip.js
tailwind/
  custom/
    components/
      components.css
    base.css
    file-header.css
    fonts.css
    utilities.css
  partials/
    footer.css
    header.css
  tailwind-editor-extra.css
  tailwind-editor.css
  tailwind-theme.css
  tailwind-typography.config.js
theme/
  images/
    partner-articles/
      cihla.svg
      dimenze.svg
      mujdum.svg
      pasivni.svg
      vila.svg
    partners/
      bmo-logo.svg
      imaterialy-logo.svg
      mujdum-logo.svg
      rodinnydum-logo.svg
      stavbaweb-logo.svg
  inc/
    breadcrumbs.php
    class-dumabyt-walker-nav-menu.php
    template-functions.php
    template-tags.php
  js/
    block-editor.min.js
    readme.txt
    script.min.js
    slideshow.min.js
  languages/
    readme.txt
  template-parts/
    content/
      content-excerpt.php
      content-none.php
      content-page.php
      content-single.php
      content.php
    layout/
      footer-content.php
      header-content.php
  404.php
  archive.php
  comments.php
  footer.php
  front-page.php
  functions.php
  header.php
  index.php
  page.php
  search.php
  single.php
  style-editor-extra.css
  style-editor.css
  style.css
  theme.json
.gitignore
CLAUDE.md
composer.json
eslint.config.mjs
LICENSE
package.json
phpcs.xml.dist
postcss.config.mjs
prettier.config.mjs
README.md
tailwind.css
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="theme/images/partner-articles/cihla.svg">
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="300" height="200" fill="#dddddd"/><text x="150" y="100" font-family="Arial" font-size="14" text-anchor="middle" alignment-baseline="middle" fill="#666666">Cihla patří i do podhůří</text></svg>
</file>

<file path="theme/images/partner-articles/dimenze.svg">
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="300" height="200" fill="#dddddd"/><text x="150" y="100" font-family="Arial" font-size="14" text-anchor="middle" alignment-baseline="middle" fill="#666666">Dom so štvrtou dimenziou</text></svg>
</file>

<file path="theme/images/partner-articles/mujdum.svg">
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="300" height="200" fill="#dddddd"/><text x="150" y="100" font-family="Arial" font-size="14" text-anchor="middle" alignment-baseline="middle" fill="#666666">Dům na výhledech</text></svg>
</file>

<file path="theme/images/partner-articles/pasivni.svg">
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="300" height="200" fill="#dddddd"/><text x="150" y="100" font-family="Arial" font-size="14" text-anchor="middle" alignment-baseline="middle" fill="#666666">Pasivní bydlení</text></svg>
</file>

<file path="theme/images/partner-articles/vila.svg">
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="300" height="200" fill="#dddddd"/><text x="150" y="100" font-family="Arial" font-size="14" text-anchor="middle" alignment-baseline="middle" fill="#666666">Grossmannova vila</text></svg>
</file>

<file path="theme/images/partners/bmo-logo.svg">
<svg width="200" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="80" fill="#e5e5e5"/><text x="100" y="40" font-family="Arial" font-size="16" text-anchor="middle" alignment-baseline="middle" fill="#666666">BUSINESS MEDIA ONE</text></svg>
</file>

<file path="theme/images/partners/imaterialy-logo.svg">
<svg width="200" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="80" fill="#e5e5e5"/><text x="100" y="40" font-family="Arial" font-size="16" text-anchor="middle" alignment-baseline="middle" fill="#666666">iMateriály</text></svg>
</file>

<file path="theme/images/partners/mujdum-logo.svg">
<svg width="200" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="80" fill="#e5e5e5"/><text x="100" y="40" font-family="Arial" font-size="16" text-anchor="middle" alignment-baseline="middle" fill="#666666">MŮJ DŮM</text></svg>
</file>

<file path="theme/images/partners/rodinnydum-logo.svg">
<svg width="200" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="80" fill="#e5e5e5"/><text x="100" y="40" font-family="Arial" font-size="16" text-anchor="middle" alignment-baseline="middle" fill="#666666">RODINNÝ dům</text></svg>
</file>

<file path="theme/images/partners/stavbaweb-logo.svg">
<svg width="200" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="80" fill="#e5e5e5"/><text x="100" y="40" font-family="Arial" font-size="16" text-anchor="middle" alignment-baseline="middle" fill="#666666">StavbaWEB</text></svg>
</file>

<file path="javascript/block-editor.js">
/**
 * Block editor modifications
 *
 * This file is loaded only by the block editor. Use it to modify the block
 * editor via its APIs.
 *
 * The JavaScript code you place here will be processed by esbuild, and the
 * output file will be created at `../theme/js/block-editor.min.js` and
 * enqueued in `../theme/functions.php`.
 *
 * For esbuild documentation, please see:
 * https://esbuild.github.io/
 */

/**
 * This import adds your front-end post title and Tailwind Typography classes
 * to the block editor. It also adds some helper classes so you can access the
 * post type when modifying the block editor’s appearance.
 */
import '@_tw/typography/block-editor-classes';

wp.domReady(() => {
	/**
	 * Add support for Tailwind Typography’s `lead` class via a block style.
	 */
	wp.blocks.registerBlockStyle('core/paragraph', {
		name: 'lead',
		label: 'Lead',
	});

	// Add additional block editor modifications here. For example, you could
	// register another block style:
	//
	// wp.blocks.registerBlockStyle( 'core/quote', {
	// 	name: 'fancy-quote',
	// 	label: 'Fancy Quote',
	// } );
});
</file>

<file path="javascript/script.js">
/**
 * Front-end JavaScript
 *
 * The JavaScript code you place here will be processed by esbuild. The output
 * file will be created at `../theme/js/script.min.js` and enqueued in
 * `../theme/functions.php`.
 *
 * For esbuild documentation, please see:
 * https://esbuild.github.io/
 */

// Mobile menu toggle functionality
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mobileMenu = document.querySelector('.mobile-menu');
  
  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function() {
      // Toggle the menu visibility
      mobileMenu.classList.toggle('hidden');
      
      // Update aria-expanded attribute
      const isExpanded = mobileMenuToggle.getAttribute('aria-expanded') === 'true';
      mobileMenuToggle.setAttribute('aria-expanded', !isExpanded);
    });
  }
  
  // Handle submenu toggles for mobile view
  const menuItemsWithChildren = document.querySelectorAll('.mobile-menu .menu-item-has-children');
  
  menuItemsWithChildren.forEach(function(item) {
    // Create dropdown toggle button
    const toggleButton = document.createElement('button');
    toggleButton.classList.add('submenu-toggle');
    toggleButton.setAttribute('aria-expanded', 'false');
    toggleButton.innerHTML = '<span class="sr-only">Toggle submenu</span>+';
    
    // Insert toggle button after the link
    const link = item.querySelector('a');
    if (link) {
      link.parentNode.insertBefore(toggleButton, link.nextSibling);
      
      // Add click event to toggle button
      toggleButton.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Find the submenu
        const submenu = item.querySelector('.sub-menu');
        if (submenu) {
          // Toggle visibility
          submenu.classList.toggle('hidden');
          
          // Update aria-expanded
          const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';
          toggleButton.setAttribute('aria-expanded', !isExpanded);
          
          // Change toggle symbol
          toggleButton.innerHTML = isExpanded ? 
            '<span class="sr-only">Toggle submenu</span>+' : 
            '<span class="sr-only">Toggle submenu</span>-';
        }
      });
    }
  });
});

// Add CSS classes to submenus in dropdown menu
document.addEventListener('DOMContentLoaded', function() {
  const primaryMenu = document.getElementById('primary-menu');
  
  if (primaryMenu) {
    // Handle hover functionality for desktop menu
    const menuItems = primaryMenu.querySelectorAll('.menu-item-has-children');
    
    menuItems.forEach(function(item) {
      // Add dropdown indicators
      const link = item.querySelector('a');
      if (link) {
        const indicator = document.createElement('span');
        indicator.classList.add('dropdown-indicator');
        indicator.innerHTML = '▼';
        link.appendChild(indicator);
      }
      
      // Handle hover events
      item.addEventListener('mouseenter', function() {
        const submenu = item.querySelector('.sub-menu');
        if (submenu) {
          submenu.classList.add('active');
        }
      });
      
      item.addEventListener('mouseleave', function() {
        const submenu = item.querySelector('.sub-menu');
        if (submenu) {
          submenu.classList.remove('active');
        }
      });
    });
  }
});
</file>

<file path="javascript/slideshow.js">
/**
 * Slideshow functionality for DŮM A BYT homepage
 */

document.addEventListener('DOMContentLoaded', function() {
    // Find the slideshow container
    const slideshowContainer = document.querySelector('.slideshow-container');
    if (!slideshowContainer) return;

    const slides = slideshowContainer.querySelectorAll('.slide');
    if (!slides.length) return;

    // Find or create navigation dots
    let dotsContainer = slideshowContainer.querySelector('.slideshow-dots');
    if (!dotsContainer) {
        dotsContainer = document.createElement('div');
        dotsContainer.className = 'slideshow-dots absolute bottom-4 right-4 flex space-x-2 z-10';
        slideshowContainer.appendChild(dotsContainer);

        // Create dots based on number of slides
        slides.forEach((_, index) => {
            const dot = document.createElement('button');
            dot.className = 'dot w-3 h-3 rounded-full bg-white/50 hover:bg-white/80 transition-colors duration-300';
            dot.setAttribute('aria-label', `Přejít na slide ${index + 1}`);
            dot.dataset.slideIndex = index;
            dotsContainer.appendChild(dot);
        });
    }

    const dots = dotsContainer.querySelectorAll('.dot');

    // Initialize with first slide active
    let currentSlide = 0;
    updateSlideState();

    // Set up auto-rotation
    let slideshowInterval = setInterval(nextSlide, 6000);

    // Add click handler to dots
    dots.forEach(dot => {
        dot.addEventListener('click', function() {
            currentSlide = parseInt(this.dataset.slideIndex);
            updateSlideState();
            resetInterval();
        });
    });

    // Add keyboard navigation
    slideshowContainer.setAttribute('tabindex', '0');
    slideshowContainer.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            prevSlide();
            resetInterval();
        } else if (e.key === 'ArrowRight') {
            nextSlide();
            resetInterval();
        }
    });

    // Add swipe functionality for touch devices
    let touchStartX = 0;
    let touchEndX = 0;
    
    slideshowContainer.addEventListener('touchstart', e => {
        touchStartX = e.changedTouches[0].screenX;
    }, {passive: true});
    
    slideshowContainer.addEventListener('touchend', e => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    }, {passive: true});

    function handleSwipe() {
        const swipeThreshold = 50;
        if (touchEndX < touchStartX - swipeThreshold) {
            // Swipe left, show next slide
            nextSlide();
            resetInterval();
        } else if (touchEndX > touchStartX + swipeThreshold) {
            // Swipe right, show previous slide
            prevSlide();
            resetInterval();
        }
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        updateSlideState();
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + slides.length) % slides.length;
        updateSlideState();
    }

    function updateSlideState() {
        // Update slides
        slides.forEach((slide, index) => {
            if (index === currentSlide) {
                slide.classList.remove('opacity-0', 'translate-x-full', '-translate-x-full');
                slide.classList.add('opacity-100', 'translate-x-0');
            } else {
                slide.classList.remove('opacity-100', 'translate-x-0');
                slide.classList.add('opacity-0');
                
                // Add direction-based transform based on index
                if (index > currentSlide) {
                    slide.classList.add('translate-x-full');
                    slide.classList.remove('-translate-x-full');
                } else {
                    slide.classList.add('-translate-x-full');
                    slide.classList.remove('translate-x-full');
                }
            }
        });

        // Update dots
        dots.forEach((dot, index) => {
            if (index === currentSlide) {
                dot.classList.remove('bg-white/50');
                dot.classList.add('bg-white');
                dot.setAttribute('aria-current', 'true');
            } else {
                dot.classList.remove('bg-white');
                dot.classList.add('bg-white/50');
                dot.removeAttribute('aria-current');
            }
        });
    }

    function resetInterval() {
        clearInterval(slideshowInterval);
        slideshowInterval = setInterval(nextSlide, 6000);
    }
});
</file>

<file path="javascript/slideshow.min.js">
/**
 * Slideshow functionality for DŮM A BYT homepage
 */
document.addEventListener("DOMContentLoaded",function(){const e=document.querySelector(".slideshow-container");if(!e)return;const t=e.querySelectorAll(".slide");if(!t.length)return;let s=e.querySelector(".slideshow-dots");s||(s=document.createElement("div"),s.className="slideshow-dots absolute bottom-4 right-4 flex space-x-2 z-10",e.appendChild(s),t.forEach((e,t)=>{const a=document.createElement("button");a.className="dot w-3 h-3 rounded-full bg-white/50 hover:bg-white/80 transition-colors duration-300",a.setAttribute("aria-label",`Přejít na slide ${t+1}`),a.dataset.slideIndex=t,s.appendChild(a)}));const a=s.querySelectorAll(".dot");let n=0;function o(){t.forEach((e,t)=>{t===n?(e.classList.remove("opacity-0","translate-x-full","-translate-x-full"),e.classList.add("opacity-100","translate-x-0")):(e.classList.remove("opacity-100","translate-x-0"),e.classList.add("opacity-0"),t>n?(e.classList.add("translate-x-full"),e.classList.remove("-translate-x-full")):(e.classList.add("-translate-x-full"),e.classList.remove("translate-x-full")))}),a.forEach((e,t)=>{t===n?(e.classList.remove("bg-white/50"),e.classList.add("bg-white"),e.setAttribute("aria-current","true")):(e.classList.remove("bg-white"),e.classList.add("bg-white/50"),e.removeAttribute("aria-current"))})}o();let l=setInterval(function(){n=(n+1)%t.length,o()},6e3);function i(){clearInterval(l),l=setInterval(function(){n=(n+1)%t.length,o()},6e3)}a.forEach(e=>{e.addEventListener("click",function(){n=parseInt(this.dataset.slideIndex),o(),i()})}),e.setAttribute("tabindex","0"),e.addEventListener("keydown",function(e){"ArrowLeft"===e.key?(n=(n-1+t.length)%t.length,o(),i()):"ArrowRight"===e.key&&(n=(n+1)%t.length,o(),i())});let r=0,c=0;e.addEventListener("touchstart",e=>{r=e.changedTouches[0].screenX},{passive:!0}),e.addEventListener("touchend",e=>{c=e.changedTouches[0].screenX,c<r-50?(n=(n+1)%t.length,o(),i()):c>r+50&&(n=(n-1+t.length)%t.length,o(),i())},{passive:!0})});
</file>

<file path="node_scripts/zip.js">
#!/usr/bin/env node

/*
 * Based upon the Archiver quickstart.
 * @see: https://www.archiverjs.com/docs/quickstart
 */

// Require modules.
const AdmZip = require('adm-zip');
const archiver = require('archiver');
const fs = require('fs');

const args = process.argv.slice(2);
const slug = args[0];

if (slug) {
	// Set the path for the ZIP file.
	const zipFilePath = __dirname + '/../' + slug + '.zip';

	// Create a file to stream archive data to.
	const output = fs.createWriteStream(zipFilePath);
	const archive = archiver('zip');

	// Listen for all archive data to be written.
	output.on('close', function () {
		console.log(archive.pointer() + ' total bytes.');
		console.log('Theme ZIP file created.');

		// Read the zip file.
		const zip = new AdmZip(zipFilePath);

		// Load `./functions.php` from the zip file.
		const entry = zip.getEntry(slug + '/functions.php');

		if (entry) {
			// Get the contents of `./functions.php`.
			const originalContent = zip.readAsText(entry);

			// Replace the version string with a date-based version string.
			const updatedContent = originalContent.replace(
				/(define\( '[A-Z0-9_]*_VERSION', ')(.*)(' \);)/g,
				'$1' +
					parseInt(Math.floor(Date.now() / 1000), 10).toString(36) +
					'$3'
			);

			// Update the contents of `./functions.php`.
			zip.updateFile(entry, Buffer.from(updatedContent));

			// Write the changes back to the zip file.
			zip.writeZip(zipFilePath);

			console.log(
				'Date-based versioning string successfully added to `./functions.php`.'
			);
		}
	});

	// This event is fired when the data source is drained no matter what was the data source.
	// It is not part of this library but rather from the NodeJS Stream API.
	// @see: https://nodejs.org/api/stream.html#stream_event_end
	output.on('end', function () {
		console.log('Data has been drained');
	});

	// Catch warnings.
	archive.on('warning', function (err) {
		if (err.code === 'ENOENT') {
			// log warning
		} else {
			// throw error
			throw err;
		}
	});

	// Catch errors.
	archive.on('error', function (err) {
		throw err;
	});

	// Pipe archive data to the file.
	archive.pipe(output);

	// Append the entire contents of the theme directory to a directory with
	// the theme slug.
	archive.directory(__dirname + '/../theme/', slug);

	// Finalize the archive.
	archive.finalize();
}
</file>

<file path="tailwind/custom/components/components.css">
/**
 * Custom styles to immediately follow Tailwind's `components` layer
 *
 * "Add more opinionated, complex classes like buttons, form controls, alerts,
 * etc; the sort of pre-built components you often see in other frameworks that
 * you might need to override with utility classes."
 *
 * — from https://tailwindcss.com/docs/plugins#adding-components
 */

/**
 * Header and navigation styles
 */
/* Main header styles */
#masthead {
  @apply shadow-sm;
}

/* Logo styling */
.logo-container a {
  @apply no-underline;
}

/* Top navigation menu */
.top-links a {
  @apply no-underline transition duration-200;
}

/* Main navigation styling */
.main-navigation {
  @apply relative z-10;
}

#primary-menu {
  @apply flex flex-row space-x-1 py-4;
}

#primary-menu > li {
  @apply relative;
}

#primary-menu > li > a {
  @apply block px-4 py-2 font-medium uppercase text-gray-700 hover:text-gray-900 transition duration-200 no-underline;
}

#primary-menu > li.current-menu-item > a,
#primary-menu > li.current-menu-ancestor > a {
  @apply text-gray-900 font-bold;
}

/* Dropdown indicator */
.dropdown-indicator {
  @apply ml-1 text-xs inline-block;
}

/* Submenu styling */
.sub-menu {
  @apply hidden absolute bg-white min-w-[200px] shadow-md z-20 border border-gray-200 rounded-sm;
}

#primary-menu > li:hover > .sub-menu,
.sub-menu.active {
  @apply block;
}

.sub-menu li {
  @apply border-b border-gray-100 last:border-b-0;
}

.sub-menu li a {
  @apply block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 no-underline;
}

/* Mobile menu */
.mobile-menu {
  @apply bg-white border-t border-gray-200;
}

.mobile-menu ul {
  @apply py-2;
}

.mobile-menu li {
  @apply relative;
}

.mobile-menu li a {
  @apply block px-4 py-2 text-gray-700 no-underline;
}

.mobile-menu .sub-menu {
  @apply static w-full shadow-none border-0 bg-gray-50;
}

.mobile-menu .sub-menu li:first-child {
  @apply border-t border-gray-100;
}

.submenu-toggle {
  @apply absolute right-4 top-2 w-6 h-6 text-center leading-6 bg-gray-200 rounded-full text-gray-700;
}

/* Breadcrumbs styling */
.breadcrumbs {
  @apply text-sm text-gray-600;
}

.breadcrumbs a {
  @apply text-gray-600 hover:text-gray-900 no-underline;
}

/* Search form styling */
.search-form {
  @apply flex items-center;
}

.search-form label {
  @apply flex-grow;
}

.search-field {
  @apply w-full border border-gray-300 rounded-l-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-gray-400;
}

.search-submit {
  @apply bg-gray-700 hover:bg-gray-900 text-white font-medium py-2 px-4 rounded-r-md transition duration-200;
}

/**
 * Post title styles
 *
 * These will be applied to all headings with a `page-title` or `entry-title`
 * class on the frontend and to the post title in the block editor.
 *
 * The supplied styles are meant to match the default `h1` classes from
 * Tailwind Typography.
 */
.page-title,
.entry-title {
	@apply max-w-content mx-auto mb-6 text-3xl font-extrabold text-neutral-900;
}

/**
 * Layout styles for centered content areas
 *
 * If you are controlling the width of your content area with styles applied
 * to its container, you can delete the following styles whose selectors begin
 * with `.page-content >` and `.entry-content >`. For more details, please see
 * the following:
 *
 * https://make.wordpress.org/core/2021/06/29/on-layout-and-content-width-in-wordpress-5-8/
 */
.page-content > *,
.entry-content > * {
	/* Content width from the `theme.json` file */
	@apply max-w-content mx-auto;
}

.entry-content > .alignwide {
	/* Wide width from the `theme.json` file */
	@apply max-w-wide;
}

.entry-content > .alignfull {
	@apply max-w-none;
}

.entry-content > .alignleft {
	@apply float-left mr-8;
}

.entry-content > .alignright {
	@apply float-right ml-8;
}
</file>

<file path="tailwind/custom/base.css">
/**
 * Custom styles to immediately follow Tailwind’s `base` layer
 */

/**
 * This uses the background and foreground colors declared in the `theme.json`
 * file and is applied both to the front end and in the block editor.
 */
body {
	@apply bg-background text-foreground font-sans;
}
</file>

<file path="tailwind/custom/file-header.css">
/*!
Theme Name: _uw-theme
Theme URI: https://umimeweby.cz
Author: Team UW
Author URI: https://underscoretw.com/
Description: Custom sablona pro DUM A BYT
Version: 0.1.0
Tested up to: 6.2
Requires PHP: 7.4
License: GNU General Public License v2 or later
License URI: LICENSE
Text Domain: _uw-theme
Tags:

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned.

_uw-theme is based on _tw https://underscoretw.com/, (C) 2021-2025 Greg Sullivan
_tw is distributed under the terms of the GNU GPL v2 or later.

_tw is based on Underscores https://underscores.me/ and Varia https://github.com/Automattic/themes/tree/master/varia, (C) 2012-2025 Automattic, Inc.
Underscores and Varia are distributed under the terms of the GNU GPL v2 or later.
*/
</file>

<file path="tailwind/custom/fonts.css">
/**
 * Custom `@font-face` rules
 *
 * These will be added immediately before Tailwind’s `base` layer.
 */
</file>

<file path="tailwind/custom/utilities.css">
/**
 * Custom styles to immediately follow Tailwind’s `utilities` layer
 *
 * Add your own utility classes to this theme. Complex utility classes should
 * be added using Tailwind’s plugin system:
 *
 * https://tailwindcss.com/docs/adding-custom-styles#adding-custom-utilities
 */
</file>

<file path="tailwind/partials/footer.css">
/**
 * Because this code is used in both `tailwind.css` and `tailwind-editor.css`,
 * we import it from here to avoid duplication.
 */

/**
 * Add Tailwind's component classes and any component classes registered by
 * plugins, then add custom component classes.
 *
 * The `components.css` file is located in a subfolder to allow for additional
 * components files from, e.g., vendor packages. Those files need to be
 * manually added below.
 */
@import "../custom/components/components.css";

/**
 * Add Tailwind's utility classes and any utility classes registered by
 * plugins, then add custom utility classes.
 */
@import "tailwindcss/utilities";
@import "../custom/utilities.css";
</file>

<file path="tailwind/partials/header.css">
/**
 * Because this code is used in both `tailwind.css` and `tailwind-editor.css`,
 * we import it from here to avoid duplication.
 */

/**
 * Add Tailwind Typography and a corresponding config file.
 */
@plugin "@_tw/typography";
@config "../tailwind-typography.config.js";

/**
 * Add the WordPress file header.
 */
@import "../custom/file-header.css";

/**
 * Add both the default Tailwind theme and your custom theme.
 */
@import "../tailwind-theme.css";

/**
 * Add custom `@font-face` rules.
 */
@import "../custom/fonts.css";
</file>

<file path="tailwind/tailwind-editor-extra.css">
/**
 * Additional styles applied only to the WordPress editor
 *
 * It is sometimes necessary to add additional styles for the WordPress block
 * editor, generally to improve the editor experience. For example, you may
 * want to tweak the appearance of the title area to better match your site’s
 * front end, or you may need to change the appearance of a block to better
 * distinguish between its fields while editing.
 *
 * Those styles should be added in this file, and they will be added only to
 * the block editor.
 */

/**
 * This allows you to use classes from both the default Tailwind theme and
 * your custom theme.
 */
@reference "./tailwind-theme.css";

.entry-header,
.entry-content {
	@apply px-8;
}
</file>

<file path="tailwind/tailwind-editor.css">
/**
 * This file outputs to `style-editor.css`, your main block editor style sheet.
 *
 * You probably won’t need to edit this file. You’re more likely to want to
 * edit `./tailwind/tailwind-theme.css`, which contains your project’s custom
 * design tokens, or files in the `./tailwind/custom` folder.
 */

/**
 * Because this file shares code with `tailwind.css`, we import the shared code
 * to avoid duplication.
 */
@import "./partials/header.css";

/**
 * Add your custom base styles.
 */
@import "./custom/base.css";

/**
 * Because this file shares code with `tailwind.css`, we import the shared code
 * to avoid duplication.
 */
@import "./partials/footer.css";
</file>

<file path="tailwind/tailwind-theme.css">
/**
 * Add your design tokens as Tailwind theme variables.
 *
 * https://tailwindcss.com/docs/theme
 */

/**
 * Import Tailwind’s default theme.
 */
@import "tailwindcss/theme";

/**
 * Your theme variables are merged with Tailwind’s defaults, either extending
 * the default theme with new variables or overriding the values of existing
 * variables.
 */
@theme {
	/**
	 * These theme variables use CSS variables set by WordPress using values
	 * from your `theme.json` file.
	 *
	 * If you are using the classic editor, you should use hardcoded color
	 * values instead of the `var()` functions below.
	 */
	--color-background: var(--wp--preset--color--background);
	--color-foreground: var(--wp--preset--color--foreground);
	--color-primary: var(--wp--preset--color--primary);
	--color-secondary: var(--wp--preset--color--secondary);
	--color-tertiary: var(--wp--preset--color--tertiary);
	--container-content: var(--wp--style--global--content-size);
	--container-wide: var(--wp--style--global--wide-size);

	/**
	 * These are the default `font-family` theme variables included with
	 * Tailwind. You can update them below by adding custom fonts at the
	 * beginning of each value.
	 *
	 * Default theme variables can be found in their entirety at the links
	 * below, on GitHub or in Tailwind’s documentation, respectively:
	 *
	 * https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/theme.css
	 * https://tailwindcss.com/docs/theme#default-theme-variable-reference
	 */
	--font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
		"Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
	--font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
	--font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
		"Liberation Mono", "Courier New", monospace;

	/**
	 * Add your theme below:
	 */
}
</file>

<file path="tailwind/tailwind-typography.config.js">
/**
 * Tailwind Typography can only be configured via JavaScript, using a legacy
 * configuration file like this one.
 */

// Copied from Tailwind Typography.
const hexToRgb = (hex) => {
	if (typeof hex !== 'string' || hex.length === 0) {
		return hex;
	}

	hex = hex.replace('#', '');
	hex = hex.length === 3 ? hex.replace(/./g, '$&$&') : hex;
	const r = parseInt(hex.substring(0, 2), 16);
	const g = parseInt(hex.substring(2, 4), 16);
	const b = parseInt(hex.substring(4, 6), 16);
	return `${r} ${g} ${b}`;
};

module.exports = {
	theme: {
		extend: {
			typography: (theme) => ({
				/**
				 * Tailwind Typography’s default styles are opinionated, and
				 * you may need to override them if you have mockups to
				 * replicate. You can view the default modifiers here:
				 *
				 * https://github.com/tailwindlabs/tailwindcss-typography/blob/master/src/styles.js
				 */

				DEFAULT: {
					css: [
						{
							/**
							 * By default, max-width is set to 65 characters.
							 * This is a good default for readability, but
							 * often in conflict with client-supplied designs.
							 */
							maxWidth: 'none',

							/**
							 * Tailwind Typography uses the font weights 400
							 * through 900. If you’re not using a variable font,
							 * you may need to limit the number of supported
							 * weights. Below are all of the default weights,
							 * ready to be overridden.
							 */
							// a: {
							// 	fontWeight: '500',
							// },
							// strong: {
							// 	fontWeight: '600',
							// },
							// 'ol > li::marker': {
							// 	fontWeight: '400',
							// },
							// dt: {
							// 	fontWeight: '600',
							// },
							// blockquote: {
							// 	fontWeight: '500',
							// },
							// h1: {
							// 	fontWeight: '800',
							// },
							// 'h1 strong': {
							// 	fontWeight: '900',
							// },
							// h2: {
							// 	fontWeight: '700',
							// },
							// 'h2 strong': {
							// 	fontWeight: '800',
							// },
							// h3: {
							// 	fontWeight: '600',
							// },
							// 'h3 strong': {
							// 	fontWeight: '700',
							// },
							// h4: {
							// 	fontWeight: '600',
							// },
							// 'h4 strong': {
							// 	fontWeight: '700',
							// },
							// kbd: {
							// 	fontWeight: '500',
							// },
							// code: {
							// 	fontWeight: '600',
							// },
							// pre: {
							// 	fontWeight: '400',
							// },
							// 'thead th': {
							// 	fontWeight: '600',
							// },
						},
					],
				},

				/**
				 * By default, _tw uses Tailwind Typography’s Neutral gray
				 * scale. If you are adapting an existing design and you need
				 * to set specific colors throughout, you can do so here. In
				 * your `./theme/functions.php file, you will need to replace
				 * `-neutral` with `-_uw-theme`.
				 */
				'_uw-theme': {
					css: {
						'--tw-prose-body': theme('colors.foreground'),
						'--tw-prose-headings': theme('colors.foreground'),
						'--tw-prose-lead': theme('colors.foreground'),
						'--tw-prose-links': theme('colors.primary'),
						'--tw-prose-bold': theme('colors.foreground'),
						'--tw-prose-counters': theme('colors.primary'),
						'--tw-prose-bullets': theme('colors.primary'),
						'--tw-prose-hr': theme('colors.foreground'),
						'--tw-prose-quotes': theme('colors.foreground'),
						'--tw-prose-quote-borders': theme('colors.primary'),
						'--tw-prose-captions': theme('colors.foreground'),
						'--tw-prose-kbd': theme('colors.foreground'),
						'--tw-prose-kbd-shadows': hexToRgb(
							theme('colors.foreground')
						),
						'--tw-prose-code': theme('colors.foreground'),
						'--tw-prose-pre-code': theme('colors.background'),
						'--tw-prose-pre-bg': theme('colors.foreground'),
						'--tw-prose-th-borders': theme('colors.foreground'),
						'--tw-prose-td-borders': theme('colors.foreground'),
						'--tw-prose-invert-body': theme('colors.background'),
						'--tw-prose-invert-headings':
							theme('colors.background'),
						'--tw-prose-invert-lead': theme('colors.background'),
						'--tw-prose-invert-links': theme('colors.primary'),
						'--tw-prose-invert-bold': theme('colors.background'),
						'--tw-prose-invert-counters': theme('colors.primary'),
						'--tw-prose-invert-bullets': theme('colors.primary'),
						'--tw-prose-invert-hr': theme('colors.background'),
						'--tw-prose-invert-quotes': theme('colors.background'),
						'--tw-prose-invert-quote-borders':
							theme('colors.primary'),
						'--tw-prose-invert-captions':
							theme('colors.background'),
						'--tw-prose-invert-kbd': theme('colors.background'),
						'--tw-prose-invert-kbd-shadows': hexToRgb(
							theme('colors.background')
						),
						'--tw-prose-invert-code': theme('colors.foreground'),
						'--tw-prose-invert-pre-code':
							theme('colors.background'),
						'--tw-prose-invert-pre-bg': 'rgb(0 0 0 / 50%)',
						'--tw-prose-invert-th-borders':
							theme('colors.background'),
						'--tw-prose-invert-td-borders':
							theme('colors.background'),
					},
				},
			}),
		},
	},
};
</file>

<file path="theme/inc/breadcrumbs.php">
<?php
/**
 * Breadcrumbs functionality for DUM A BYT theme
 *
 * @package _uw-theme
 */

/**
 * Display breadcrumbs for the current page
 */
function dumabyt_breadcrumbs() {
    // Home page
    $home_link = esc_url(home_url('/'));
    $home_text = __('Hlavní stránka', '_uw-theme');
    
    // Start the breadcrumb with a wrapper
    echo '<div class="breadcrumbs">';
    
    // Don't display breadcrumbs on the home page
    if (!is_front_page()) {
        echo '<a href="' . $home_link . '">' . $home_text . '</a>';
        
        if (is_category() || is_single()) {
            echo ' &gt; ';
            
            // Display category
            $categories = get_the_category();
            if (!empty($categories)) {
                echo '<a href="' . esc_url(get_category_link($categories[0]->term_id)) . '">' . esc_html($categories[0]->name) . '</a>';
            }
            
            // If it's a single post, also display the title
            if (is_single()) {
                echo ' &gt; ';
                the_title();
            }
        } elseif (is_page()) {
            global $post;
            // If page has parent
            if ($post && $post->post_parent) {
                $parents = get_post_ancestors($post->ID);
                
                // Display each parent in order
                foreach (array_reverse($parents) as $parent_id) {
                    echo ' &gt; ';
                    echo '<a href="' . get_permalink($parent_id) . '">' . get_the_title($parent_id) . '</a>';
                }
                
                echo ' &gt; ';
                the_title();
            } else {
                // If simple page without parent
                echo ' &gt; ';
                the_title();
            }
        } elseif (is_search()) {
            echo ' &gt; ';
            echo __('Výsledky vyhledávání pro:', '_uw-theme') . ' ' . get_search_query();
        } elseif (is_tag()) {
            echo ' &gt; ';
            echo __('Příspěvky označené:', '_uw-theme') . ' ' . single_tag_title('', false);
        } elseif (is_author()) {
            echo ' &gt; ';
            echo __('Autor:', '_uw-theme') . ' ' . get_the_author();
        } elseif (is_archive()) {
            echo ' &gt; ';
            
            if (is_day()) {
                echo get_the_date();
            } elseif (is_month()) {
                echo get_the_date('F Y');
            } elseif (is_year()) {
                echo get_the_date('Y');
            } else {
                echo __('Archiv', '_uw-theme');
            }
        }
    }
    
    echo '</div>';
}
</file>

<file path="theme/inc/class-dumabyt-walker-nav-menu.php">
<?php
/**
 * Custom Walker class for DUM A BYT navigation menu
 *
 * @package _uw-theme
 */

/**
 * Custom Walker class for multilevel navigation menu
 * 
 * Implements dropdown functionality with proper classes and structure
 */
class Dumabyt_Walker_Nav_Menu extends Walker_Nav_Menu {
    /**
     * Starts the list before the elements are added.
     *
     * @param string   $output Used to append additional content (passed by reference).
     * @param int      $depth  Depth of menu item. Used for padding.
     * @param stdClass $args   An object of wp_nav_menu() arguments.
     */
    public function start_lvl( &$output, $depth = 0, $args = null ) {
        if ( isset( $args->item_spacing ) && 'discard' === $args->item_spacing ) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = str_repeat( $t, $depth );

        // Default class.
        $classes = array( 'sub-menu' );

        /**
         * Filters the CSS class(es) applied to a menu list element.
         *
         * @param string[] $classes Array of the CSS classes that are applied to the menu `<ul>` element.
         * @param stdClass $args    An object of `wp_nav_menu()` arguments.
         * @param int      $depth   Depth of menu item. Used for padding.
         */
        $class_names = implode( ' ', apply_filters( 'nav_menu_submenu_css_class', $classes, $args, $depth ) );
        $class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';

        $output .= "{$n}{$indent}<ul$class_names>{$n}";
    }

    /**
     * Starts the element output.
     *
     * @param string   $output Used to append additional content (passed by reference).
     * @param WP_Post  $item   Menu item data object.
     * @param int      $depth  Depth of menu item. Used for padding.
     * @param stdClass $args   An object of wp_nav_menu() arguments.
     * @param int      $id     Current item ID.
     */
    public function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ) {
        if ( isset( $args->item_spacing ) && 'discard' === $args->item_spacing ) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = ( $depth ) ? str_repeat( $t, $depth ) : '';

        $classes   = empty( $item->classes ) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;
        
        // Add has-children class for parent items
        if (in_array('menu-item-has-children', $classes)) {
            $classes[] = 'has-children';
        }
        
        // Add active class for current menu item
        if (in_array('current-menu-item', $classes)) {
            $classes[] = 'active';
        }

        /**
         * Filters the arguments for a single nav menu item.
         *
         * @param stdClass $args  An object of wp_nav_menu() arguments.
         * @param WP_Post  $item  Menu item data object.
         * @param int      $depth Depth of menu item. Used for padding.
         */
        $args = apply_filters( 'nav_menu_item_args', $args, $item, $depth );

        /**
         * Filters the CSS classes applied to a menu item's list item element.
         *
         * @param string[] $classes Array of the CSS classes that are applied to the menu item's `<li>` element.
         * @param WP_Post  $item    The current menu item.
         * @param stdClass $args    An object of wp_nav_menu() arguments.
         * @param int      $depth   Depth of menu item. Used for padding.
         */
        $class_names = implode( ' ', apply_filters( 'nav_menu_css_class', array_filter( $classes ), $item, $args, $depth ) );
        $class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';

        /**
         * Filters the ID applied to a menu item's list item element.
         *
         * @param string   $menu_id The ID that is applied to the menu item's `<li>` element.
         * @param WP_Post  $item    The current menu item.
         * @param stdClass $args    An object of wp_nav_menu() arguments.
         * @param int      $depth   Depth of menu item. Used for padding.
         */
        $id = apply_filters( 'nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args, $depth );
        $id = $id ? ' id="' . esc_attr( $id ) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names . '>';

        $atts           = array();
        $atts['title']  = ! empty( $item->attr_title ) ? $item->attr_title : '';
        $atts['target'] = ! empty( $item->target ) ? $item->target : '';
        if ( '_blank' === $item->target && empty( $item->xfn ) ) {
            $atts['rel'] = 'noopener';
        } else {
            $atts['rel'] = $item->xfn;
        }
        $atts['href']         = ! empty( $item->url ) ? $item->url : '';
        $atts['aria-current'] = $item->current ? 'page' : '';

        /**
         * Filters the HTML attributes applied to a menu item's anchor element.
         *
         * @param array $atts {
         *     The HTML attributes applied to the menu item's `<a>` element, empty strings are ignored.
         *
         *     @type string $title        Title attribute.
         *     @type string $target       Target attribute.
         *     @type string $rel          The rel attribute.
         *     @type string $href         The href attribute.
         *     @type string $aria-current The aria-current attribute.
         * }
         * @param WP_Post  $item  The current menu item.
         * @param stdClass $args  An object of wp_nav_menu() arguments.
         * @param int      $depth Depth of menu item. Used for padding.
         */
        $atts = apply_filters( 'nav_menu_link_attributes', $atts, $item, $args, $depth );

        $attributes = '';
        foreach ( $atts as $attr => $value ) {
            if ( is_scalar( $value ) && '' !== $value && false !== $value ) {
                $value       = ( 'href' === $attr ) ? esc_url( $value ) : esc_attr( $value );
                $attributes .= ' ' . $attr . '="' . $value . '"';
            }
        }

        /** This filter is documented in wp-includes/post-template.php */
        $title = apply_filters( 'the_title', $item->title, $item->ID );

        /**
         * Filters a menu item's title.
         *
         * @param string   $title The menu item's title.
         * @param WP_Post  $item  The current menu item.
         * @param stdClass $args  An object of wp_nav_menu() arguments.
         * @param int      $depth Depth of menu item. Used for padding.
         */
        $title = apply_filters( 'nav_menu_item_title', $title, $item, $args, $depth );

        $item_output  = $args->before;
        $item_output .= '<a' . $attributes . '>';
        $item_output .= $args->link_before . $title . $args->link_after;
        $item_output .= '</a>';
        $item_output .= $args->after;

        /**
         * Filters a menu item's starting output.
         *
         * The menu item's starting output only includes `$args->before`, the opening `<a>`,
         * the menu item's title, the closing `</a>`, and `$args->after`. Currently, there is
         * no filter for modifying the opening and closing `<li>` for a menu item.
         *
         * @param string   $item_output The menu item's starting HTML output.
         * @param WP_Post  $item        Menu item data object.
         * @param int      $depth       Depth of menu item. Used for padding.
         * @param stdClass $args        An object of wp_nav_menu() arguments.
         */
        $output .= apply_filters( 'walker_nav_menu_start_el', $item_output, $item, $depth, $args );
    }
}
</file>

<file path="theme/inc/template-functions.php">
<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package _uw-theme
 */

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function _uw_theme_pingback_header() {
	if ( is_singular() && pings_open() ) {
		printf( '<link rel="pingback" href="%s">', esc_url( get_bloginfo( 'pingback_url' ) ) );
	}
}
add_action( 'wp_head', '_uw_theme_pingback_header' );

/**
 * Changes comment form default fields.
 *
 * @param array $defaults The default comment form arguments.
 *
 * @return array Returns the modified fields.
 */
function _uw_theme_comment_form_defaults( $defaults ) {
	$comment_field = $defaults['comment_field'];

	// Adjust height of comment form.
	$defaults['comment_field'] = preg_replace( '/rows="\d+"/', 'rows="5"', $comment_field );

	return $defaults;
}
add_filter( 'comment_form_defaults', '_uw_theme_comment_form_defaults' );

/**
 * Filters the default archive titles.
 */
function _uw_theme_get_the_archive_title() {
	if ( is_category() ) {
		$title = __( 'Category Archives: ', '_uw-theme' ) . '<span>' . single_term_title( '', false ) . '</span>';
	} elseif ( is_tag() ) {
		$title = __( 'Tag Archives: ', '_uw-theme' ) . '<span>' . single_term_title( '', false ) . '</span>';
	} elseif ( is_author() ) {
		$title = __( 'Author Archives: ', '_uw-theme' ) . '<span>' . get_the_author_meta( 'display_name' ) . '</span>';
	} elseif ( is_year() ) {
		$title = __( 'Yearly Archives: ', '_uw-theme' ) . '<span>' . get_the_date( _x( 'Y', 'yearly archives date format', '_uw-theme' ) ) . '</span>';
	} elseif ( is_month() ) {
		$title = __( 'Monthly Archives: ', '_uw-theme' ) . '<span>' . get_the_date( _x( 'F Y', 'monthly archives date format', '_uw-theme' ) ) . '</span>';
	} elseif ( is_day() ) {
		$title = __( 'Daily Archives: ', '_uw-theme' ) . '<span>' . get_the_date() . '</span>';
	} elseif ( is_post_type_archive() ) {
		$cpt   = get_post_type_object( get_queried_object()->name );
		$title = sprintf(
			/* translators: %s: Post type singular name */
			esc_html__( '%s Archives', '_uw-theme' ),
			$cpt->labels->singular_name
		);
	} elseif ( is_tax() ) {
		$tax   = get_taxonomy( get_queried_object()->taxonomy );
		$title = sprintf(
			/* translators: %s: Taxonomy singular name */
			esc_html__( '%s Archives', '_uw-theme' ),
			$tax->labels->singular_name
		);
	} else {
		$title = __( 'Archives:', '_uw-theme' );
	}
	return $title;
}
add_filter( 'get_the_archive_title', '_uw_theme_get_the_archive_title' );

/**
 * Determines whether the post thumbnail can be displayed.
 */
function _uw_theme_can_show_post_thumbnail() {
	return apply_filters( '_uw_theme_can_show_post_thumbnail', ! post_password_required() && ! is_attachment() && has_post_thumbnail() );
}

/**
 * Returns the size for avatars used in the theme.
 */
function _uw_theme_get_avatar_size() {
	return 60;
}

/**
 * Create the continue reading link
 *
 * @param string $more_string The string shown within the more link.
 */
function _uw_theme_continue_reading_link( $more_string ) {

	if ( ! is_admin() ) {
		$continue_reading = sprintf(
			/* translators: %s: Name of current post. */
			wp_kses( __( 'Continue reading %s', '_uw-theme' ), array( 'span' => array( 'class' => array() ) ) ),
			the_title( '<span class="sr-only">"', '"</span>', false )
		);

		$more_string = '<a href="' . esc_url( get_permalink() ) . '">' . $continue_reading . '</a>';
	}

	return $more_string;
}

// Filter the excerpt more link.
add_filter( 'excerpt_more', '_uw_theme_continue_reading_link' );

// Filter the content more link.
add_filter( 'the_content_more_link', '_uw_theme_continue_reading_link' );

/**
 * Outputs a comment in the HTML5 format.
 *
 * This function overrides the default WordPress comment output in HTML5
 * format, adding the required class for Tailwind Typography. Based on the
 * `html5_comment()` function from WordPress core.
 *
 * @param WP_Comment $comment Comment to display.
 * @param array      $args    An array of arguments.
 * @param int        $depth   Depth of the current comment.
 */
function _uw_theme_html5_comment( $comment, $args, $depth ) {
	$tag = ( 'div' === $args['style'] ) ? 'div' : 'li';

	$commenter          = wp_get_current_commenter();
	$show_pending_links = ! empty( $commenter['comment_author'] );

	if ( $commenter['comment_author_email'] ) {
		$moderation_note = __( 'Your comment is awaiting moderation.', '_uw-theme' );
	} else {
		$moderation_note = __( 'Your comment is awaiting moderation. This is a preview; your comment will be visible after it has been approved.', '_uw-theme' );
	}
	?>
	<<?php echo esc_attr( $tag ); ?> id="comment-<?php comment_ID(); ?>" <?php comment_class( $comment->has_children ? 'parent' : '', $comment ); ?>>
		<article id="div-comment-<?php comment_ID(); ?>" class="comment-body">
			<footer class="comment-meta">
				<div class="comment-author vcard">
					<?php
					if ( 0 !== $args['avatar_size'] ) {
						echo get_avatar( $comment, $args['avatar_size'] );
					}
					?>
					<?php
					$comment_author = get_comment_author_link( $comment );

					if ( '0' === $comment->comment_approved && ! $show_pending_links ) {
						$comment_author = get_comment_author( $comment );
					}

					printf(
						/* translators: %s: Comment author link. */
						wp_kses_post( __( '%s <span class="says">says:</span>', '_uw-theme' ) ),
						sprintf( '<b class="fn">%s</b>', wp_kses_post( $comment_author ) )
					);
					?>
				</div><!-- .comment-author -->

				<div class="comment-metadata">
					<?php
					printf(
						'<a href="%s"><time datetime="%s">%s</time></a>',
						esc_url( get_comment_link( $comment, $args ) ),
						esc_attr( get_comment_time( 'c' ) ),
						esc_html(
							sprintf(
							/* translators: 1: Comment date, 2: Comment time. */
								__( '%1$s at %2$s', '_uw-theme' ),
								get_comment_date( '', $comment ),
								get_comment_time()
							)
						)
					);

					edit_comment_link( __( 'Edit', '_uw-theme' ), ' <span class="edit-link">', '</span>' );
					?>
				</div><!-- .comment-metadata -->

				<?php if ( '0' === $comment->comment_approved ) : ?>
				<em class="comment-awaiting-moderation"><?php echo esc_html( $moderation_note ); ?></em>
				<?php endif; ?>
			</footer><!-- .comment-meta -->

			<div <?php _uw_theme_content_class( 'comment-content' ); ?>>
				<?php comment_text(); ?>
			</div><!-- .comment-content -->

			<?php
			if ( '1' === $comment->comment_approved || $show_pending_links ) {
				comment_reply_link(
					array_merge(
						$args,
						array(
							'add_below' => 'div-comment',
							'depth'     => $depth,
							'max_depth' => $args['max_depth'],
							'before'    => '<div class="reply">',
							'after'     => '</div>',
						)
					)
				);
			}
			?>
		</article><!-- .comment-body -->
	<?php
}
</file>

<file path="theme/inc/template-tags.php">
<?php
/**
 * Custom template tags for this theme
 *
 * Eventually, some functionality here could be replaced by core features.
 *
 * @package _uw-theme
 */

if ( ! function_exists( '_uw_theme_posted_on' ) ) :
	/**
	 * Prints HTML with meta information for the current post-date/time.
	 */
	function _uw_theme_posted_on() {
		$time_string = '<time datetime="%1$s">%2$s</time>';
		if ( get_the_time( 'U' ) !== get_the_modified_time( 'U' ) ) {
			$time_string = '<time datetime="%1$s">%2$s</time><time datetime="%3$s">%4$s</time>';
		}

		$time_string = sprintf(
			$time_string,
			esc_attr( get_the_date( DATE_W3C ) ),
			esc_html( get_the_date() ),
			esc_attr( get_the_modified_date( DATE_W3C ) ),
			esc_html( get_the_modified_date() )
		);

		printf(
			'<a href="%1$s" rel="bookmark">%2$s</a>',
			esc_url( get_permalink() ),
			$time_string // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		);
	}
endif;

if ( ! function_exists( '_uw_theme_posted_by' ) ) :
	/**
	 * Prints HTML with meta information about theme author.
	 */
	function _uw_theme_posted_by() {
		printf(
		/* translators: 1: posted by label, only visible to screen readers. 2: author link. 3: post author. */
			'<span class="sr-only">%1$s</span><span class="author vcard"><a class="url fn n" href="%2$s">%3$s</a></span>',
			esc_html__( 'Posted by', '_uw-theme' ),
			esc_url( get_author_posts_url( get_the_author_meta( 'ID' ) ) ),
			esc_html( get_the_author() )
		);
	}
endif;

if ( ! function_exists( '_uw_theme_comment_count' ) ) :
	/**
	 * Prints HTML with the comment count for the current post.
	 */
	function _uw_theme_comment_count() {
		if ( ! post_password_required() && ( comments_open() || get_comments_number() ) ) {
			/* translators: %s: Name of current post. Only visible to screen readers. */
			comments_popup_link( sprintf( __( 'Leave a comment<span class="sr-only"> on %s</span>', '_uw-theme' ), get_the_title() ) );
		}
	}
endif;

if ( ! function_exists( '_uw_theme_entry_meta' ) ) :
	/**
	 * Prints HTML with meta information for the categories, tags and comments.
	 * This template tag is used in the entry header.
	 */
	function _uw_theme_entry_meta() {

		// Hide author, post date, category and tag text for pages.
		if ( 'post' === get_post_type() ) {

			// Posted by.
			_uw_theme_posted_by();

			// Posted on.
			_uw_theme_posted_on();

			/* translators: used between list items, there is a space after the comma. */
			$categories_list = get_the_category_list( __( ', ', '_uw-theme' ) );
			if ( $categories_list ) {
				printf(
				/* translators: 1: posted in label, only visible to screen readers. 2: list of categories. */
					'<span class="sr-only">%1$s</span>%2$s',
					esc_html__( 'Posted in', '_uw-theme' ),
					$categories_list // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
				);
			}

			/* translators: used between list items, there is a space after the comma. */
			$tags_list = get_the_tag_list( '', __( ', ', '_uw-theme' ) );
			if ( $tags_list ) {
				printf(
				/* translators: 1: tags label, only visible to screen readers. 2: list of tags. */
					'<span class="sr-only">%1$s</span>%2$s',
					esc_html__( 'Tags:', '_uw-theme' ),
					$tags_list // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
				);
			}
		}

		// Comment count.
		if ( ! is_singular() ) {
			_uw_theme_comment_count();
		}

		// Edit post link.
		edit_post_link(
			sprintf(
				wp_kses(
				/* translators: %s: Name of current post. Only visible to screen readers. */
					__( 'Edit <span class="sr-only">%s</span>', '_uw-theme' ),
					array(
						'span' => array(
							'class' => array(),
						),
					)
				),
				get_the_title()
			)
		);
	}
endif;

if ( ! function_exists( '_uw_theme_entry_footer' ) ) :
	/**
	 * Prints HTML with meta information for the categories, tags and comments.
	 */
	function _uw_theme_entry_footer() {

		// Hide author, post date, category and tag text for pages.
		if ( 'post' === get_post_type() ) {

			// Posted by.
			_uw_theme_posted_by();

			// Posted on.
			_uw_theme_posted_on();

			/* translators: used between list items, there is a space after the comma. */
			$categories_list = get_the_category_list( __( ', ', '_uw-theme' ) );
			if ( $categories_list ) {
				printf(
				/* translators: 1: posted in label, only visible to screen readers. 2: list of categories. */
					'<span class="sr-only">%1$s</span>%2$s',
					esc_html__( 'Posted in', '_uw-theme' ),
					$categories_list // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
				);
			}

			/* translators: used between list items, there is a space after the comma. */
			$tags_list = get_the_tag_list( '', __( ', ', '_uw-theme' ) );
			if ( $tags_list ) {
				printf(
				/* translators: 1: tags label, only visible to screen readers. 2: list of tags. */
					'<span class="sr-only">%1$s</span>%2$s',
					esc_html__( 'Tags:', '_uw-theme' ),
					$tags_list // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
				);
			}
		}

		// Comment count.
		if ( ! is_singular() ) {
			_uw_theme_comment_count();
		}

		// Edit post link.
		edit_post_link(
			sprintf(
				wp_kses(
				/* translators: %s: Name of current post. Only visible to screen readers. */
					__( 'Edit <span class="sr-only">%s</span>', '_uw-theme' ),
					array(
						'span' => array(
							'class' => array(),
						),
					)
				),
				get_the_title()
			)
		);
	}
endif;

if ( ! function_exists( '_uw_theme_post_thumbnail' ) ) :
	/**
	 * Displays an optional post thumbnail, wrapping the post thumbnail in an
	 * anchor element except when viewing a single post.
	 */
	function _uw_theme_post_thumbnail() {
		if ( ! _uw_theme_can_show_post_thumbnail() ) {
			return;
		}

		if ( is_singular() ) :
			?>

			<figure>
				<?php the_post_thumbnail(); ?>
			</figure><!-- .post-thumbnail -->

			<?php
		else :
			?>

			<figure>
				<a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
					<?php the_post_thumbnail(); ?>
				</a>
			</figure>

			<?php
		endif; // End is_singular().
	}
endif;

if ( ! function_exists( '_uw_theme_comment_avatar' ) ) :
	/**
	 * Returns the HTML markup to generate a user avatar.
	 *
	 * @param mixed $id_or_email The Gravatar to retrieve. Accepts a user_id, gravatar md5 hash,
	 *                           user email, WP_User object, WP_Post object, or WP_Comment object.
	 */
	function _uw_theme_get_user_avatar_markup( $id_or_email = null ) {

		if ( ! isset( $id_or_email ) ) {
			$id_or_email = get_current_user_id();
		}

		return sprintf( '<div class="vcard">%s</div>', get_avatar( $id_or_email, _uw_theme_get_avatar_size() ) );
	}
endif;

if ( ! function_exists( '_uw_theme_discussion_avatars_list' ) ) :
	/**
	 * Displays a list of avatars involved in a discussion for a given post.
	 *
	 * @param array $comment_authors Comment authors to list as avatars.
	 */
	function _uw_theme_discussion_avatars_list( $comment_authors ) {
		if ( empty( $comment_authors ) ) {
			return;
		}
		echo '<ol>', "\n";
		foreach ( $comment_authors as $id_or_email ) {
			printf(
				"<li>%s</li>\n",
				_uw_theme_get_user_avatar_markup( $id_or_email ) // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
			);
		}
		echo '</ol>', "\n";
	}
endif;

if ( ! function_exists( '_uw_theme_the_posts_navigation' ) ) :
	/**
	 * Wraps `the_posts_pagination` for use throughout the theme.
	 */
	function _uw_theme_the_posts_navigation() {
		the_posts_pagination(
			array(
				'mid_size'  => 2,
				'prev_text' => __( 'Newer posts', '_uw-theme' ),
				'next_text' => __( 'Older posts', '_uw-theme' ),
			)
		);
	}
endif;

if ( ! function_exists( '_uw_theme_content_class' ) ) :
	/**
	 * Displays the class names for the post content wrapper.
	 *
	 * This allows us to add Tailwind Typography’s modifier classes throughout
	 * the theme without repeating them in multiple files. (They can be edited
	 * at the top of the `../functions.php` file via the
	 * _UW_THEME_TYPOGRAPHY_CLASSES constant.)
	 *
	 * Based on WordPress core’s `body_class` and `get_body_class` functions.
	 *
	 * @param string|string[] $classes Space-separated string or array of class
	 *                                 names to add to the class list.
	 */
	function _uw_theme_content_class( $classes = '' ) {
		$all_classes = array( $classes, _UW_THEME_TYPOGRAPHY_CLASSES );

		foreach ( $all_classes as &$class_groups ) {
			if ( ! empty( $class_groups ) ) {
				if ( ! is_array( $class_groups ) ) {
					$class_groups = preg_split( '#\s+#', $class_groups );
				}
			} else {
				// Ensure that we always coerce class to being an array.
				$class_groups = array();
			}
		}

		$combined_classes = array_merge( $all_classes[0], $all_classes[1] );
		$combined_classes = array_map( 'esc_attr', $combined_classes );

		// Separates class names with a single space, preparing them for the
		// post content wrapper.
		echo 'class="' . esc_attr( implode( ' ', $combined_classes ) ) . '"';
	}
endif;
</file>

<file path="theme/js/block-editor.min.js">
(()=>{var a={"edit-post-visual-editor__post-title-wrapper":["entry-header"],"wp-block-post-title":["entry-title"],"wp-block-post-content":["entry-content",...tailwindTypographyClasses]};wp.domReady(()=>{l()});function n(){let t=null;for(let e of document.body.classList)if(e.startsWith("post-type-")){t=e;break}return t}function l(){let t=setInterval(function(){Object.keys(a).every(e=>document.getElementsByClassName(e).length)?(n()&&Object.values(a).forEach(e=>e.push(n())),Object.entries(a).forEach(([e,s])=>{document.getElementsByClassName(e)[0].classList.add(...s)}),Object.keys(a).forEach(e=>{i.observe(document.querySelector("."+e),{attributes:!0,attributeFilter:["class"]})}),clearInterval(t)):document.getElementsByName("editor-canvas").length&&clearInterval(t)},40)}var i=new MutationObserver(function(t){t.forEach(function(e){let s=e.target.classList;Object.entries(a).forEach(([o,r])=>{s.contains(o)&&(r.every(c=>s.contains(c))||s.add(...r))})})});wp.domReady(()=>{wp.blocks.registerBlockStyle("core/paragraph",{name:"lead",label:"Lead"})});})();
</file>

<file path="theme/js/readme.txt">
The two JavaScript files at `../../javascript` will be processed by esbuild,
and the output files will be created in this folder with `.min.js` extensions.
The files `script.min.js` and `block-editor.min.js` are enqueued by default in
`../functions.php`.

If you would like to add new files to be processed by esbuild, add them to the
`../../javascript` folder and then add them to the `development:esbuild` key in
your `package.json` file.

DO NOT directly edit `*.min.js` files, as these files are ignored by git and
will be overwritten the next time esbuild runs.
</file>

<file path="theme/js/script.min.js">
(()=>{document.addEventListener("DOMContentLoaded",function(){let n=document.querySelector(".mobile-menu-toggle"),o=document.querySelector(".mobile-menu");n&&o&&n.addEventListener("click",function(){o.classList.toggle("hidden");let t=n.getAttribute("aria-expanded")==="true";n.setAttribute("aria-expanded",!t)}),document.querySelectorAll(".mobile-menu .menu-item-has-children").forEach(function(t){let e=document.createElement("button");e.classList.add("submenu-toggle"),e.setAttribute("aria-expanded","false"),e.innerHTML='<span class="sr-only">Toggle submenu</span>+';let u=t.querySelector("a");u&&(u.parentNode.insertBefore(e,u.nextSibling),e.addEventListener("click",function(r){r.preventDefault();let i=t.querySelector(".sub-menu");if(i){i.classList.toggle("hidden");let a=e.getAttribute("aria-expanded")==="true";e.setAttribute("aria-expanded",!a),e.innerHTML=a?'<span class="sr-only">Toggle submenu</span>+':'<span class="sr-only">Toggle submenu</span>-'}}))})});document.addEventListener("DOMContentLoaded",function(){let n=document.getElementById("primary-menu");n&&n.querySelectorAll(".menu-item-has-children").forEach(function(s){let t=s.querySelector("a");if(t){let e=document.createElement("span");e.classList.add("dropdown-indicator"),e.innerHTML="\u25BC",t.appendChild(e)}s.addEventListener("mouseenter",function(){let e=s.querySelector(".sub-menu");e&&e.classList.add("active")}),s.addEventListener("mouseleave",function(){let e=s.querySelector(".sub-menu");e&&e.classList.remove("active")})})});})();
</file>

<file path="theme/js/slideshow.min.js">
/**
 * Slideshow functionality for DŮM A BYT homepage
 */
document.addEventListener("DOMContentLoaded",function(){const e=document.querySelector(".slideshow-container");if(!e)return;const t=e.querySelectorAll(".slide");if(!t.length)return;let s=e.querySelector(".slideshow-dots");s||(s=document.createElement("div"),s.className="slideshow-dots absolute bottom-4 right-4 flex space-x-2 z-10",e.appendChild(s),t.forEach((e,t)=>{const a=document.createElement("button");a.className="dot w-3 h-3 rounded-full bg-white/50 hover:bg-white/80 transition-colors duration-300",a.setAttribute("aria-label",`Přejít na slide ${t+1}`),a.dataset.slideIndex=t,s.appendChild(a)}));const a=s.querySelectorAll(".dot");let n=0;function o(){t.forEach((e,t)=>{t===n?(e.classList.remove("opacity-0","translate-x-full","-translate-x-full"),e.classList.add("opacity-100","translate-x-0")):(e.classList.remove("opacity-100","translate-x-0"),e.classList.add("opacity-0"),t>n?(e.classList.add("translate-x-full"),e.classList.remove("-translate-x-full")):(e.classList.add("-translate-x-full"),e.classList.remove("translate-x-full")))}),a.forEach((e,t)=>{t===n?(e.classList.remove("bg-white/50"),e.classList.add("bg-white"),e.setAttribute("aria-current","true")):(e.classList.remove("bg-white"),e.classList.add("bg-white/50"),e.removeAttribute("aria-current"))})}o();let l=setInterval(function(){n=(n+1)%t.length,o()},6e3);function i(){clearInterval(l),l=setInterval(function(){n=(n+1)%t.length,o()},6e3)}a.forEach(e=>{e.addEventListener("click",function(){n=parseInt(this.dataset.slideIndex),o(),i()})}),e.setAttribute("tabindex","0"),e.addEventListener("keydown",function(e){"ArrowLeft"===e.key?(n=(n-1+t.length)%t.length,o(),i()):"ArrowRight"===e.key&&(n=(n+1)%t.length,o(),i())});let r=0,c=0;e.addEventListener("touchstart",e=>{r=e.changedTouches[0].screenX},{passive:!0}),e.addEventListener("touchend",e=>{c=e.changedTouches[0].screenX,c<r-50?(n=(n+1)%t.length,o(),i()):c>r+50&&(n=(n-1+t.length)%t.length,o(),i())},{passive:!0})});
</file>

<file path="theme/languages/readme.txt">
Place your theme’s language files in this directory.

Please visit the following links to learn more about translating WordPress themes:

https://make.wordpress.org/polyglots/teams/
https://developer.wordpress.org/themes/functionality/localization/
https://developer.wordpress.org/reference/functions/load_theme_textdomain/
</file>

<file path="theme/template-parts/content/content-excerpt.php">
<?php
/**
 * Template part for displaying post archives and search results
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>

	<header class="entry-header">
		<?php
		if ( is_sticky() && is_home() && ! is_paged() ) {
			printf( '%s', esc_html_x( 'Featured', 'post', '_uw-theme' ) );
		}
		the_title( sprintf( '<h2 class="entry-title"><a href="%s" rel="bookmark">', esc_url( get_permalink() ) ), '</a></h2>' );
		?>
	</header><!-- .entry-header -->

	<?php _uw_theme_post_thumbnail(); ?>

	<div <?php _uw_theme_content_class( 'entry-content' ); ?>>
		<?php the_excerpt(); ?>
	</div><!-- .entry-content -->

	<footer class="entry-footer">
		<?php _uw_theme_entry_footer(); ?>
	</footer><!-- .entry-footer -->

</article><!-- #post-${ID} -->
</file>

<file path="theme/template-parts/content/content-none.php">
<?php
/**
 * Template part for displaying a message when posts are not found
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

?>

<section>

	<header class="page-header">
		<?php if ( is_search() ) : ?>

			<h1 class="page-title">
				<?php
				printf(
					/* translators: 1: search result title. 2: search term. */
					'<h1 class="page-title">%1$s <span>%2$s</span></h1>',
					esc_html__( 'Search results for:', '_uw-theme' ),
					get_search_query()
				);
				?>
			</h1>

		<?php else : ?>

			<h1 class="page-title"><?php esc_html_e( 'Nothing Found', '_uw-theme' ); ?></h1>

		<?php endif; ?>
	</header><!-- .page-header -->

	<div <?php _uw_theme_content_class( 'page-content' ); ?>>
		<?php
		if ( is_home() && current_user_can( 'publish_posts' ) ) :
			?>

			<p>
				<?php esc_html_e( 'Your site is set to show the most recent posts on your homepage, but you haven&rsquo;t published any posts.', '_uw-theme' ); ?>
			</p>

			<p>
				<a href="<?php echo esc_url( admin_url( 'edit.php' ) ); ?>">
					<?php
					/* translators: 1: link to WP admin new post page. */
					esc_html_e( 'Add or publish posts', '_uw-theme' );
					?>
				</a>
			</p>

			<?php
		elseif ( is_search() ) :
			?>

			<p>
				<?php esc_html_e( 'Your search generated no results. Please try a different search.', '_uw-theme' ); ?>
			</p>

			<?php
			get_search_form();
		else :
			?>

			<p>
				<?php esc_html_e( 'No content matched your request.', '_uw-theme' ); ?>
			</p>

			<?php
			get_search_form();
		endif;
		?>
	</div><!-- .page-content -->

</section>
</file>

<file path="theme/template-parts/content/content-page.php">
<?php
/**
 * Template part for displaying pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>

	<header class="entry-header">
		<?php
		if ( ! is_front_page() ) {
			the_title( '<h1 class="entry-title">', '</h1>' );
		} else {
			the_title( '<h2 class="entry-title">', '</h2>' );
		}
		?>
	</header><!-- .entry-header -->

	<?php _uw_theme_post_thumbnail(); ?>

	<div <?php _uw_theme_content_class( 'entry-content' ); ?>>
		<?php
		the_content();

		wp_link_pages(
			array(
				'before' => '<div>' . __( 'Pages:', '_uw-theme' ),
				'after'  => '</div>',
			)
		);
		?>
	</div><!-- .entry-content -->

	<?php if ( get_edit_post_link() ) : ?>
		<footer class="entry-footer">
			<?php
			edit_post_link(
				sprintf(
					wp_kses(
						/* translators: %s: Name of current post. Only visible to screen readers. */
						__( 'Edit <span class="sr-only">%s</span>', '_uw-theme' ),
						array(
							'span' => array(
								'class' => array(),
							),
						)
					),
					get_the_title()
				)
			);
			?>
		</footer><!-- .entry-footer -->
	<?php endif; ?>

</article><!-- #post-<?php the_ID(); ?> -->
</file>

<file path="theme/template-parts/content/content-single.php">
<?php
/**
 * Template part for displaying single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>

	<header class="entry-header">
		<?php the_title( '<h1 class="entry-title">', '</h1>' ); ?>

		<?php if ( ! is_page() ) : ?>
			<div class="entry-meta">
				<?php _uw_theme_entry_meta(); ?>
			</div><!-- .entry-meta -->
		<?php endif; ?>
	</header><!-- .entry-header -->

	<?php _uw_theme_post_thumbnail(); ?>

	<div <?php _uw_theme_content_class( 'entry-content' ); ?>>
		<?php
		the_content(
			sprintf(
				wp_kses(
					/* translators: %s: Name of current post. Only visible to screen readers. */
					__( 'Continue reading<span class="sr-only"> "%s"</span>', '_uw-theme' ),
					array(
						'span' => array(
							'class' => array(),
						),
					)
				),
				get_the_title()
			)
		);

		wp_link_pages(
			array(
				'before' => '<div>' . __( 'Pages:', '_uw-theme' ),
				'after'  => '</div>',
			)
		);
		?>
	</div><!-- .entry-content -->

	<footer class="entry-footer">
		<?php _uw_theme_entry_footer(); ?>
	</footer><!-- .entry-footer -->

</article><!-- #post-${ID} -->
</file>

<file path="theme/template-parts/content/content.php">
<?php
/**
 * Template part for displaying posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>

	<header class="entry-header">
		<?php
		if ( is_sticky() && is_home() && ! is_paged() ) {
			printf( '<span">%s</span>', esc_html_x( 'Featured', 'post', '_uw-theme' ) );
		}
		if ( is_singular() ) :
			the_title( '<h1 class="entry-title">', '</h1>' );
		else :
			the_title( sprintf( '<h2 class="entry-title"><a href="%s" rel="bookmark">', esc_url( get_permalink() ) ), '</a></h2>' );
		endif;
		?>
	</header><!-- .entry-header -->

	<?php _uw_theme_post_thumbnail(); ?>

	<div <?php _uw_theme_content_class( 'entry-content' ); ?>>
		<?php
		the_content();

		wp_link_pages(
			array(
				'before' => '<div>' . __( 'Pages:', '_uw-theme' ),
				'after'  => '</div>',
			)
		);
		?>
	</div><!-- .entry-content -->

	<footer class="entry-footer">
		<?php _uw_theme_entry_footer(); ?>
	</footer><!-- .entry-footer -->

</article><!-- #post-${ID} -->
</file>

<file path="theme/template-parts/layout/footer-content.php">
<?php
/**
 * Template part for displaying the footer content
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

?>

<footer id="colophon">

	<?php if ( is_active_sidebar( 'sidebar-1' ) ) : ?>
		<aside role="complementary" aria-label="<?php esc_attr_e( 'Footer', '_uw-theme' ); ?>">
			<?php dynamic_sidebar( 'sidebar-1' ); ?>
		</aside>
	<?php endif; ?>

	<?php if ( has_nav_menu( 'menu-2' ) ) : ?>
		<nav aria-label="<?php esc_attr_e( 'Footer Menu', '_uw-theme' ); ?>">
			<?php
			wp_nav_menu(
				array(
					'theme_location' => 'menu-2',
					'menu_class'     => 'footer-menu',
					'depth'          => 1,
				)
			);
			?>
		</nav>
	<?php endif; ?>

	<div>
		<?php
		$_uw_theme_blog_info = get_bloginfo( 'name' );
		if ( ! empty( $_uw_theme_blog_info ) ) :
			?>
			<a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home"><?php bloginfo( 'name' ); ?></a>,
			<?php
		endif;

		/* translators: 1: WordPress link, 2: WordPress. */
		printf(
			'<a href="%1$s">proudly powered by %2$s</a>.',
			esc_url( __( 'https://wordpress.org/', '_uw-theme' ) ),
			'WordPress'
		);
		?>
	</div>

</footer><!-- #colophon -->
</file>

<file path="theme/template-parts/layout/header-content.php">
<?php
/**
 * Template part for displaying the header content
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

?>

<header id="masthead" class="relative">
    <!-- Top header with logo and secondary links -->
    <div class="top-header border-b border-gray-200">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <!-- Logo with subtitle -->
            <div class="logo-container">
                <?php if ( is_front_page() ) : ?>
                <h1 class="text-3xl font-bold text-gray-600">
                    <a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home" class="flex flex-col">
                        <span class="text-3xl tracking-wider">DŮM & BYT</span>
                        <span class="text-sm text-gray-500 mt-1">bydlení | stavba | zahrada</span>
                    </a>
                </h1>
                <?php else : ?>
                <p class="text-3xl font-bold text-gray-600">
                    <a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home" class="flex flex-col">
                        <span class="text-3xl tracking-wider">DŮM & BYT</span>
                        <span class="text-sm text-gray-500 mt-1">bydlení | stavba | zahrada</span>
                    </a>
                </p>
                <?php endif; ?>
            </div>
            
            <!-- Top links and magazine image -->
            <div class="top-links flex items-center space-x-4">
                <a href="<?php echo esc_url( home_url( '/prihlaseni' ) ); ?>" class="text-sm uppercase text-gray-600 hover:text-gray-900">Přihlášení</a>
                <a href="<?php echo esc_url( home_url( '/newsletter' ) ); ?>" class="text-sm uppercase text-gray-600 hover:text-gray-900">Newsletter</a>
                <a href="<?php echo esc_url( home_url( '/o-nas' ) ); ?>" class="text-sm uppercase text-gray-600 hover:text-gray-900">O nás</a>
                <a href="<?php echo esc_url( home_url( '/casopis' ) ); ?>" class="ml-4">
                    <?php 
                    $magazine_img = get_theme_file_uri( '/screenshot.png' );
                    if ( $magazine_img ) : 
                    ?>
                    <img src="<?php echo esc_url( $magazine_img ); ?>" alt="<?php esc_attr_e( 'Časopis', '_uw-theme' ); ?>" class="h-16 w-auto">
                    <?php endif; ?>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Search bar -->
    <div class="search-bar bg-gray-100 border-b border-gray-200">
        <div class="container mx-auto px-4 py-2">
            <?php get_search_form(); ?>
        </div>
    </div>
    
    <!-- Main navigation -->
    <nav id="site-navigation" class="main-navigation bg-white border-b border-gray-200" aria-label="<?php esc_attr_e( 'Main Navigation', '_uw-theme' ); ?>">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center">
                <!-- Mobile menu button -->
                <button class="mobile-menu-toggle md:hidden" aria-controls="primary-menu" aria-expanded="false">
                    <span class="sr-only"><?php esc_html_e( 'Primary Menu', '_uw-theme' ); ?></span>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
                    </svg>
                </button>
                
                <!-- Desktop navigation menu -->
                <div class="hidden md:block w-full">
                    <?php
                    wp_nav_menu(
                        array(
                            'theme_location' => 'menu-1',
                            'menu_id'        => 'primary-menu',
                            'menu_class'     => 'flex',
                            'container'      => false,
                            'fallback_cb'    => false,
                            'walker'         => new Dumabyt_Walker_Nav_Menu(),
                            'items_wrap'     => '<ul id="%1$s" class="%2$s" aria-label="submenu">%3$s</ul>',
                        )
                    );
                    ?>
                </div>
            </div>
        </div>
        
        <!-- Mobile navigation menu (hidden by default) -->
        <div class="mobile-menu hidden md:hidden">
            <?php
            wp_nav_menu(
                array(
                    'theme_location' => 'menu-1',
                    'menu_id'        => 'mobile-menu',
                    'menu_class'     => 'flex flex-col',
                    'container'      => false,
                    'fallback_cb'    => false,
                    'walker'         => new Dumabyt_Walker_Nav_Menu(),
                )
            );
            ?>
        </div>
    </nav>
    
    <!-- Breadcrumbs -->
    <div class="breadcrumbs-container bg-gray-100 border-b border-gray-200">
        <div class="container mx-auto px-4 py-2 text-sm text-gray-600">
            <?php dumabyt_breadcrumbs(); ?>
        </div>
    </div>
</header><!-- #masthead -->
</file>

<file path="theme/404.php">
<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @link https://codex.wordpress.org/Creating_an_Error_404_Page
 *
 * @package _uw-theme
 */

get_header();
?>

	<section id="primary">
		<main id="main">

			<div>
				<header class="page-header">
					<h1 class="page-title"><?php esc_html_e( 'Page Not Found', '_uw-theme' ); ?></h1>
				</header><!-- .page-header -->

				<div <?php _uw_theme_content_class( 'page-content' ); ?>>
					<p><?php esc_html_e( 'This page could not be found. It might have been removed or renamed, or it may never have existed.', '_uw-theme' ); ?></p>
					<?php get_search_form(); ?>
				</div><!-- .page-content -->
			</div>

		</main><!-- #main -->
	</section><!-- #primary -->

<?php
get_footer();
</file>

<file path="theme/archive.php">
<?php
/**
 * The template for displaying archive pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

get_header();
?>

	<section id="primary">
		<main id="main">

		<?php if ( have_posts() ) : ?>

			<header class="page-header">
				<?php the_archive_title( '<h1 class="page-title">', '</h1>' ); ?>
			</header><!-- .page-header -->

			<?php
			// Start the Loop.
			while ( have_posts() ) :
				the_post();
				get_template_part( 'template-parts/content/content', 'excerpt' );

				// End the loop.
			endwhile;

			// Previous/next page navigation.
			_uw_theme_the_posts_navigation();

		else :

			// If no content, include the "No posts found" template.
			get_template_part( 'template-parts/content/content', 'none' );

		endif;
		?>
		</main><!-- #main -->
	</section><!-- #primary -->

<?php
get_footer();
</file>

<file path="theme/comments.php">
<?php
/**
 * The template for displaying comments
 *
 * This is the template that displays the area of the page that contains both
 * the current comments and the comment form.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

/*
 * If the current post is protected by a password and the visitor has not yet
 * entered the password we will return early without loading the comments.
 */
if ( post_password_required() ) {
	return;
}
?>

<div id="comments">

	<?php
	if ( have_comments() ) :
		?>
		<h2>
			<?php
			$_uw_theme_comment_count = get_comments_number();
			if ( '1' === $_uw_theme_comment_count ) {
				// phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped
				printf(
					/* translators: 1: title. */
					esc_html__( 'One comment on &ldquo;%1$s&rdquo;', '_uw-theme' ),
					get_the_title()
				);
				// phpcs:enable WordPress.Security.EscapeOutput.OutputNotEscaped
			} else {
				// phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped
				printf(
					/* translators: 1: comment count number, 2: title. */
					esc_html( _nx( '%1$s comment on &ldquo;%2$s&rdquo;', '%1$s comments on &ldquo;%2$s&rdquo;', $_uw_theme_comment_count, 'comments title', '_uw-theme' ) ),
					number_format_i18n( $_uw_theme_comment_count ),
					get_the_title()
				);
				// phpcs:enable WordPress.Security.EscapeOutput.OutputNotEscaped
			}
			?>
		</h2>

		<?php the_comments_navigation(); ?>

		<ol>
			<?php
			wp_list_comments(
				array(
					'style'      => 'ol',
					'callback'   => '_uw_theme_html5_comment',
					'short_ping' => true,
				)
			);
			?>
		</ol>

		<?php
		the_comments_navigation();

		// If there are existing comments, but comments are closed, display a
		// message.
		if ( ! comments_open() ) :
			?>
			<p><?php esc_html_e( 'Comments are closed.', '_uw-theme' ); ?></p>
			<?php
		endif;

	endif;

	comment_form();
	?>

</div><!-- #comments -->
</file>

<file path="theme/footer.php">
<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the `#content` element and all content thereafter.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package _uw-theme
 */

?>

	</div><!-- #content -->

	<?php get_template_part( 'template-parts/layout/footer', 'content' ); ?>

</div><!-- #page -->

<?php wp_footer(); ?>

</body>
</html>
</file>

<file path="theme/front-page.php">
<?php
/**
 * The front page template file
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

get_header();

// Enqueue custom slideshow script only on front page
wp_enqueue_script('dumabyt-slideshow', get_template_directory_uri() . '/js/slideshow.min.js', array(), _UW_THEME_VERSION, true);
?>

<main id="primary" class="site-main bg-gray-50">
    <!-- Main slideshow banner -->
    <section class="container mx-auto px-4 mb-8 pt-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Main slideshow - 3 columns wide -->
            <div class="lg:col-span-3">
                <div class="slideshow-container relative overflow-hidden rounded-lg shadow-lg h-[300px] sm:h-[400px] md:h-[500px]">
                    <?php
                    // Get featured posts for slideshow
                    $featured_posts = new WP_Query(array(
                        'posts_per_page' => 5,
                        'meta_key' => '_thumbnail_id',
                        'post_type' => 'post',
                        'post_status' => 'publish'
                    ));
                    
                    if ($featured_posts->have_posts()) :
                        $slide_index = 0;
                        while ($featured_posts->have_posts()) : $featured_posts->the_post();
                            $slide_index++;
                    ?>
                        <div class="slide absolute inset-0 transition-all duration-500 ease-in-out <?php echo $slide_index === 1 ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full'; ?>">
                            <?php if (has_post_thumbnail()) : ?>
                            <a href="<?php the_permalink(); ?>" class="block h-full">
                                <?php the_post_thumbnail('full', array('class' => 'w-full h-full object-cover')); ?>
                                <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent/20 flex items-end">
                                    <div class="p-4 md:p-6 text-white">
                                        <h2 class="text-xl md:text-2xl lg:text-3xl font-bold"><?php the_title(); ?></h2>
                                        <p class="mt-2 text-sm md:text-base"><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
                                    </div>
                                </div>
                            </a>
                            <?php endif; ?>
                        </div>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    else :
                        // Fallback if no featured posts
                    ?>
                        <div class="slide absolute inset-0 opacity-100">
                            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Dům a Byt" class="w-full h-full object-cover">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                                <div class="p-6 text-white">
                                    <h2 class="text-3xl font-bold">DŮM & BYT</h2>
                                    <p class="mt-2">Váš průvodce světem bydlení a stavby</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Sidebar ads - 1 column wide -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <a href="#" class="block">
                        <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Advertisement" class="w-full h-auto">
                    </a>
                </div>
                
                <!-- Additional ad space -->
                <div class="mt-6 bg-white rounded-lg shadow-sm overflow-hidden">
                    <a href="#" class="block">
                        <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Advertisement" class="w-full h-auto">
                    </a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Category sections - first row -->
    <section class="container mx-auto px-4 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- DŮM -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
                <div class="border-b border-gray-200">
                    <h2 class="text-xl font-bold uppercase py-3 px-4 text-center text-gray-800">
                        <a href="<?php echo esc_url(get_category_link(get_cat_ID('dum'))); ?>" class="hover:text-blue-600 transition-colors">DŮM</a>
                    </h2>
                </div>
                <div class="p-4">
                    <?php
                    $dum_articles = new WP_Query(array(
                        'category_name' => 'dum',
                        'posts_per_page' => 1,
                        'meta_key' => '_thumbnail_id'
                    ));
                    
                    if ($dum_articles->have_posts()) :
                        while ($dum_articles->have_posts()) : $dum_articles->the_post();
                    ?>
                        <a href="<?php the_permalink(); ?>" class="block">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('medium', array('class' => 'w-full h-auto rounded')); ?>
                            <?php else: ?>
                                <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="<?php the_title_attribute(); ?>" class="w-full h-auto rounded">
                            <?php endif; ?>
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800 hover:text-blue-600 transition-colors">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        <p class="text-sm mt-1 text-gray-600"><?php echo wp_trim_words(get_the_excerpt(), 12); ?></p>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    else:
                    ?>
                        <a href="#" class="block">
                            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Dům" class="w-full h-auto rounded">
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800">Moderní rodinný dům se zahradou</h3>
                        <p class="text-sm mt-1 text-gray-600">Prohlédněte si inspirativní projekty moderních rodinných domů.</p>
                    <?php endif; ?>
                    
                    <?php
                    // List of more articles
                    $more_dum_articles = new WP_Query(array(
                        'category_name' => 'dum',
                        'posts_per_page' => 3,
                        'offset' => 1
                    ));
                    
                    if ($more_dum_articles->have_posts()) :
                    ?>
                        <ul class="mt-3 space-y-2 border-t border-gray-100 pt-3">
                        <?php while ($more_dum_articles->have_posts()) : $more_dum_articles->the_post(); ?>
                            <li class="text-sm text-gray-700">
                                <a href="<?php the_permalink(); ?>" class="hover:text-blue-600 transition-colors">
                                    &bull; <?php the_title(); ?>
                                </a>
                            </li>
                        <?php 
                        endwhile;
                        wp_reset_postdata();
                        ?>
                        </ul>
                    <?php endif; ?>
                    
                    <a href="<?php echo esc_url(get_category_link(get_cat_ID('dum'))); ?>" class="text-blue-600 text-sm font-bold mt-3 inline-block hover:underline">více článků</a>
                </div>
            </div>
            
            <!-- INTERIÉR -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
                <div class="border-b border-gray-200">
                    <h2 class="text-xl font-bold uppercase py-3 px-4 text-center text-gray-800">
                        <a href="<?php echo esc_url(get_category_link(get_cat_ID('interier'))); ?>" class="hover:text-blue-600 transition-colors">INTERIÉR</a>
                    </h2>
                </div>
                <div class="p-4">
                    <?php
                    $interier_articles = new WP_Query(array(
                        'category_name' => 'interier',
                        'posts_per_page' => 1,
                        'meta_key' => '_thumbnail_id'
                    ));
                    
                    if ($interier_articles->have_posts()) :
                        while ($interier_articles->have_posts()) : $interier_articles->the_post();
                    ?>
                        <a href="<?php the_permalink(); ?>" class="block">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('medium', array('class' => 'w-full h-auto rounded')); ?>
                            <?php else: ?>
                                <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="<?php the_title_attribute(); ?>" class="w-full h-auto rounded">
                            <?php endif; ?>
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800 hover:text-blue-600 transition-colors">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        <p class="text-sm mt-1 text-gray-600"><?php echo wp_trim_words(get_the_excerpt(), 12); ?></p>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    else:
                    ?>
                        <a href="#" class="block">
                            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Interiér" class="w-full h-auto rounded">
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800">Jak vybrat správný nábytek pro malé prostory</h3>
                        <p class="text-sm mt-1 text-gray-600">Praktické tipy a triky pro zařízení malých bytů a interiérů.</p>
                    <?php endif; ?>
                    
                    <?php
                    // List of more articles
                    $more_interier_articles = new WP_Query(array(
                        'category_name' => 'interier',
                        'posts_per_page' => 3,
                        'offset' => 1
                    ));
                    
                    if ($more_interier_articles->have_posts()) :
                    ?>
                        <ul class="mt-3 space-y-2 border-t border-gray-100 pt-3">
                        <?php while ($more_interier_articles->have_posts()) : $more_interier_articles->the_post(); ?>
                            <li class="text-sm text-gray-700">
                                <a href="<?php the_permalink(); ?>" class="hover:text-blue-600 transition-colors">
                                    &bull; <?php the_title(); ?>
                                </a>
                            </li>
                        <?php 
                        endwhile;
                        wp_reset_postdata();
                        ?>
                        </ul>
                    <?php endif; ?>
                    
                    <a href="<?php echo esc_url(get_category_link(get_cat_ID('interier'))); ?>" class="text-blue-600 text-sm font-bold mt-3 inline-block hover:underline">více článků</a>
                </div>
            </div>
            
            <!-- Ad Space -->
            <div class="space-y-6">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <a href="#" class="block">
                        <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Advertisement" class="w-full h-auto">
                    </a>
                </div>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <a href="#" class="block">
                        <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Advertisement" class="w-full h-auto">
                    </a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Second row of categories -->
    <section class="container mx-auto px-4 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- STAVBA -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
                <div class="border-b border-gray-200">
                    <h2 class="text-xl font-bold uppercase py-3 px-4 text-center text-gray-800">
                        <a href="<?php echo esc_url(get_category_link(get_cat_ID('stavba'))); ?>" class="hover:text-blue-600 transition-colors">STAVBA</a>
                    </h2>
                </div>
                <div class="p-4">
                    <?php
                    $stavba_articles = new WP_Query(array(
                        'category_name' => 'stavba',
                        'posts_per_page' => 1,
                        'meta_key' => '_thumbnail_id'
                    ));
                    
                    if ($stavba_articles->have_posts()) :
                        while ($stavba_articles->have_posts()) : $stavba_articles->the_post();
                    ?>
                        <a href="<?php the_permalink(); ?>" class="block">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('medium', array('class' => 'w-full h-auto rounded')); ?>
                            <?php else: ?>
                                <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="<?php the_title_attribute(); ?>" class="w-full h-auto rounded">
                            <?php endif; ?>
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800 hover:text-blue-600 transition-colors">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        <p class="text-sm mt-1 text-gray-600"><?php echo wp_trim_words(get_the_excerpt(), 12); ?></p>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    else:
                    ?>
                        <a href="#" class="block">
                            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Stavba" class="w-full h-auto rounded">
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800">Trendy v moderních stavebních technologiích</h3>
                        <p class="text-sm mt-1 text-gray-600">Inovativní technologie pro zdravé a úsporné bydlení.</p>
                    <?php endif; ?>
                    
                    <?php
                    // List of more articles
                    $more_stavba_articles = new WP_Query(array(
                        'category_name' => 'stavba',
                        'posts_per_page' => 3,
                        'offset' => 1
                    ));
                    
                    if ($more_stavba_articles->have_posts()) :
                    ?>
                        <ul class="mt-3 space-y-2 border-t border-gray-100 pt-3">
                        <?php while ($more_stavba_articles->have_posts()) : $more_stavba_articles->the_post(); ?>
                            <li class="text-sm text-gray-700">
                                <a href="<?php the_permalink(); ?>" class="hover:text-blue-600 transition-colors">
                                    &bull; <?php the_title(); ?>
                                </a>
                            </li>
                        <?php 
                        endwhile;
                        wp_reset_postdata();
                        ?>
                        </ul>
                    <?php endif; ?>
                    
                    <a href="<?php echo esc_url(get_category_link(get_cat_ID('stavba'))); ?>" class="text-blue-600 text-sm font-bold mt-3 inline-block hover:underline">více článků</a>
                </div>
            </div>
            
            <!-- REKONSTRUKCE -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
                <div class="border-b border-gray-200">
                    <h2 class="text-xl font-bold uppercase py-3 px-4 text-center text-gray-800">
                        <a href="<?php echo esc_url(get_category_link(get_cat_ID('rekonstrukce'))); ?>" class="hover:text-blue-600 transition-colors">REKONSTRUKCE</a>
                    </h2>
                </div>
                <div class="p-4">
                    <?php
                    $rekonstrukce_articles = new WP_Query(array(
                        'category_name' => 'rekonstrukce',
                        'posts_per_page' => 1,
                        'meta_key' => '_thumbnail_id'
                    ));
                    
                    if ($rekonstrukce_articles->have_posts()) :
                        while ($rekonstrukce_articles->have_posts()) : $rekonstrukce_articles->the_post();
                    ?>
                        <a href="<?php the_permalink(); ?>" class="block">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('medium', array('class' => 'w-full h-auto rounded')); ?>
                            <?php else: ?>
                                <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="<?php the_title_attribute(); ?>" class="w-full h-auto rounded">
                            <?php endif; ?>
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800 hover:text-blue-600 transition-colors">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        <p class="text-sm mt-1 text-gray-600"><?php echo wp_trim_words(get_the_excerpt(), 12); ?></p>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    else:
                    ?>
                        <a href="#" class="block">
                            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Rekonstrukce" class="w-full h-auto rounded">
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800">Jak na úspěšnou rekonstrukci podkroví</h3>
                        <p class="text-sm mt-1 text-gray-600">Využijte nevyužitý půdní prostor pro nové komfortní bydlení.</p>
                    <?php endif; ?>
                    
                    <?php
                    // List of more articles
                    $more_rekonstrukce_articles = new WP_Query(array(
                        'category_name' => 'rekonstrukce',
                        'posts_per_page' => 3,
                        'offset' => 1
                    ));
                    
                    if ($more_rekonstrukce_articles->have_posts()) :
                    ?>
                        <ul class="mt-3 space-y-2 border-t border-gray-100 pt-3">
                        <?php while ($more_rekonstrukce_articles->have_posts()) : $more_rekonstrukce_articles->the_post(); ?>
                            <li class="text-sm text-gray-700">
                                <a href="<?php the_permalink(); ?>" class="hover:text-blue-600 transition-colors">
                                    &bull; <?php the_title(); ?>
                                </a>
                            </li>
                        <?php 
                        endwhile;
                        wp_reset_postdata();
                        ?>
                        </ul>
                    <?php endif; ?>
                    
                    <a href="<?php echo esc_url(get_category_link(get_cat_ID('rekonstrukce'))); ?>" class="text-blue-600 text-sm font-bold mt-3 inline-block hover:underline">více článků</a>
                </div>
            </div>
            
            <!-- Ad Space -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <a href="#" class="block">
                    <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Advertisement" class="w-full h-auto">
                </a>
            </div>
        </div>
    </section>
    
    <!-- Third row of categories -->
    <section class="container mx-auto px-4 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- ZAHRADA -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
                <div class="border-b border-gray-200">
                    <h2 class="text-xl font-bold uppercase py-3 px-4 text-center text-gray-800">
                        <a href="<?php echo esc_url(get_category_link(get_cat_ID('zahrada'))); ?>" class="hover:text-blue-600 transition-colors">ZAHRADA</a>
                    </h2>
                </div>
                <div class="p-4">
                    <?php
                    $zahrada_articles = new WP_Query(array(
                        'category_name' => 'zahrada',
                        'posts_per_page' => 1,
                        'meta_key' => '_thumbnail_id'
                    ));
                    
                    if ($zahrada_articles->have_posts()) :
                        while ($zahrada_articles->have_posts()) : $zahrada_articles->the_post();
                    ?>
                        <a href="<?php the_permalink(); ?>" class="block">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('medium', array('class' => 'w-full h-auto rounded')); ?>
                            <?php else: ?>
                                <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="<?php the_title_attribute(); ?>" class="w-full h-auto rounded">
                            <?php endif; ?>
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800 hover:text-blue-600 transition-colors">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        <p class="text-sm mt-1 text-gray-600"><?php echo wp_trim_words(get_the_excerpt(), 12); ?></p>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    else:
                    ?>
                        <a href="#" class="block">
                            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Zahrada" class="w-full h-auto rounded">
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800">Jak vytvořit udržitelnou zahradu</h3>
                        <p class="text-sm mt-1 text-gray-600">Permakulturní principy pro ekologickou zahradu plnou života.</p>
                    <?php endif; ?>
                    
                    <?php
                    // List of more articles
                    $more_zahrada_articles = new WP_Query(array(
                        'category_name' => 'zahrada',
                        'posts_per_page' => 3,
                        'offset' => 1
                    ));
                    
                    if ($more_zahrada_articles->have_posts()) :
                    ?>
                        <ul class="mt-3 space-y-2 border-t border-gray-100 pt-3">
                        <?php while ($more_zahrada_articles->have_posts()) : $more_zahrada_articles->the_post(); ?>
                            <li class="text-sm text-gray-700">
                                <a href="<?php the_permalink(); ?>" class="hover:text-blue-600 transition-colors">
                                    &bull; <?php the_title(); ?>
                                </a>
                            </li>
                        <?php 
                        endwhile;
                        wp_reset_postdata();
                        ?>
                        </ul>
                    <?php endif; ?>
                    
                    <a href="<?php echo esc_url(get_category_link(get_cat_ID('zahrada'))); ?>" class="text-blue-600 text-sm font-bold mt-3 inline-block hover:underline">více článků</a>
                </div>
            </div>
            
            <!-- BLOG -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
                <div class="border-b border-gray-200">
                    <h2 class="text-xl font-bold uppercase py-3 px-4 text-center text-gray-800">
                        <a href="<?php echo esc_url(get_category_link(get_cat_ID('blog'))); ?>" class="hover:text-blue-600 transition-colors">BLOG</a>
                    </h2>
                </div>
                <div class="p-4">
                    <?php
                    $blog_articles = new WP_Query(array(
                        'category_name' => 'blog',
                        'posts_per_page' => 1,
                        'meta_key' => '_thumbnail_id'
                    ));
                    
                    if ($blog_articles->have_posts()) :
                        while ($blog_articles->have_posts()) : $blog_articles->the_post();
                    ?>
                        <a href="<?php the_permalink(); ?>" class="block">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('medium', array('class' => 'w-full h-auto rounded')); ?>
                            <?php else: ?>
                                <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="<?php the_title_attribute(); ?>" class="w-full h-auto rounded">
                            <?php endif; ?>
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800 hover:text-blue-600 transition-colors">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        <p class="text-sm mt-1 text-gray-600"><?php echo wp_trim_words(get_the_excerpt(), 12); ?></p>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    else:
                    ?>
                        <a href="#" class="block">
                            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Blog" class="w-full h-auto rounded">
                        </a>
                        <h3 class="mt-3 font-bold text-gray-800">Architekt Jan Kouba: Inspirace je všude kolem nás</h3>
                        <p class="text-sm mt-1 text-gray-600">Rozhovor s úspěšným českým architektem o jeho přístupu k navrhování.</p>
                    <?php endif; ?>
                    
                    <?php
                    // List of more articles
                    $more_blog_articles = new WP_Query(array(
                        'category_name' => 'blog',
                        'posts_per_page' => 3,
                        'offset' => 1
                    ));
                    
                    if ($more_blog_articles->have_posts()) :
                    ?>
                        <ul class="mt-3 space-y-2 border-t border-gray-100 pt-3">
                        <?php while ($more_blog_articles->have_posts()) : $more_blog_articles->the_post(); ?>
                            <li class="text-sm text-gray-700">
                                <a href="<?php the_permalink(); ?>" class="hover:text-blue-600 transition-colors">
                                    &bull; <?php the_title(); ?>
                                </a>
                            </li>
                        <?php 
                        endwhile;
                        wp_reset_postdata();
                        ?>
                        </ul>
                    <?php endif; ?>
                    
                    <a href="<?php echo esc_url(get_category_link(get_cat_ID('blog'))); ?>" class="text-blue-600 text-sm font-bold mt-3 inline-block hover:underline">více článků</a>
                </div>
            </div>
            
            <!-- Partners section -->
            <div class="space-y-6">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden p-4">
                    <h3 class="font-bold mb-4 text-gray-700 text-center border-b pb-2">PARTNEŘI SEKCE</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <a href="#" class="block hover:opacity-80 transition-opacity">
                            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.
</file>

<file path="theme/functions.php">
<?php
/**
 * _uw-theme functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package _uw-theme
 */

if ( ! defined( '_UW_THEME_VERSION' ) ) {
	/*
	 * Set the theme’s version number.
	 *
	 * This is used primarily for cache busting. If you use `npm run bundle`
	 * to create your production build, the value below will be replaced in the
	 * generated zip file with a timestamp, converted to base 36.
	 */
	define( '_UW_THEME_VERSION', '0.1.0' );
}

if ( ! defined( '_UW_THEME_TYPOGRAPHY_CLASSES' ) ) {
	/*
	 * Set Tailwind Typography classes for the front end, block editor and
	 * classic editor using the constant below.
	 *
	 * For the front end, these classes are added by the `_uw_theme_content_class`
	 * function. You will see that function used everywhere an `entry-content`
	 * or `page-content` class has been added to a wrapper element.
	 *
	 * For the block editor, these classes are converted to a JavaScript array
	 * and then used by the `./javascript/block-editor.js` file, which adds
	 * them to the appropriate elements in the block editor (and adds them
	 * again when they’re removed.)
	 *
	 * For the classic editor (and anything using TinyMCE, like Advanced Custom
	 * Fields), these classes are added to TinyMCE’s body class when it
	 * initializes.
	 */
	define(
		'_UW_THEME_TYPOGRAPHY_CLASSES',
		'prose prose-neutral max-w-none prose-a:text-primary'
	);
}

if ( ! function_exists( '_uw_theme_setup' ) ) :
	/**
	 * Sets up theme defaults and registers support for various WordPress features.
	 *
	 * Note that this function is hooked into the after_setup_theme hook, which
	 * runs before the init hook. The init hook is too late for some features, such
	 * as indicating support for post thumbnails.
	 */
	function _uw_theme_setup() {
		/*
		 * Make theme available for translation.
		 * Translations can be filed in the /languages/ directory.
		 * If you're building a theme based on _uw-theme, use a find and replace
		 * to change '_uw-theme' to the name of your theme in all the template files.
		 */
		load_theme_textdomain( '_uw-theme', get_template_directory() . '/languages' );

		// Add default posts and comments RSS feed links to head.
		add_theme_support( 'automatic-feed-links' );

		/*
		 * Let WordPress manage the document title.
		 * By adding theme support, we declare that this theme does not use a
		 * hard-coded <title> tag in the document head, and expect WordPress to
		 * provide it for us.
		 */
		add_theme_support( 'title-tag' );

		/*
		 * Enable support for Post Thumbnails on posts and pages.
		 *
		 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		 */
		add_theme_support( 'post-thumbnails' );

		// This theme uses wp_nav_menu() in multiple locations.
		register_nav_menus(
			array(
				'menu-1' => __( 'Primary', '_uw-theme' ),
				'menu-2' => __( 'Footer Menu', '_uw-theme' ),
				'top-menu' => __( 'Top Menu', '_uw-theme' ),
			)
		);

		/*
		 * Switch default core markup for search form, comment form, and comments
		 * to output valid HTML5.
		 */
		add_theme_support(
			'html5',
			array(
				'search-form',
				'comment-form',
				'comment-list',
				'gallery',
				'caption',
				'style',
				'script',
			)
		);

		// Add theme support for selective refresh for widgets.
		add_theme_support( 'customize-selective-refresh-widgets' );

		// Add support for editor styles.
		add_theme_support( 'editor-styles' );

		// Enqueue editor styles.
		add_editor_style( 'style-editor.css' );
		add_editor_style( 'style-editor-extra.css' );

		// Add support for responsive embedded content.
		add_theme_support( 'responsive-embeds' );

		// Remove support for block templates.
		remove_theme_support( 'block-templates' );
	}
endif;
add_action( 'after_setup_theme', '_uw_theme_setup' );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function _uw_theme_widgets_init() {
	register_sidebar(
		array(
			'name'          => __( 'Footer', '_uw-theme' ),
			'id'            => 'sidebar-1',
			'description'   => __( 'Add widgets here to appear in your footer.', '_uw-theme' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action( 'widgets_init', '_uw_theme_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function _uw_theme_scripts() {
	wp_enqueue_style( '_uw-theme-style', get_stylesheet_uri(), array(), _UW_THEME_VERSION );
	wp_enqueue_script( '_uw-theme-script', get_template_directory_uri() . '/js/script.min.js', array(), _UW_THEME_VERSION, true );

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}
}
add_action( 'wp_enqueue_scripts', '_uw_theme_scripts' );

/**
 * Enqueue the block editor script.
 */
function _uw_theme_enqueue_block_editor_script() {
	if ( is_admin() ) {
		wp_enqueue_script(
			'_uw-theme-editor',
			get_template_directory_uri() . '/js/block-editor.min.js',
			array(
				'wp-blocks',
				'wp-edit-post',
			),
			_UW_THEME_VERSION,
			true
		);
		wp_add_inline_script( '_uw-theme-editor', "tailwindTypographyClasses = '" . esc_attr( _UW_THEME_TYPOGRAPHY_CLASSES ) . "'.split(' ');", 'before' );
	}
}
add_action( 'enqueue_block_assets', '_uw_theme_enqueue_block_editor_script' );

/**
 * Add the Tailwind Typography classes to TinyMCE.
 *
 * @param array $settings TinyMCE settings.
 * @return array
 */
function _uw_theme_tinymce_add_class( $settings ) {
	$settings['body_class'] = _UW_THEME_TYPOGRAPHY_CLASSES;
	return $settings;
}
add_filter( 'tiny_mce_before_init', '_uw_theme_tinymce_add_class' );

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Custom Walker class for multilevel navigation.
 */
require get_template_directory() . '/inc/class-dumabyt-walker-nav-menu.php';

/**
 * Breadcrumbs functionality.
 */
require get_template_directory() . '/inc/breadcrumbs.php';
</file>

<file path="theme/header.php">
<?php
/**
 * The header for our theme
 *
 * This is the template that displays the `head` element and everything up
 * until the `#content` element.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package _uw-theme
 */

?><!doctype html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11">
	<?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>

<?php wp_body_open(); ?>

<div id="page">
	<a href="#content" class="sr-only"><?php esc_html_e( 'Skip to content', '_uw-theme' ); ?></a>

	<?php get_template_part( 'template-parts/layout/header', 'content' ); ?>

	<div id="content">
</file>

<file path="theme/index.php">
<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no `home.php` file exists.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

get_header();
?>

	<section id="primary">
		<main id="main">

		<?php
		if ( have_posts() ) {

			if ( is_home() && ! is_front_page() ) :
				?>
				<header class="entry-header">
					<h1 class="entry-title"><?php single_post_title(); ?></h1>
				</header><!-- .entry-header -->
				<?php
			endif;

			// Load posts loop.
			while ( have_posts() ) {
				the_post();
				get_template_part( 'template-parts/content/content' );
			}

			// Previous/next page navigation.
			_uw_theme_the_posts_navigation();

		} else {

			// If no content, include the "No posts found" template.
			get_template_part( 'template-parts/content/content', 'none' );

		}
		?>

		</main><!-- #main -->
	</section><!-- #primary -->

<?php
get_footer();
</file>

<file path="theme/page.php">
<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default. Please note that
 * this is the WordPress construct of pages: specifically, posts with a post
 * type of `page`.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

get_header();
?>

	<section id="primary">
		<main id="main">

			<?php
			/* Start the Loop */
			while ( have_posts() ) :
				the_post();

				get_template_part( 'template-parts/content/content', 'page' );

				// If comments are open, or we have at least one comment, load
				// the comment template.
				if ( comments_open() || get_comments_number() ) {
					comments_template();
				}

			endwhile; // End of the loop.
			?>

		</main><!-- #main -->
	</section><!-- #primary -->

<?php
get_footer();
</file>

<file path="theme/search.php">
<?php
/**
 * The template for displaying search results pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#search-result
 *
 * @package _uw-theme
 */

get_header();
?>

	<section id="primary">
		<main id="main">

		<?php if ( have_posts() ) : ?>

			<header class="page-header">
				<?php
				printf(
					/* translators: 1: search result title. 2: search term. */
					'<h1 class="page-title">%1$s <span>%2$s</span></h1>',
					esc_html__( 'Search results for:', '_uw-theme' ),
					get_search_query()
				);
				?>
			</header><!-- .page-header -->

			<?php
			// Start the Loop.
			while ( have_posts() ) :
				the_post();
				get_template_part( 'template-parts/content/content', 'excerpt' );

				// End the loop.
			endwhile;

			// Previous/next page navigation.
			_uw_theme_the_posts_navigation();

		else :

			// If no content is found, get the `content-none` template part.
			get_template_part( 'template-parts/content/content', 'none' );

		endif;
		?>
		</main><!-- #main -->
	</section><!-- #primary -->

<?php
get_footer();
</file>

<file path="theme/single.php">
<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package _uw-theme
 */

get_header();
?>

	<section id="primary">
		<main id="main">

			<?php
			/* Start the Loop */
			while ( have_posts() ) :
				the_post();
				get_template_part( 'template-parts/content/content', 'single' );

				if ( is_singular( 'post' ) ) {
					// Previous/next post navigation.
					the_post_navigation(
						array(
							'next_text' => '<span aria-hidden="true">' . __( 'Next Post', '_uw-theme' ) . '</span> ' .
								'<span class="sr-only">' . __( 'Next post:', '_uw-theme' ) . '</span> <br/>' .
								'<span>%title</span>',
							'prev_text' => '<span aria-hidden="true">' . __( 'Previous Post', '_uw-theme' ) . '</span> ' .
								'<span class="sr-only">' . __( 'Previous post:', '_uw-theme' ) . '</span> <br/>' .
								'<span>%title</span>',
						)
					);
				}

				// If comments are open, or we have at least one comment, load
				// the comment template.
				if ( comments_open() || get_comments_number() ) {
					comments_template();
				}

				// End the loop.
			endwhile;
			?>

		</main><!-- #main -->
	</section><!-- #primary -->

<?php
get_footer();
</file>

<file path="theme/style-editor-extra.css">
/*! tailwindcss v4.0.14 | MIT License | https://tailwindcss.com */
.entry-header,
.entry-content {
  padding-inline: calc(var(--spacing, 0.25rem) * 8);
}
</file>

<file path="theme/style-editor.css">
/*! tailwindcss v4.0.14 | MIT License | https://tailwindcss.com */
/*!
Theme Name: _uw-theme
Theme URI: https://umimeweby.cz
Author: Team UW
Author URI: https://underscoretw.com/
Description: Custom sablona pro DUM A BYT
Version: 0.1.0
Tested up to: 6.2
Requires PHP: 7.4
License: GNU General Public License v2 or later
License URI: LICENSE
Text Domain: _uw-theme
Tags:

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned.

_uw-theme is based on _tw https://underscoretw.com/, (C) 2021-2025 Greg Sullivan
_tw is distributed under the terms of the GNU GPL v2 or later.

_tw is based on Underscores https://underscores.me/ and Varia https://github.com/Automattic/themes/tree/master/varia, (C) 2012-2025 Automattic, Inc.
Underscores and Varia are distributed under the terms of the GNU GPL v2 or later.
*/
:root, :host {
  --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
		"Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
		"Liberation Mono", "Courier New", monospace;
  --color-blue-500: oklch(0.623 0.214 259.815);
  --color-blue-600: oklch(0.546 0.245 262.881);
  --color-gray-50: oklch(0.985 0.002 247.839);
  --color-gray-100: oklch(0.967 0.003 264.542);
  --color-gray-200: oklch(0.928 0.006 264.531);
  --color-gray-300: oklch(0.872 0.01 258.338);
  --color-gray-400: oklch(0.707 0.022 261.325);
  --color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-neutral-900: oklch(0.205 0 0);
  --color-black: #000;
  --color-white: #fff;
  --spacing: 0.25rem;
  --text-xs: 0.75rem;
  --text-xs--line-height: calc(1 / 0.75);
  --text-sm: 0.875rem;
  --text-sm--line-height: calc(1.25 / 0.875);
  --text-base: 1rem;
  --text-base--line-height: calc(1.5 / 1);
  --text-xl: 1.25rem;
  --text-xl--line-height: calc(1.75 / 1.25);
  --text-2xl: 1.5rem;
  --text-2xl--line-height: calc(2 / 1.5);
  --text-3xl: 1.875rem;
  --text-3xl--line-height: calc(2.25 / 1.875);
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --tracking-wider: 0.05em;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --default-transition-duration: 150ms;
  --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  --default-font-family: var(--font-sans);
  --default-font-feature-settings: var(--font-sans--font-feature-settings);
  --default-font-variation-settings: var(--font-sans--font-variation-settings);
  --default-mono-font-family: var(--font-mono);
  --default-mono-font-feature-settings: var(--font-mono--font-feature-settings);
  --default-mono-font-variation-settings: var(--font-mono--font-variation-settings);
  --color-background: var(--wp--preset--color--background);
  --color-foreground: var(--wp--preset--color--foreground);
  --color-primary: var(--wp--preset--color--primary);
  --color-secondary: var(--wp--preset--color--secondary);
  --color-tertiary: var(--wp--preset--color--tertiary);
  --container-content: var(--wp--style--global--content-size);
  --container-wide: var(--wp--style--global--wide-size);
}
body {
  background-color: var(--color-background);
  font-family: var(--font-sans);
  color: var(--color-foreground);
}
#masthead {
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.logo-container a {
  text-decoration-line: none;
}
.top-links a {
  text-decoration-line: none;
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
.main-navigation {
  position: relative;
  z-index: 10;
}
#primary-menu {
  display: flex;
  flex-direction: row;
}
:where(#primary-menu > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
}
#primary-menu {
  padding-block: calc(var(--spacing) * 4);
}
#primary-menu > li {
  position: relative;
}
#primary-menu > li > a {
  display: block;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  text-transform: uppercase;
  text-decoration-line: none;
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
@media (hover: hover) {
  :is(#primary-menu > li > a):hover {
    color: var(--color-gray-900);
  }
}
#primary-menu > li.current-menu-item > a, #primary-menu > li.current-menu-ancestor > a {
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
}
.dropdown-indicator {
  margin-left: calc(var(--spacing) * 1);
  display: inline-block;
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
}
.sub-menu {
  position: absolute;
  z-index: 20;
  display: none;
  min-width: 200px;
  border-radius: var(--radius-sm);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-200);
  background-color: var(--color-white);
  --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
#primary-menu > li:hover > .sub-menu, .sub-menu.active {
  display: block;
}
.sub-menu li {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: var(--color-gray-100);
}
:is(.sub-menu li):last-child {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 0px;
}
.sub-menu li a {
  display: block;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  color: var(--color-gray-700);
  text-decoration-line: none;
}
@media (hover: hover) {
  :is(.sub-menu li a):hover {
    background-color: var(--color-gray-50);
  }
}
@media (hover: hover) {
  :is(.sub-menu li a):hover {
    color: var(--color-gray-900);
  }
}
.mobile-menu {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
  border-color: var(--color-gray-200);
  background-color: var(--color-white);
}
.mobile-menu ul {
  padding-block: calc(var(--spacing) * 2);
}
.mobile-menu li {
  position: relative;
}
.mobile-menu li a {
  display: block;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  color: var(--color-gray-700);
  text-decoration-line: none;
}
.mobile-menu .sub-menu {
  position: static;
  width: 100%;
  border-style: var(--tw-border-style);
  border-width: 0px;
  background-color: var(--color-gray-50);
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.mobile-menu .sub-menu li:first-child {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
  border-color: var(--color-gray-100);
}
.submenu-toggle {
  position: absolute;
  top: calc(var(--spacing) * 2);
  right: calc(var(--spacing) * 4);
  height: calc(var(--spacing) * 6);
  width: calc(var(--spacing) * 6);
  border-radius: calc(infinity * 1px);
  background-color: var(--color-gray-200);
  text-align: center;
  --tw-leading: calc(var(--spacing) * 6);
  line-height: calc(var(--spacing) * 6);
  color: var(--color-gray-700);
}
.breadcrumbs {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  color: var(--color-gray-600);
}
.breadcrumbs a {
  color: var(--color-gray-600);
  text-decoration-line: none;
}
@media (hover: hover) {
  :is(.breadcrumbs a):hover {
    color: var(--color-gray-900);
  }
}
.search-form {
  display: flex;
  align-items: center;
}
.search-form label {
  flex-grow: 1;
}
.search-field {
  width: 100%;
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-300);
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
}
.search-field:focus {
  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.search-field:focus {
  --tw-ring-color: var(--color-gray-400);
}
.search-field:focus {
  --tw-outline-style: none;
  outline-style: none;
}
.search-submit {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  background-color: var(--color-gray-700);
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-white);
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
@media (hover: hover) {
  .search-submit:hover {
    background-color: var(--color-gray-900);
  }
}
.page-title, .entry-title {
  margin-inline: auto;
  margin-bottom: calc(var(--spacing) * 6);
  max-width: var(--container-content);
  font-size: var(--text-3xl);
  line-height: var(--tw-leading, var(--text-3xl--line-height));
  --tw-font-weight: var(--font-weight-extrabold);
  font-weight: var(--font-weight-extrabold);
  color: var(--color-neutral-900);
}
.page-content > *, .entry-content > * {
  margin-inline: auto;
  max-width: var(--container-content);
}
.entry-content > .alignwide {
  max-width: var(--container-wide);
}
.entry-content > .alignfull {
  max-width: none;
}
.entry-content > .alignleft {
  float: left;
  margin-right: calc(var(--spacing) * 8);
}
.entry-content > .alignright {
  float: right;
  margin-left: calc(var(--spacing) * 8);
}
.visible {
  visibility: visible;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: calc(var(--spacing) * 0);
}
.right-4 {
  right: calc(var(--spacing) * 4);
}
.bottom-4 {
  bottom: calc(var(--spacing) * 4);
}
.z-10 {
  z-index: 10;
}
.float-left {
  float: left;
}
.float-right {
  float: right;
}
.container {
  width: 100%;
}
@media (width >= 40rem) {
  .container {
    max-width: 40rem;
  }
}
@media (width >= 48rem) {
  .container {
    max-width: 48rem;
  }
}
@media (width >= 64rem) {
  .container {
    max-width: 64rem;
  }
}
@media (width >= 80rem) {
  .container {
    max-width: 80rem;
  }
}
@media (width >= 96rem) {
  .container {
    max-width: 96rem;
  }
}
.mx-auto {
  margin-inline: auto;
}
.prose {
  color: var(--tw-prose-body);
  max-width: none;
}
.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where([class~="lead"],[class~="is-style-lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}
.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}
.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}
.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
}
.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
}
.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
}
.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}
.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: decimal;
}
.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))::marker {
  color: var(--tw-prose-bullets);
}
.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}
.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1;
  margin-top: 3em;
  margin-bottom: 3em;
  border-bottom: none;
}
.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
  border-left-style: solid;
}
.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))::before {
  content: open-quote;
}
.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))::after {
  content: close-quote;
}
.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}
.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  font-weight: 900;
  color: inherit;
}
.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}
.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  font-weight: 800;
  color: inherit;
}
.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}
.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}
.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))::before {
  content: "`";
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))::after {
  content: "`";
}
.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
}
.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
}
.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
  font-size: 0.875em;
}
.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
  font-size: 0.9em;
}
.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
}
.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
}
.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: inherit;
}
.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))::before {
  content: none;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))::after {
  content: none;
}
.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}
.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}
.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}
.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}
.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  border-bottom-width: 0;
}
.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  vertical-align: baseline;
}
.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}
.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  vertical-align: top;
}
.prose :where(th,td):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  text-align: start;
}
.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}
.prose {
  --tw-prose-body: oklch(0.373 0.034 259.733);
  --tw-prose-headings: oklch(0.21 0.034 264.665);
  --tw-prose-lead: oklch(0.446 0.03 256.802);
  --tw-prose-links: oklch(0.21 0.034 264.665);
  --tw-prose-bold: oklch(0.21 0.034 264.665);
  --tw-prose-counters: oklch(0.551 0.027 264.364);
  --tw-prose-bullets: oklch(0.872 0.01 258.338);
  --tw-prose-hr: oklch(0.928 0.006 264.531);
  --tw-prose-quotes: oklch(0.21 0.034 264.665);
  --tw-prose-quote-borders: oklch(0.928 0.006 264.531);
  --tw-prose-captions: oklch(0.551 0.027 264.364);
  --tw-prose-kbd: oklch(0.21 0.034 264.665);
  --tw-prose-kbd-shadows: NaN NaN NaN;
  --tw-prose-code: oklch(0.21 0.034 264.665);
  --tw-prose-pre-code: oklch(0.928 0.006 264.531);
  --tw-prose-pre-bg: oklch(0.278 0.033 256.848);
  --tw-prose-th-borders: oklch(0.872 0.01 258.338);
  --tw-prose-td-borders: oklch(0.928 0.006 264.531);
  --tw-prose-invert-body: oklch(0.872 0.01 258.338);
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: oklch(0.707 0.022 261.325);
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: oklch(0.707 0.022 261.325);
  --tw-prose-invert-bullets: oklch(0.446 0.03 256.802);
  --tw-prose-invert-hr: oklch(0.373 0.034 259.733);
  --tw-prose-invert-quotes: oklch(0.967 0.003 264.542);
  --tw-prose-invert-quote-borders: oklch(0.373 0.034 259.733);
  --tw-prose-invert-captions: oklch(0.707 0.022 261.325);
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: oklch(0.872 0.01 258.338);
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: oklch(0.446 0.03 256.802);
  --tw-prose-invert-td-borders: oklch(0.373 0.034 259.733);
  font-size: 1rem;
  line-height: 1.75;
}
.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  padding-inline-start: 0.375em;
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  padding-inline-start: 0.375em;
}
.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}
.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0;
}
.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0;
}
.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0;
}
.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0;
}
.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  padding-inline-start: 0;
}
.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  padding-inline-end: 0;
}
.prose :where(tbody td,tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}
.prose :where(tbody td:first-child,tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  padding-inline-start: 0;
}
.prose :where(tbody td:last-child,tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  padding-inline-end: 0;
}
.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-top: 0;
}
.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  margin-bottom: 0;
}
.prose :where(blockquote > cite):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  color: var(--tw-prose-body);
  font-style: normal;
  font-weight: 400;
}
.prose :where(blockquote > cite):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))::before {
  content: "\2014";
}
.prose :where(table.has-fixed-layout):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *)) {
  table-layout: fixed;
  width: 100%;
}
.mt-1 {
  margin-top: calc(var(--spacing) * 1);
}
.mt-2 {
  margin-top: calc(var(--spacing) * 2);
}
.mt-3 {
  margin-top: calc(var(--spacing) * 3);
}
.mt-6 {
  margin-top: calc(var(--spacing) * 6);
}
.mb-4 {
  margin-bottom: calc(var(--spacing) * 4);
}
.mb-6 {
  margin-bottom: calc(var(--spacing) * 6);
}
.mb-8 {
  margin-bottom: calc(var(--spacing) * 8);
}
.ml-4 {
  margin-left: calc(var(--spacing) * 4);
}
.block {
  display: block;
}
.contents {
  display: contents;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.inline-block {
  display: inline-block;
}
.table {
  display: table;
}
.h-3 {
  height: calc(var(--spacing) * 3);
}
.h-6 {
  height: calc(var(--spacing) * 6);
}
.h-16 {
  height: calc(var(--spacing) * 16);
}
.h-\[300px\] {
  height: 300px;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.w-3 {
  width: calc(var(--spacing) * 3);
}
.w-6 {
  width: calc(var(--spacing) * 6);
}
.w-auto {
  width: auto;
}
.w-full {
  width: 100%;
}
.max-w-content {
  max-width: var(--container-content);
}
.max-w-none {
  max-width: none;
}
.border-collapse {
  border-collapse: collapse;
}
.-translate-x-full {
  --tw-translate-x: -100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-x-0 {
  --tw-translate-x: calc(var(--spacing) * 0);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-x-full {
  --tw-translate-x: 100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.transform {
  transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
}
.resize {
  resize: both;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.gap-2 {
  gap: calc(var(--spacing) * 2);
}
.gap-4 {
  gap: calc(var(--spacing) * 4);
}
.gap-6 {
  gap: calc(var(--spacing) * 6);
}
:where(.space-y-2 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
}
:where(.space-y-6 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
}
:where(.space-x-2 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
}
:where(.space-x-4 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
}
.overflow-hidden {
  overflow: hidden;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-full {
  border-radius: calc(infinity * 1px);
}
.rounded-lg {
  border-radius: var(--radius-lg);
}
.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}
.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}
.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}
.border-gray-100 {
  border-color: var(--color-gray-100);
}
.border-gray-200 {
  border-color: var(--color-gray-200);
}
.bg-background {
  background-color: var(--color-background);
}
.bg-black {
  background-color: var(--color-black);
}
.bg-gray-50 {
  background-color: var(--color-gray-50);
}
.bg-gray-100 {
  background-color: var(--color-gray-100);
}
.bg-white {
  background-color: var(--color-white);
}
.bg-white\/50 {
  background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
}
.bg-gradient-to-t {
  --tw-gradient-position: to top in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.from-black {
  --tw-gradient-from: var(--color-black);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-black\/70 {
  --tw-gradient-from: color-mix(in oklab, var(--color-black) 70%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-black\/80 {
  --tw-gradient-from: color-mix(in oklab, var(--color-black) 80%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-transparent {
  --tw-gradient-to: transparent;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-transparent\/20 {
  --tw-gradient-to: color-mix(in oklab, transparent 20%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.object-cover {
  object-fit: cover;
}
.p-4 {
  padding: calc(var(--spacing) * 4);
}
.p-6 {
  padding: calc(var(--spacing) * 6);
}
.px-4 {
  padding-inline: calc(var(--spacing) * 4);
}
.py-2 {
  padding-block: calc(var(--spacing) * 2);
}
.py-3 {
  padding-block: calc(var(--spacing) * 3);
}
.pt-2 {
  padding-top: calc(var(--spacing) * 2);
}
.pt-3 {
  padding-top: calc(var(--spacing) * 3);
}
.pt-4 {
  padding-top: calc(var(--spacing) * 4);
}
.pt-8 {
  padding-top: calc(var(--spacing) * 8);
}
.pb-2 {
  padding-bottom: calc(var(--spacing) * 2);
}
.text-center {
  text-align: center;
}
.text-3xl {
  font-size: var(--text-3xl);
  line-height: var(--tw-leading, var(--text-3xl--line-height));
}
.text-base {
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
}
.text-sm {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
}
.text-xl {
  font-size: var(--text-xl);
  line-height: var(--tw-leading, var(--text-xl--line-height));
}
.font-bold {
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
}
.font-extrabold {
  --tw-font-weight: var(--font-weight-extrabold);
  font-weight: var(--font-weight-extrabold);
}
.tracking-wider {
  --tw-tracking: var(--tracking-wider);
  letter-spacing: var(--tracking-wider);
}
.text-blue-500 {
  color: var(--color-blue-500);
}
.text-blue-600 {
  color: var(--color-blue-600);
}
.text-foreground {
  color: var(--color-foreground);
}
.text-gray-500 {
  color: var(--color-gray-500);
}
.text-gray-600 {
  color: var(--color-gray-600);
}
.text-gray-700 {
  color: var(--color-gray-700);
}
.text-gray-800 {
  color: var(--color-gray-800);
}
.text-white {
  color: var(--color-white);
}
.uppercase {
  text-transform: uppercase;
}
.underline {
  text-decoration-line: underline;
}
.opacity-0 {
  opacity: 0%;
}
.opacity-100 {
  opacity: 100%;
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}
.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
}
.filter {
  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
}
.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.duration-300 {
  --tw-duration: 300ms;
  transition-duration: 300ms;
}
.duration-500 {
  --tw-duration: 500ms;
  transition-duration: 500ms;
}
.ease-in-out {
  --tw-ease: var(--ease-in-out);
  transition-timing-function: var(--ease-in-out);
}
.content-none {
  --tw-content: none;
  content: none;
}
.prose-neutral {
  --tw-prose-body: oklch(0.371 0 0);
  --tw-prose-headings: oklch(0.205 0 0);
  --tw-prose-lead: oklch(0.439 0 0);
  --tw-prose-links: oklch(0.205 0 0);
  --tw-prose-bold: oklch(0.205 0 0);
  --tw-prose-counters: oklch(0.556 0 0);
  --tw-prose-bullets: oklch(0.87 0 0);
  --tw-prose-hr: oklch(0.922 0 0);
  --tw-prose-quotes: oklch(0.205 0 0);
  --tw-prose-quote-borders: oklch(0.922 0 0);
  --tw-prose-captions: oklch(0.556 0 0);
  --tw-prose-kbd: oklch(0.205 0 0);
  --tw-prose-kbd-shadows: NaN NaN NaN;
  --tw-prose-code: oklch(0.205 0 0);
  --tw-prose-pre-code: oklch(0.922 0 0);
  --tw-prose-pre-bg: oklch(0.269 0 0);
  --tw-prose-th-borders: oklch(0.87 0 0);
  --tw-prose-td-borders: oklch(0.922 0 0);
  --tw-prose-invert-body: oklch(0.87 0 0);
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: oklch(0.708 0 0);
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: oklch(0.708 0 0);
  --tw-prose-invert-bullets: oklch(0.439 0 0);
  --tw-prose-invert-hr: oklch(0.371 0 0);
  --tw-prose-invert-quotes: oklch(0.97 0 0);
  --tw-prose-invert-quote-borders: oklch(0.371 0 0);
  --tw-prose-invert-captions: oklch(0.708 0 0);
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: oklch(0.87 0 0);
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: oklch(0.439 0 0);
  --tw-prose-invert-td-borders: oklch(0.371 0 0);
}
@media (hover: hover) {
  .hover\:bg-white\/80:hover {
    background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
  }
}
@media (hover: hover) {
  .hover\:text-blue-600:hover {
    color: var(--color-blue-600);
  }
}
@media (hover: hover) {
  .hover\:text-gray-900:hover {
    color: var(--color-gray-900);
  }
}
@media (hover: hover) {
  .hover\:underline:hover {
    text-decoration-line: underline;
  }
}
@media (hover: hover) {
  .hover\:opacity-80:hover {
    opacity: 80%;
  }
}
@media (hover: hover) {
  .hover\:shadow-md:hover {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}
@media (width >= 40rem) {
  .sm\:h-\[400px\] {
    height: 400px;
  }
}
@media (width >= 48rem) {
  .md\:block {
    display: block;
  }
}
@media (width >= 48rem) {
  .md\:hidden {
    display: none;
  }
}
@media (width >= 48rem) {
  .md\:h-\[500px\] {
    height: 500px;
  }
}
@media (width >= 48rem) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (width >= 48rem) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
@media (width >= 48rem) {
  .md\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}
@media (width >= 48rem) {
  .md\:p-6 {
    padding: calc(var(--spacing) * 6);
  }
}
@media (width >= 48rem) {
  .md\:text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
}
@media (width >= 48rem) {
  .md\:text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}
@media (width >= 64rem) {
  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }
}
@media (width >= 64rem) {
  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }
}
@media (width >= 64rem) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
@media (width >= 64rem) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
@media (width >= 64rem) {
  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}
@media (width >= 64rem) {
  .lg\:text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
}
.prose-a\:text-primary :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *,[class~="acf-block-body"][class~="is-selected"] *))) {
  color: var(--color-primary);
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
</file>

<file path="theme/style.css">
/*! tailwindcss v4.0.14 | MIT License | https://tailwindcss.com */
/*!
Theme Name: _uw-theme
Theme URI: https://umimeweby.cz
Author: Team UW
Author URI: https://underscoretw.com/
Description: Custom sablona pro DUM A BYT
Version: 0.1.0
Tested up to: 6.2
Requires PHP: 7.4
License: GNU General Public License v2 or later
License URI: LICENSE
Text Domain: _uw-theme
Tags:

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned.

_uw-theme is based on _tw https://underscoretw.com/, (C) 2021-2025 Greg Sullivan
_tw is distributed under the terms of the GNU GPL v2 or later.

_tw is based on Underscores https://underscores.me/ and Varia https://github.com/Automattic/themes/tree/master/varia, (C) 2012-2025 Automattic, Inc.
Underscores and Varia are distributed under the terms of the GNU GPL v2 or later.
*/
:root, :host {
  --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
		"Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
		"Liberation Mono", "Courier New", monospace;
  --color-blue-500: oklch(0.623 0.214 259.815);
  --color-blue-600: oklch(0.546 0.245 262.881);
  --color-gray-50: oklch(0.985 0.002 247.839);
  --color-gray-100: oklch(0.967 0.003 264.542);
  --color-gray-200: oklch(0.928 0.006 264.531);
  --color-gray-300: oklch(0.872 0.01 258.338);
  --color-gray-400: oklch(0.707 0.022 261.325);
  --color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-neutral-900: oklch(0.205 0 0);
  --color-black: #000;
  --color-white: #fff;
  --spacing: 0.25rem;
  --text-xs: 0.75rem;
  --text-xs--line-height: calc(1 / 0.75);
  --text-sm: 0.875rem;
  --text-sm--line-height: calc(1.25 / 0.875);
  --text-base: 1rem;
  --text-base--line-height: calc(1.5 / 1);
  --text-xl: 1.25rem;
  --text-xl--line-height: calc(1.75 / 1.25);
  --text-2xl: 1.5rem;
  --text-2xl--line-height: calc(2 / 1.5);
  --text-3xl: 1.875rem;
  --text-3xl--line-height: calc(2.25 / 1.875);
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --tracking-wider: 0.05em;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --default-transition-duration: 150ms;
  --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  --default-font-family: var(--font-sans);
  --default-font-feature-settings: var(--font-sans--font-feature-settings);
  --default-font-variation-settings: var(--font-sans--font-variation-settings);
  --default-mono-font-family: var(--font-mono);
  --default-mono-font-feature-settings: var(--font-mono--font-feature-settings);
  --default-mono-font-variation-settings: var(--font-mono--font-variation-settings);
  --color-background: var(--wp--preset--color--background);
  --color-foreground: var(--wp--preset--color--foreground);
  --color-primary: var(--wp--preset--color--primary);
  --color-secondary: var(--wp--preset--color--secondary);
  --color-tertiary: var(--wp--preset--color--tertiary);
  --container-content: var(--wp--style--global--content-size);
  --container-wide: var(--wp--style--global--wide-size);
}
*, ::after, ::before, ::backdrop, ::file-selector-button {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0 solid;
}
html, :host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  tab-size: 4;
  font-family: var( --default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' );
  font-feature-settings: var(--default-font-feature-settings, normal);
  font-variation-settings: var(--default-font-variation-settings, normal);
  -webkit-tap-highlight-color: transparent;
}
body {
  line-height: inherit;
}
hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}
h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}
a {
  color: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}
b, strong {
  font-weight: bolder;
}
code, kbd, samp, pre {
  font-family: var( --default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace );
  font-feature-settings: var(--default-mono-font-feature-settings, normal);
  font-variation-settings: var(--default-mono-font-variation-settings, normal);
  font-size: 1em;
}
small {
  font-size: 80%;
}
sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}
:-moz-focusring {
  outline: auto;
}
progress {
  vertical-align: baseline;
}
summary {
  display: list-item;
}
ol, ul, menu {
  list-style: none;
}
img, svg, video, canvas, audio, iframe, embed, object {
  display: block;
  vertical-align: middle;
}
img, video {
  max-width: 100%;
  height: auto;
}
button, input, select, optgroup, textarea, ::file-selector-button {
  font: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  letter-spacing: inherit;
  color: inherit;
  border-radius: 0;
  background-color: transparent;
  opacity: 1;
}
:where(select:is([multiple], [size])) optgroup {
  font-weight: bolder;
}
:where(select:is([multiple], [size])) optgroup option {
  padding-inline-start: 20px;
}
::file-selector-button {
  margin-inline-end: 4px;
}
::placeholder {
  opacity: 1;
  color: color-mix(in oklab, currentColor 50%, transparent);
}
textarea {
  resize: vertical;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-date-and-time-value {
  min-height: 1lh;
  text-align: inherit;
}
::-webkit-datetime-edit {
  display: inline-flex;
}
::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}
::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
  padding-block: 0;
}
:-moz-ui-invalid {
  box-shadow: none;
}
button, input:where([type='button'], [type='reset'], [type='submit']), ::file-selector-button {
  appearance: button;
}
::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}
[hidden]:where(:not([hidden='until-found'])) {
  display: none !important;
}
body {
  background-color: var(--color-background);
  font-family: var(--font-sans);
  color: var(--color-foreground);
}
#masthead {
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.logo-container a {
  text-decoration-line: none;
}
.top-links a {
  text-decoration-line: none;
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
.main-navigation {
  position: relative;
  z-index: 10;
}
#primary-menu {
  display: flex;
  flex-direction: row;
}
:where(#primary-menu > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
}
#primary-menu {
  padding-block: calc(var(--spacing) * 4);
}
#primary-menu > li {
  position: relative;
}
#primary-menu > li > a {
  display: block;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  text-transform: uppercase;
  text-decoration-line: none;
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
@media (hover: hover) {
  :is(#primary-menu > li > a):hover {
    color: var(--color-gray-900);
  }
}
#primary-menu > li.current-menu-item > a, #primary-menu > li.current-menu-ancestor > a {
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
}
.dropdown-indicator {
  margin-left: calc(var(--spacing) * 1);
  display: inline-block;
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
}
.sub-menu {
  position: absolute;
  z-index: 20;
  display: none;
  min-width: 200px;
  border-radius: var(--radius-sm);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-200);
  background-color: var(--color-white);
  --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
#primary-menu > li:hover > .sub-menu, .sub-menu.active {
  display: block;
}
.sub-menu li {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: var(--color-gray-100);
}
:is(.sub-menu li):last-child {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 0px;
}
.sub-menu li a {
  display: block;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  color: var(--color-gray-700);
  text-decoration-line: none;
}
@media (hover: hover) {
  :is(.sub-menu li a):hover {
    background-color: var(--color-gray-50);
  }
}
@media (hover: hover) {
  :is(.sub-menu li a):hover {
    color: var(--color-gray-900);
  }
}
.mobile-menu {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
  border-color: var(--color-gray-200);
  background-color: var(--color-white);
}
.mobile-menu ul {
  padding-block: calc(var(--spacing) * 2);
}
.mobile-menu li {
  position: relative;
}
.mobile-menu li a {
  display: block;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  color: var(--color-gray-700);
  text-decoration-line: none;
}
.mobile-menu .sub-menu {
  position: static;
  width: 100%;
  border-style: var(--tw-border-style);
  border-width: 0px;
  background-color: var(--color-gray-50);
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.mobile-menu .sub-menu li:first-child {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
  border-color: var(--color-gray-100);
}
.submenu-toggle {
  position: absolute;
  top: calc(var(--spacing) * 2);
  right: calc(var(--spacing) * 4);
  height: calc(var(--spacing) * 6);
  width: calc(var(--spacing) * 6);
  border-radius: calc(infinity * 1px);
  background-color: var(--color-gray-200);
  text-align: center;
  --tw-leading: calc(var(--spacing) * 6);
  line-height: calc(var(--spacing) * 6);
  color: var(--color-gray-700);
}
.breadcrumbs {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  color: var(--color-gray-600);
}
.breadcrumbs a {
  color: var(--color-gray-600);
  text-decoration-line: none;
}
@media (hover: hover) {
  :is(.breadcrumbs a):hover {
    color: var(--color-gray-900);
  }
}
.search-form {
  display: flex;
  align-items: center;
}
.search-form label {
  flex-grow: 1;
}
.search-field {
  width: 100%;
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-300);
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
}
.search-field:focus {
  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.search-field:focus {
  --tw-ring-color: var(--color-gray-400);
}
.search-field:focus {
  --tw-outline-style: none;
  outline-style: none;
}
.search-submit {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  background-color: var(--color-gray-700);
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-white);
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
@media (hover: hover) {
  .search-submit:hover {
    background-color: var(--color-gray-900);
  }
}
.page-title, .entry-title {
  margin-inline: auto;
  margin-bottom: calc(var(--spacing) * 6);
  max-width: var(--container-content);
  font-size: var(--text-3xl);
  line-height: var(--tw-leading, var(--text-3xl--line-height));
  --tw-font-weight: var(--font-weight-extrabold);
  font-weight: var(--font-weight-extrabold);
  color: var(--color-neutral-900);
}
.page-content > *, .entry-content > * {
  margin-inline: auto;
  max-width: var(--container-content);
}
.entry-content > .alignwide {
  max-width: var(--container-wide);
}
.entry-content > .alignfull {
  max-width: none;
}
.entry-content > .alignleft {
  float: left;
  margin-right: calc(var(--spacing) * 8);
}
.entry-content > .alignright {
  float: right;
  margin-left: calc(var(--spacing) * 8);
}
.visible {
  visibility: visible;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: calc(var(--spacing) * 0);
}
.right-4 {
  right: calc(var(--spacing) * 4);
}
.bottom-4 {
  bottom: calc(var(--spacing) * 4);
}
.z-10 {
  z-index: 10;
}
.float-left {
  float: left;
}
.float-right {
  float: right;
}
.container {
  width: 100%;
}
@media (width >= 40rem) {
  .container {
    max-width: 40rem;
  }
}
@media (width >= 48rem) {
  .container {
    max-width: 48rem;
  }
}
@media (width >= 64rem) {
  .container {
    max-width: 64rem;
  }
}
@media (width >= 80rem) {
  .container {
    max-width: 80rem;
  }
}
@media (width >= 96rem) {
  .container {
    max-width: 96rem;
  }
}
.mx-auto {
  margin-inline: auto;
}
.prose {
  color: var(--tw-prose-body);
  max-width: none;
}
.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where([class~="lead"],[class~="is-style-lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}
.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}
.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}
.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}
.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}
.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}
.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}
.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1;
  margin-top: 3em;
  margin-bottom: 3em;
  border-bottom: none;
}
.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
  border-left-style: solid;
}
.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}
.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}
.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}
.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}
.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}
.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}
.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}
.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}
.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "`";
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "`";
}
.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}
.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}
.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}
.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}
.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}
.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}
.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}
.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}
.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}
.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}
.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}
.prose :where(th,td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}
.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}
.prose {
  --tw-prose-body: oklch(0.373 0.034 259.733);
  --tw-prose-headings: oklch(0.21 0.034 264.665);
  --tw-prose-lead: oklch(0.446 0.03 256.802);
  --tw-prose-links: oklch(0.21 0.034 264.665);
  --tw-prose-bold: oklch(0.21 0.034 264.665);
  --tw-prose-counters: oklch(0.551 0.027 264.364);
  --tw-prose-bullets: oklch(0.872 0.01 258.338);
  --tw-prose-hr: oklch(0.928 0.006 264.531);
  --tw-prose-quotes: oklch(0.21 0.034 264.665);
  --tw-prose-quote-borders: oklch(0.928 0.006 264.531);
  --tw-prose-captions: oklch(0.551 0.027 264.364);
  --tw-prose-kbd: oklch(0.21 0.034 264.665);
  --tw-prose-kbd-shadows: NaN NaN NaN;
  --tw-prose-code: oklch(0.21 0.034 264.665);
  --tw-prose-pre-code: oklch(0.928 0.006 264.531);
  --tw-prose-pre-bg: oklch(0.278 0.033 256.848);
  --tw-prose-th-borders: oklch(0.872 0.01 258.338);
  --tw-prose-td-borders: oklch(0.928 0.006 264.531);
  --tw-prose-invert-body: oklch(0.872 0.01 258.338);
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: oklch(0.707 0.022 261.325);
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: oklch(0.707 0.022 261.325);
  --tw-prose-invert-bullets: oklch(0.446 0.03 256.802);
  --tw-prose-invert-hr: oklch(0.373 0.034 259.733);
  --tw-prose-invert-quotes: oklch(0.967 0.003 264.542);
  --tw-prose-invert-quote-borders: oklch(0.373 0.034 259.733);
  --tw-prose-invert-captions: oklch(0.707 0.022 261.325);
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: oklch(0.872 0.01 258.338);
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: oklch(0.446 0.03 256.802);
  --tw-prose-invert-td-borders: oklch(0.373 0.034 259.733);
  font-size: 1rem;
  line-height: 1.75;
}
.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}
.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}
.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose :where(tbody td,tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}
.prose :where(tbody td:first-child,tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose :where(tbody td:last-child,tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}
.prose :where(blockquote > cite):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-body);
  font-style: normal;
  font-weight: 400;
}
.prose :where(blockquote > cite):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "\2014";
}
.prose :where(table.has-fixed-layout):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  table-layout: fixed;
  width: 100%;
}
.mt-1 {
  margin-top: calc(var(--spacing) * 1);
}
.mt-2 {
  margin-top: calc(var(--spacing) * 2);
}
.mt-3 {
  margin-top: calc(var(--spacing) * 3);
}
.mt-6 {
  margin-top: calc(var(--spacing) * 6);
}
.mb-4 {
  margin-bottom: calc(var(--spacing) * 4);
}
.mb-6 {
  margin-bottom: calc(var(--spacing) * 6);
}
.mb-8 {
  margin-bottom: calc(var(--spacing) * 8);
}
.ml-4 {
  margin-left: calc(var(--spacing) * 4);
}
.block {
  display: block;
}
.contents {
  display: contents;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.inline-block {
  display: inline-block;
}
.table {
  display: table;
}
.h-3 {
  height: calc(var(--spacing) * 3);
}
.h-6 {
  height: calc(var(--spacing) * 6);
}
.h-16 {
  height: calc(var(--spacing) * 16);
}
.h-\[300px\] {
  height: 300px;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.w-3 {
  width: calc(var(--spacing) * 3);
}
.w-6 {
  width: calc(var(--spacing) * 6);
}
.w-auto {
  width: auto;
}
.w-full {
  width: 100%;
}
.max-w-content {
  max-width: var(--container-content);
}
.max-w-none {
  max-width: none;
}
.border-collapse {
  border-collapse: collapse;
}
.-translate-x-full {
  --tw-translate-x: -100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-x-0 {
  --tw-translate-x: calc(var(--spacing) * 0);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-x-full {
  --tw-translate-x: 100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.transform {
  transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
}
.resize {
  resize: both;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.gap-2 {
  gap: calc(var(--spacing) * 2);
}
.gap-4 {
  gap: calc(var(--spacing) * 4);
}
.gap-6 {
  gap: calc(var(--spacing) * 6);
}
:where(.space-y-2 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
}
:where(.space-y-6 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
}
:where(.space-x-2 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
}
:where(.space-x-4 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
}
.overflow-hidden {
  overflow: hidden;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-full {
  border-radius: calc(infinity * 1px);
}
.rounded-lg {
  border-radius: var(--radius-lg);
}
.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}
.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}
.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}
.border-gray-100 {
  border-color: var(--color-gray-100);
}
.border-gray-200 {
  border-color: var(--color-gray-200);
}
.bg-background {
  background-color: var(--color-background);
}
.bg-black {
  background-color: var(--color-black);
}
.bg-gray-50 {
  background-color: var(--color-gray-50);
}
.bg-gray-100 {
  background-color: var(--color-gray-100);
}
.bg-white {
  background-color: var(--color-white);
}
.bg-white\/50 {
  background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
}
.bg-gradient-to-t {
  --tw-gradient-position: to top in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.from-black {
  --tw-gradient-from: var(--color-black);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-black\/70 {
  --tw-gradient-from: color-mix(in oklab, var(--color-black) 70%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-black\/80 {
  --tw-gradient-from: color-mix(in oklab, var(--color-black) 80%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-transparent {
  --tw-gradient-to: transparent;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-transparent\/20 {
  --tw-gradient-to: color-mix(in oklab, transparent 20%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.object-cover {
  object-fit: cover;
}
.p-4 {
  padding: calc(var(--spacing) * 4);
}
.p-6 {
  padding: calc(var(--spacing) * 6);
}
.px-4 {
  padding-inline: calc(var(--spacing) * 4);
}
.py-2 {
  padding-block: calc(var(--spacing) * 2);
}
.py-3 {
  padding-block: calc(var(--spacing) * 3);
}
.pt-2 {
  padding-top: calc(var(--spacing) * 2);
}
.pt-3 {
  padding-top: calc(var(--spacing) * 3);
}
.pt-4 {
  padding-top: calc(var(--spacing) * 4);
}
.pt-8 {
  padding-top: calc(var(--spacing) * 8);
}
.pb-2 {
  padding-bottom: calc(var(--spacing) * 2);
}
.text-center {
  text-align: center;
}
.text-3xl {
  font-size: var(--text-3xl);
  line-height: var(--tw-leading, var(--text-3xl--line-height));
}
.text-base {
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
}
.text-sm {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
}
.text-xl {
  font-size: var(--text-xl);
  line-height: var(--tw-leading, var(--text-xl--line-height));
}
.font-bold {
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
}
.font-extrabold {
  --tw-font-weight: var(--font-weight-extrabold);
  font-weight: var(--font-weight-extrabold);
}
.tracking-wider {
  --tw-tracking: var(--tracking-wider);
  letter-spacing: var(--tracking-wider);
}
.text-blue-500 {
  color: var(--color-blue-500);
}
.text-blue-600 {
  color: var(--color-blue-600);
}
.text-foreground {
  color: var(--color-foreground);
}
.text-gray-500 {
  color: var(--color-gray-500);
}
.text-gray-600 {
  color: var(--color-gray-600);
}
.text-gray-700 {
  color: var(--color-gray-700);
}
.text-gray-800 {
  color: var(--color-gray-800);
}
.text-white {
  color: var(--color-white);
}
.uppercase {
  text-transform: uppercase;
}
.underline {
  text-decoration-line: underline;
}
.opacity-0 {
  opacity: 0%;
}
.opacity-100 {
  opacity: 100%;
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}
.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
}
.filter {
  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
}
.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.duration-300 {
  --tw-duration: 300ms;
  transition-duration: 300ms;
}
.duration-500 {
  --tw-duration: 500ms;
  transition-duration: 500ms;
}
.ease-in-out {
  --tw-ease: var(--ease-in-out);
  transition-timing-function: var(--ease-in-out);
}
.content-none {
  --tw-content: none;
  content: none;
}
.prose-neutral {
  --tw-prose-body: oklch(0.371 0 0);
  --tw-prose-headings: oklch(0.205 0 0);
  --tw-prose-lead: oklch(0.439 0 0);
  --tw-prose-links: oklch(0.205 0 0);
  --tw-prose-bold: oklch(0.205 0 0);
  --tw-prose-counters: oklch(0.556 0 0);
  --tw-prose-bullets: oklch(0.87 0 0);
  --tw-prose-hr: oklch(0.922 0 0);
  --tw-prose-quotes: oklch(0.205 0 0);
  --tw-prose-quote-borders: oklch(0.922 0 0);
  --tw-prose-captions: oklch(0.556 0 0);
  --tw-prose-kbd: oklch(0.205 0 0);
  --tw-prose-kbd-shadows: NaN NaN NaN;
  --tw-prose-code: oklch(0.205 0 0);
  --tw-prose-pre-code: oklch(0.922 0 0);
  --tw-prose-pre-bg: oklch(0.269 0 0);
  --tw-prose-th-borders: oklch(0.87 0 0);
  --tw-prose-td-borders: oklch(0.922 0 0);
  --tw-prose-invert-body: oklch(0.87 0 0);
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: oklch(0.708 0 0);
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: oklch(0.708 0 0);
  --tw-prose-invert-bullets: oklch(0.439 0 0);
  --tw-prose-invert-hr: oklch(0.371 0 0);
  --tw-prose-invert-quotes: oklch(0.97 0 0);
  --tw-prose-invert-quote-borders: oklch(0.371 0 0);
  --tw-prose-invert-captions: oklch(0.708 0 0);
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: oklch(0.87 0 0);
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: oklch(0.439 0 0);
  --tw-prose-invert-td-borders: oklch(0.371 0 0);
}
@media (hover: hover) {
  .hover\:bg-white\/80:hover {
    background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
  }
}
@media (hover: hover) {
  .hover\:text-blue-600:hover {
    color: var(--color-blue-600);
  }
}
@media (hover: hover) {
  .hover\:text-gray-900:hover {
    color: var(--color-gray-900);
  }
}
@media (hover: hover) {
  .hover\:underline:hover {
    text-decoration-line: underline;
  }
}
@media (hover: hover) {
  .hover\:opacity-80:hover {
    opacity: 80%;
  }
}
@media (hover: hover) {
  .hover\:shadow-md:hover {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}
@media (width >= 40rem) {
  .sm\:h-\[400px\] {
    height: 400px;
  }
}
@media (width >= 48rem) {
  .md\:block {
    display: block;
  }
}
@media (width >= 48rem) {
  .md\:hidden {
    display: none;
  }
}
@media (width >= 48rem) {
  .md\:h-\[500px\] {
    height: 500px;
  }
}
@media (width >= 48rem) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (width >= 48rem) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
@media (width >= 48rem) {
  .md\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}
@media (width >= 48rem) {
  .md\:p-6 {
    padding: calc(var(--spacing) * 6);
  }
}
@media (width >= 48rem) {
  .md\:text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
}
@media (width >= 48rem) {
  .md\:text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}
@media (width >= 64rem) {
  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }
}
@media (width >= 64rem) {
  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }
}
@media (width >= 64rem) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
@media (width >= 64rem) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
@media (width >= 64rem) {
  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}
@media (width >= 64rem) {
  .lg\:text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
}
.prose-a\:text-primary :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  color: var(--color-primary);
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
</file>

<file path="theme/theme.json">
{
	"$schema": "https://schemas.wp.org/trunk/theme.json",
	"version": 3,
	"settings": {
		"color": {
			"palette": [
				{
					"slug": "background",
					"color": "#ffffff",
					"name": "Background"
				},
				{
					"slug": "foreground",
					"color": "#404040",
					"name": "Foreground"
				},
				{
					"slug": "primary",
					"color": "#b91c1c",
					"name": "Primary"
				},
				{
					"slug": "secondary",
					"color": "#15803d",
					"name": "Secondary"
				},
				{
					"slug": "tertiary",
					"color": "#0369a1",
					"name": "Tertiary"
				}
			]
		},
		"layout": {
			"contentSize": "40rem",
			"wideSize": "60rem"
		}
	}
}
</file>

<file path=".gitignore">
# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv
</file>

<file path="CLAUDE.md">
# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Build Commands
- Development: `npm run dev` or `npm run development`
- Watch for changes: `npm run watch`
- Production: `npm run prod` or `npm run production`
- Bundle for release: `npm run bundle`

## Lint Commands
- Lint check: `npm run lint`
- Auto-fix: `npm run lint-fix`
- PHP Lint: `phpcs`

## Code Style Guidelines
- JavaScript: Uses ESLint with WordPress coding standards
- PHP: Follows WordPress-Extra and WordPress-Docs coding standards
- CSS: Uses Tailwind CSS with PostCSS processing
- Naming: PHP functions/classes prefixed with `_uw_theme_`
- Formatting: Uses Prettier with WordPress config
- I18n: Text domain is `_uw-theme`
- PHP Version: Minimum 7.4
- WordPress Version: Minimum 6.2
</file>

<file path="composer.json">
{
	"require-dev": {
		"dealerdirect/phpcodesniffer-composer-installer": "^1.0",
		"phpcompatibility/phpcompatibility-wp": "^2.1",
		"sirbrillig/phpcs-changed": "^2.11",
		"sirbrillig/phpcs-variable-analysis": "^2.11",
		"wp-cli/i18n-command": "^2.6",
		"wp-coding-standards/wpcs": "^3.1"
	},
	"scripts": {
		"php:lint": "vendor/bin/phpcs -p -s",
		"php:lint:errors": "vendor/bin/phpcs -p -s --runtime-set ignore_warnings_on_exit 1",
		"php:lint:autofix": "vendor/bin/phpcbf",
		"php:lint:changed": "vendor/bin/phpcs-changed --git --git-unstaged",
		"make-pot": "wp i18n make-pot . theme/languages/_uw-theme.pot"
	},
	"config": {
		"allow-plugins": {
			"dealerdirect/phpcodesniffer-composer-installer": true
		}
	}
}
</file>

<file path="eslint.config.mjs">
import globals from 'globals';
import js from '@eslint/js';
import prettier from 'eslint-config-prettier';

export default [
	{
		ignores: ['**/*.min.js', '**/vendor/'],
	},
	{
		files: ['**/*.{js,mjs}'],
		languageOptions: {
			ecmaVersion: 'latest',
			sourceType: 'script',
		},
		rules: {
			...js.configs.recommended.rules,
			...prettier.rules,
		},
	},
	{
		files: ['javascript/**/*.js', '**/*.mjs'],
		languageOptions: {
			sourceType: 'module',
		},
	},
	{
		files: ['javascript/**/*.js'],
		languageOptions: {
			globals: {
				...globals.browser,
				wp: 'readonly',
			},
		},
	},
	{
		files: ['node_scripts/*.js', 'tailwind/*.js'],
		languageOptions: {
			globals: {
				...globals.node,
			},
		},
	},
];
</file>

<file path="LICENSE">
GNU GENERAL PUBLIC LICENSE
                       Version 2, June 1991

 Copyright (C) 1989, 1991 Free Software Foundation, Inc., <http://fsf.org/>
 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.

                            Preamble

  The licenses for most software are designed to take away your
freedom to share and change it.  By contrast, the GNU General Public
License is intended to guarantee your freedom to share and change free
software--to make sure the software is free for all its users.  This
General Public License applies to most of the Free Software
Foundation's software and to any other program whose authors commit to
using it.  (Some other Free Software Foundation software is covered by
the GNU Lesser General Public License instead.)  You can apply it to
your programs, too.

  When we speak of free software, we are referring to freedom, not
price.  Our General Public Licenses are designed to make sure that you
have the freedom to distribute copies of free software (and charge for
this service if you wish), that you receive source code or can get it
if you want it, that you can change the software or use pieces of it
in new free programs; and that you know you can do these things.

  To protect your rights, we need to make restrictions that forbid
anyone to deny you these rights or to ask you to surrender the rights.
These restrictions translate to certain responsibilities for you if you
distribute copies of the software, or if you modify it.

  For example, if you distribute copies of such a program, whether
gratis or for a fee, you must give the recipients all the rights that
you have.  You must make sure that they, too, receive or can get the
source code.  And you must show them these terms so they know their
rights.

  We protect your rights with two steps: (1) copyright the software, and
(2) offer you this license which gives you legal permission to copy,
distribute and/or modify the software.

  Also, for each author's protection and ours, we want to make certain
that everyone understands that there is no warranty for this free
software.  If the software is modified by someone else and passed on, we
want its recipients to know that what they have is not the original, so
that any problems introduced by others will not reflect on the original
authors' reputations.

  Finally, any free program is threatened constantly by software
patents.  We wish to avoid the danger that redistributors of a free
program will individually obtain patent licenses, in effect making the
program proprietary.  To prevent this, we have made it clear that any
patent must be licensed for everyone's free use or not licensed at all.

  The precise terms and conditions for copying, distribution and
modification follow.

                    GNU GENERAL PUBLIC LICENSE
   TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

  0. This License applies to any program or other work which contains
a notice placed by the copyright holder saying it may be distributed
under the terms of this General Public License.  The "Program", below,
refers to any such program or work, and a "work based on the Program"
means either the Program or any derivative work under copyright law:
that is to say, a work containing the Program or a portion of it,
either verbatim or with modifications and/or translated into another
language.  (Hereinafter, translation is included without limitation in
the term "modification".)  Each licensee is addressed as "you".

Activities other than copying, distribution and modification are not
covered by this License; they are outside its scope.  The act of
running the Program is not restricted, and the output from the Program
is covered only if its contents constitute a work based on the
Program (independent of having been made by running the Program).
Whether that is true depends on what the Program does.

  1. You may copy and distribute verbatim copies of the Program's
source code as you receive it, in any medium, provided that you
conspicuously and appropriately publish on each copy an appropriate
copyright notice and disclaimer of warranty; keep intact all the
notices that refer to this License and to the absence of any warranty;
and give any other recipients of the Program a copy of this License
along with the Program.

You may charge a fee for the physical act of transferring a copy, and
you may at your option offer warranty protection in exchange for a fee.

  2. You may modify your copy or copies of the Program or any portion
of it, thus forming a work based on the Program, and copy and
distribute such modifications or work under the terms of Section 1
above, provided that you also meet all of these conditions:

    a) You must cause the modified files to carry prominent notices
    stating that you changed the files and the date of any change.

    b) You must cause any work that you distribute or publish, that in
    whole or in part contains or is derived from the Program or any
    part thereof, to be licensed as a whole at no charge to all third
    parties under the terms of this License.

    c) If the modified program normally reads commands interactively
    when run, you must cause it, when started running for such
    interactive use in the most ordinary way, to print or display an
    announcement including an appropriate copyright notice and a
    notice that there is no warranty (or else, saying that you provide
    a warranty) and that users may redistribute the program under
    these conditions, and telling the user how to view a copy of this
    License.  (Exception: if the Program itself is interactive but
    does not normally print such an announcement, your work based on
    the Program is not required to print an announcement.)

These requirements apply to the modified work as a whole.  If
identifiable sections of that work are not derived from the Program,
and can be reasonably considered independent and separate works in
themselves, then this License, and its terms, do not apply to those
sections when you distribute them as separate works.  But when you
distribute the same sections as part of a whole which is a work based
on the Program, the distribution of the whole must be on the terms of
this License, whose permissions for other licensees extend to the
entire whole, and thus to each and every part regardless of who wrote it.

Thus, it is not the intent of this section to claim rights or contest
your rights to work written entirely by you; rather, the intent is to
exercise the right to control the distribution of derivative or
collective works based on the Program.

In addition, mere aggregation of another work not based on the Program
with the Program (or with a work based on the Program) on a volume of
a storage or distribution medium does not bring the other work under
the scope of this License.

  3. You may copy and distribute the Program (or a work based on it,
under Section 2) in object code or executable form under the terms of
Sections 1 and 2 above provided that you also do one of the following:

    a) Accompany it with the complete corresponding machine-readable
    source code, which must be distributed under the terms of Sections
    1 and 2 above on a medium customarily used for software interchange; or,

    b) Accompany it with a written offer, valid for at least three
    years, to give any third party, for a charge no more than your
    cost of physically performing source distribution, a complete
    machine-readable copy of the corresponding source code, to be
    distributed under the terms of Sections 1 and 2 above on a medium
    customarily used for software interchange; or,

    c) Accompany it with the information you received as to the offer
    to distribute corresponding source code.  (This alternative is
    allowed only for noncommercial distribution and only if you
    received the program in object code or executable form with such
    an offer, in accord with Subsection b above.)

The source code for a work means the preferred form of the work for
making modifications to it.  For an executable work, complete source
code means all the source code for all modules it contains, plus any
associated interface definition files, plus the scripts used to
control compilation and installation of the executable.  However, as a
special exception, the source code distributed need not include
anything that is normally distributed (in either source or binary
form) with the major components (compiler, kernel, and so on) of the
operating system on which the executable runs, unless that component
itself accompanies the executable.

If distribution of executable or object code is made by offering
access to copy from a designated place, then offering equivalent
access to copy the source code from the same place counts as
distribution of the source code, even though third parties are not
compelled to copy the source along with the object code.

  4. You may not copy, modify, sublicense, or distribute the Program
except as expressly provided under this License.  Any attempt
otherwise to copy, modify, sublicense or distribute the Program is
void, and will automatically terminate your rights under this License.
However, parties who have received copies, or rights, from you under
this License will not have their licenses terminated so long as such
parties remain in full compliance.

  5. You are not required to accept this License, since you have not
signed it.  However, nothing else grants you permission to modify or
distribute the Program or its derivative works.  These actions are
prohibited by law if you do not accept this License.  Therefore, by
modifying or distributing the Program (or any work based on the
Program), you indicate your acceptance of this License to do so, and
all its terms and conditions for copying, distributing or modifying
the Program or works based on it.

  6. Each time you redistribute the Program (or any work based on the
Program), the recipient automatically receives a license from the
original licensor to copy, distribute or modify the Program subject to
these terms and conditions.  You may not impose any further
restrictions on the recipients' exercise of the rights granted herein.
You are not responsible for enforcing compliance by third parties to
this License.

  7. If, as a consequence of a court judgment or allegation of patent
infringement or for any other reason (not limited to patent issues),
conditions are imposed on you (whether by court order, agreement or
otherwise) that contradict the conditions of this License, they do not
excuse you from the conditions of this License.  If you cannot
distribute so as to satisfy simultaneously your obligations under this
License and any other pertinent obligations, then as a consequence you
may not distribute the Program at all.  For example, if a patent
license would not permit royalty-free redistribution of the Program by
all those who receive copies directly or indirectly through you, then
the only way you could satisfy both it and this License would be to
refrain entirely from distribution of the Program.

If any portion of this section is held invalid or unenforceable under
any particular circumstance, the balance of the section is intended to
apply and the section as a whole is intended to apply in other
circumstances.

It is not the purpose of this section to induce you to infringe any
patents or other property right claims or to contest validity of any
such claims; this section has the sole purpose of protecting the
integrity of the free software distribution system, which is
implemented by public license practices.  Many people have made
generous contributions to the wide range of software distributed
through that system in reliance on consistent application of that
system; it is up to the author/donor to decide if he or she is willing
to distribute software through any other system and a licensee cannot
impose that choice.

This section is intended to make thoroughly clear what is believed to
be a consequence of the rest of this License.

  8. If the distribution and/or use of the Program is restricted in
certain countries either by patents or by copyrighted interfaces, the
original copyright holder who places the Program under this License
may add an explicit geographical distribution limitation excluding
those countries, so that distribution is permitted only in or among
countries not thus excluded.  In such case, this License incorporates
the limitation as if written in the body of this License.

  9. The Free Software Foundation may publish revised and/or new versions
of the General Public License from time to time.  Such new versions will
be similar in spirit to the present version, but may differ in detail to
address new problems or concerns.

Each version is given a distinguishing version number.  If the Program
specifies a version number of this License which applies to it and "any
later version", you have the option of following the terms and conditions
either of that version or of any later version published by the Free
Software Foundation.  If the Program does not specify a version number of
this License, you may choose any version ever published by the Free Software
Foundation.

  10. If you wish to incorporate parts of the Program into other free
programs whose distribution conditions are different, write to the author
to ask for permission.  For software which is copyrighted by the Free
Software Foundation, write to the Free Software Foundation; we sometimes
make exceptions for this.  Our decision will be guided by the two goals
of preserving the free status of all derivatives of our free software and
of promoting the sharing and reuse of software generally.

                            NO WARRANTY

  11. BECAUSE THE PROGRAM IS LICENSED FREE OF CHARGE, THERE IS NO WARRANTY
FOR THE PROGRAM, TO THE EXTENT PERMITTED BY APPLICABLE LAW.  EXCEPT WHEN
OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR OTHER PARTIES
PROVIDE THE PROGRAM "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED
OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.  THE ENTIRE RISK AS
TO THE QUALITY AND PERFORMANCE OF THE PROGRAM IS WITH YOU.  SHOULD THE
PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF ALL NECESSARY SERVICING,
REPAIR OR CORRECTION.

  12. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING
WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY AND/OR
REDISTRIBUTE THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES,
INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING
OUT OF THE USE OR INABILITY TO USE THE PROGRAM (INCLUDING BUT NOT LIMITED
TO LOSS OF DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY
YOU OR THIRD PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER
PROGRAMS), EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE
POSSIBILITY OF SUCH DAMAGES.

                     END OF TERMS AND CONDITIONS

            How to Apply These Terms to Your New Programs

  If you develop a new program, and you want it to be of the greatest
possible use to the public, the best way to achieve this is to make it
free software which everyone can redistribute and change under these terms.

  To do so, attach the following notices to the program.  It is safest
to attach them to the start of each source file to most effectively
convey the exclusion of warranty; and each file should have at least
the "copyright" line and a pointer to where the full notice is found.

    {description}
    Copyright (C) {year}  {fullname}

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License along
    with this program; if not, write to the Free Software Foundation, Inc.,
    51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.

Also add information on how to contact you by electronic and paper mail.

If the program is interactive, make it output a short notice like this
when it starts in an interactive mode:

    Gnomovision version 69, Copyright (C) year name of author
    Gnomovision comes with ABSOLUTELY NO WARRANTY; for details type `show w'.
    This is free software, and you are welcome to redistribute it
    under certain conditions; type `show c' for details.

The hypothetical commands `show w' and `show c' should show the appropriate
parts of the General Public License.  Of course, the commands you use may
be called something other than `show w' and `show c'; they could even be
mouse-clicks or menu items--whatever suits your program.

You should also get your employer (if you work as a programmer) or your
school, if any, to sign a "copyright disclaimer" for the program, if
necessary.  Here is a sample; alter the names:

  Yoyodyne, Inc., hereby disclaims all copyright interest in the program
  `Gnomovision' (which makes passes at compilers) written by James Hacker.

  {signature of Ty Coon}, 1 April 1989
  Ty Coon, President of Vice

This General Public License does not permit incorporating your program into
proprietary programs.  If your program is a subroutine library, you may
consider it more useful to permit linking proprietary applications with the
library.  If this is what you want to do, use the GNU Lesser General
Public License instead of this License.
</file>

<file path="package.json">
{
	"private": true,
	"devDependencies": {
		"@_tw/typography": "^0.5.17",
		"@tailwindcss/postcss": "^4.0.14",
		"@wordpress/prettier-config": "^4.20.0",
		"adm-zip": "^0.5.16",
		"archiver": "^7.0.1",
		"cross-env": "^7.0.3",
		"esbuild": "^0.25.1",
		"eslint": "^9.22.0",
		"eslint-config-prettier": "^10.1.1",
		"npm-run-all": "^4.1.5",
		"postcss": "^8.5.3",
		"postcss-cli": "^11.0.1",
		"postcss-nesting": "^13.0.1",
		"postcss-url": "^10.1.3",
		"prettier": "^3.5.3",
		"prettier-plugin-tailwindcss": "^0.6.11",
		"tailwindcss": "^4.0.14"
	},
	"scripts": {
		"development:tailwind:frontend": "npx postcss ./tailwind.css -o ./theme/style.css --verbose",
		"development:tailwind:editor": "cross-env _TW_TARGET=editor npx postcss ./tailwind/tailwind-editor.css -o ./theme/style-editor.css",
		"development:tailwind:editor:extra": "npx postcss ./tailwind/tailwind-editor-extra.css -o ./theme/style-editor-extra.css",
		"development:esbuild": "npx esbuild ./javascript/script.js ./javascript/block-editor.js --target=esnext --bundle --outdir=./theme/js --out-extension:.js=.min.js",
		"development": "run-p \"development:**\"",
		"dev": "npm run development",
		"watch:tailwind:frontend": "npm run development:tailwind:frontend -- --watch",
		"watch:tailwind:editor": "npm run development:tailwind:editor -- --watch",
		"watch:tailwind:editor:extra": "npm run development:tailwind:editor:extra -- --watch",
		"watch:esbuild": "npm run development:esbuild -- --watch",
		"watch": "run-p \"watch:**\"",
		"lint:eslint": "npx eslint",
		"lint:prettier": "npx prettier --check .",
		"lint": "run-p \"lint:*\"",
		"lint-fix:eslint": "npx eslint --fix",
		"lint-fix:prettier": "npx prettier --write .",
		"lint-fix": "run-p \"lint-fix:*\"",
		"production:tailwind:frontend": "npm run development:tailwind:frontend -- --minify",
		"production:tailwind:editor": "npm run development:tailwind:editor -- --minify",
		"production:tailwind:editor:extra": "npm run development:tailwind:editor:extra -- --minify",
		"production:esbuild": "npm run development:esbuild -- --minify",
		"production": "run-p \"production:**\"",
		"prod": "npm run production",
		"zip": "node node_scripts/zip.js _uw-theme",
		"bundle": "run-s production zip"
	}
}
</file>

<file path="phpcs.xml.dist">
<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="_uw-theme" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/squizlabs/PHP_CodeSniffer/master/phpcs.xsd">

	<description>A set of rules to check for a custom WordPress theme</description>
	<!-- Based on the `phpcs.xml.dist.sample` file from the WordPress Coding Standards. -->

	<!-- Pass some flags to PHPCS:
		 p flag: Show progress of the run.
		 s flag: Show sniff codes in all reports.
	-->
	<arg value="ps"/>

	<!-- Strip the file paths down to the relevant bit. -->
	<arg name="basepath" value="./"/>

	<!-- Check up to 8 files simultaneously. -->
	<arg name="parallel" value="8"/>

	<!-- Check PHP files only. JavaScript and CSS files are checked separately. -->
	<arg name="extensions" value="php"/>

	<!-- Check all files in this directory and the directories below it. -->
	<file>.</file>

	<!-- Exclude patterns. -->
	<exclude-pattern>/vendor/*</exclude-pattern>
	<exclude-pattern>/node_modules/*</exclude-pattern>

	<!-- Include the WordPress-Extra standard. -->
	<rule ref="WordPress-Extra">
		<!--
		We may want a middle ground, though. The best way to do this is to add
		the entire ruleset, then remove rules that don't suit a project. We can
		do this by running `phpcs` with the '-s' flag, which allows us to see
		the names of the sniffs reporting errors.

		Once we know the sniff names, we can opt to exclude sniffs which don't
		suit our project.

		The examples below demonstrate how you can exclude rules. They are not
		intended as advice about which sniffs to exclude.
		-->

		<!--
		<exclude name="WordPress.WhiteSpace.ControlStructureSpacing"/>
		<exclude name="WordPress.Security.EscapeOutput"/>
		-->
	</rule>

	<!-- Let's also check that everything is properly documented. -->
	<rule ref="WordPress-Docs"/>

	<!-- Check for PHP cross-version compatibility. -->
	<config name="testVersion" value="7.4-"/>
	<rule ref="PHPCompatibilityWP"/>

	<!--
	To get the optimal benefits of using the WordPress Coding Standards, we
	should add a couple of custom properties.

	For information on additional custom properties available, check out
	the wiki:
	https://github.com/WordPress/WordPress-Coding-Standards/wiki/Customizable-sniff-properties
	-->
	<config name="minimum_supported_wp_version" value="6.2"/>

	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array">
				<element value="_uw-theme"/>
			</property>
		</properties>
	</rule>

	<rule ref="WordPress.NamingConventions.PrefixAllGlobals">
		<properties>
			<property name="prefixes" type="array">
				<element value="_uw_theme"/>
			</property>
		</properties>
	</rule>

	<rule ref="WordPress.Files.FileName">
		<properties>
			<property name="is_theme" value="true"/>
		</properties>
	</rule>

</ruleset>
</file>

<file path="postcss.config.mjs">
export default {
	plugins: {
		'@tailwindcss/postcss': {},
		'postcss-nesting': {},

		/**
		 * This is a hopefully temporary workaround to prevent `url()` values
		 * from being rebased to the document root for bundlers by
		 * `@tailwindcss/postcss`. See:
		 * https://github.com/tailwindlabs/tailwindcss/pull/16965
		 */
		'postcss-url': {
			url: function (asset) {
				const prefixesToStrip = [
					'./tailwind/custom/components/',
					'./tailwind/custom/',
					'./tailwind/',
				];

				for (const prefix of prefixesToStrip) {
					if (asset.url.startsWith(prefix)) {
						return asset.url.replace(prefix, '');
					}
				}

				return asset.url;
			},
		},
	},
};
</file>

<file path="prettier.config.mjs">
import wordpressPrettierConfig from "@wordpress/prettier-config";

export default {
	...wordpressPrettierConfig,
	plugins: ["prettier-plugin-tailwindcss"],
};
</file>

<file path="README.md">
\_uw-theme
==========

Custom sablona pro DUM A BYT

## Quickstart

### Installation

1. Move this folder to `wp-content/themes` in your local development environment
2. Run `npm install && npm run dev` in this folder
3. Activate this theme in your local WordPress installation

### Development

4. Run `npm run watch`
5. Add [Tailwind utility classes](https://tailwindcss.com/docs/utility-first) with abandon

### Deployment

6. Run `npm run bundle`
7. Upload the resulting zip file to your site using the “Upload Theme” button on the “Add Themes” administration page

Or [deploy with the tool of your choice](https://underscoretw.com/docs/deployment/#h-other-deployment-options)!

## Full Documentation

### Fundamentals

* [Installation](https://underscoretw.com/docs/installation/)  
  Generate your custom theme, install it in WordPress and run your first Tailwind builds
* [Development](https://underscoretw.com/docs/development/)  
  Watch for changes, build for production and learn more about how _tw, WordPress and Tailwind work together
* [Deployment](https://underscoretw.com/docs/deployment/)  
  Share your new WordPress theme with the world
* [Troubleshooting](https://underscoretw.com/docs/troubleshooting/)  
  Find solutions to potential issues and answers to frequently asked questions

### In Depth

* [Using Tailwind Typography](https://underscoretw.com/docs/tailwind-typography/)  
  Customize front-end and back-end typographic styles
* [JavaScript Bundling with esbuild](https://underscoretw.com/docs/esbuild/)  
  Install and bundle JavaScript libraries (very quickly)
* [Adding custom fonts](https://underscoretw.com/docs/custom-fonts/)
  Host your fonts yourself or use a third party—and then add those fonts to your WordPress theme
* [Linting and Code Formatting](https://underscoretw.com/docs/linting-code-formatting/)  
  Catch bugs and stop thinking about formatting
* [Keeping your theme up-to-date](https://underscoretw.com/docs/updating/)
  How to update (and whether or not you should)

### Extras

* [On Tailwind and WordPress](https://underscoretw.com/docs/wordpress-tailwind/)  
  Understand how WordPress and Tailwind work together
* [Styling HTML from outside the theme](https://underscoretw.com/docs/styling-html-from-outside-the-theme/)
  Work with WordPress core, plugins and JavaScript libraries
* [Managing Styles for Custom Blocks](https://underscoretw.com/docs/custom-blocks/)  
  Learn strategies for using Tailwind in theme-specific custom blocks
* [Setting Up Browsersync](https://underscoretw.com/docs/browsersync/)  
  Add live reloads and synchronized cross-device testing to your workflow
</file>

<file path="tailwind.css">
/**
 * This file outputs to `style.css`, your main front-end style sheet.
 *
 * You probably won’t need to edit this file. You’re more likely to want to
 * edit `./tailwind/tailwind-theme.css`, which contains your project’s custom
 * design tokens, or files in the `./tailwind/custom` folder.
 */

/**
 * Because this file shares code with `tailwind-editor.css`, we import the
 * shared code to avoid duplication.
 */
@import "./tailwind/partials/header.css";

/**
 * Add Tailwind’s Preflight styles followed by your custom base styles.
 */
@import "tailwindcss/preflight";
@import "./tailwind/custom/base.css";

/**
 * Because this file shares code with `tailwind-editor.css`, we import the
 * shared code to avoid duplication.
 */
@import "./tailwind/partials/footer.css";
</file>

</files>
