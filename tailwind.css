/**
 * This file outputs to `style.css`, your main front-end style sheet.
 *
 * You probably won’t need to edit this file. You’re more likely to want to
 * edit `./tailwind/tailwind-theme.css`, which contains your project’s custom
 * design tokens, or files in the `./tailwind/custom` folder.
 */

/**
 * Because this file shares code with `tailwind-editor.css`, we import the
 * shared code to avoid duplication.
 */
@import "./tailwind/partials/header.css";
@import "./tailwind/partials/single.css";

/**
 * Add Tailwind’s Preflight styles followed by your custom base styles.
 */
@import "tailwindcss/preflight";
@import "./tailwind/custom/base.css";

/**
 * Because this file shares code with `tailwind-editor.css`, we import the
 * shared code to avoid duplication.
 */
@import "./tailwind/partials/footer.css";
