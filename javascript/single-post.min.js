document.addEventListener("DOMContentLoaded",function(){let o=document.getElementById("reading-progress");o&&window.addEventListener("scroll",function(){var e=window.innerHeight,e=document.documentElement.scrollHeight-e,t=window.pageYOffset||document.documentElement.scrollTop,e=t/e*100;o.style.width=e+"%",o.parentElement.style.opacity=50<t?"1":"0"});document.querySelectorAll('.toc a[href^="#"]').forEach(e=>{e.addEventListener("click",function(e){e.preventDefault();e=this.getAttribute("href").substring(1);let t=document.getElementById(e);t&&(window.scrollTo({top:t.offsetTop-20,behavior:"smooth"}),t.classList.add("highlight-target"),setTimeout(()=>{t.classList.remove("highlight-target")},2e3))})});var e=document.querySelectorAll(".gallery-item a"),e=(0<e.length&&void 0!==window.SimpleLightbox&&new SimpleLightbox(".gallery-item a",{captionPosition:"bottom",captionsData:"alt",enableKeyboard:!0,showCounter:!0,loop:!0,animationSpeed:300,swipeClose:!0,navText:["&larr;","&rarr;"],closeText:"&times;",widthRatio:.9,heightRatio:.9}),document.querySelector(".slideshow-container"));if(e){let i=e.querySelector(".flex");var l=e.querySelector(".slider-prev"),e=e.querySelector(".slider-next");if(i&&l&&e){let t=162,o=(l.classList.add("opacity-100"),e.classList.add("opacity-100"),e.addEventListener("click",()=>{i.scrollLeft+=324}),l.addEventListener("click",()=>{i.scrollLeft-=324}),0),n;i.addEventListener("touchstart",e=>{o=e.changedTouches[0].screenX}),i.addEventListener("touchend",e=>{(n=e.changedTouches[0].screenX)<o&&(i.scrollLeft+=t),n>o&&(i.scrollLeft-=t)})}}});