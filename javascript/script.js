/**
 * Super simple JavaScript for WordPress theme
 * 
 * Minimal JavaScript to handle the mobile menu toggle and sticky navigation
 */

document.addEventListener('DOMContentLoaded', function() {
  // Simple mobile menu toggle
  const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      // Toggle mobile menu visibility using CSS classes
      const isExpanded = mobileMenuToggle.getAttribute('aria-expanded') === 'true';
      
      if (!isExpanded) {
        mobileMenuToggle.setAttribute('aria-expanded', 'true');
        mobileMenuToggle.classList.add('is-active');
        mobileMenu.classList.add('visible');
      } else {
        mobileMenuToggle.setAttribute('aria-expanded', 'false');
        mobileMenuToggle.classList.remove('is-active');
        mobileMenu.classList.remove('visible');
      }
    });
  }
  
  // Mobile submenu functionality - add toggle buttons only on the right
  const menuItemsWithChildren = document.querySelectorAll('#mobile-menu .menu-item-has-children');
  
  menuItemsWithChildren.forEach(function(item) {
    // Create toggle button with proper positioning
    const toggleButton = document.createElement('button');
    toggleButton.className = 'mobile-submenu-toggle';
    toggleButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>';
    toggleButton.setAttribute('aria-expanded', 'false');
    
    // Add the toggle button after the link
    const itemLink = item.querySelector(':scope > a');
    
    if (itemLink) {
      itemLink.after(toggleButton);
    }
    
    // Toggle submenu visibility
    toggleButton.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const submenu = item.querySelector('.sub-menu');
      if (submenu) {
        const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';
        
        if (!isExpanded) {
          // Open submenu
          const scrollHeight = submenu.scrollHeight;
          submenu.style.maxHeight = scrollHeight + 'px';
          toggleButton.setAttribute('aria-expanded', 'true');
          toggleButton.classList.add('is-active');
          item.classList.add('has-active-submenu');
        } else {
          // Close submenu
          submenu.style.maxHeight = '0';
          toggleButton.setAttribute('aria-expanded', 'false');
          toggleButton.classList.remove('is-active');
          item.classList.remove('has-active-submenu');
        }
      }
    });
    
    // Initialize submenu as closed
    const submenu = item.querySelector('.sub-menu');
    if (submenu) {
      submenu.style.maxHeight = '0';
    }
  });

  // Sticky navigation functionality - now only for the navigation
  const mainNavigation = document.getElementById('site-navigation');
  const body = document.body;
  let navOffset = 0;
  let navHeight = 0;
  
  if (mainNavigation) {
    // Get the initial offset of the navigation
    navOffset = mainNavigation.offsetTop;
    
    // Measure and set the navigation height as a CSS variable
    navHeight = mainNavigation.offsetHeight;
    document.documentElement.style.setProperty('--nav-height', navHeight + 'px');
    
    // Function to toggle sticky class based on scroll position
    function toggleStickyNav() {
      if (window.pageYOffset > navOffset) {
        mainNavigation.classList.add('sticky-nav');
        body.classList.add('has-sticky-nav');
      } else {
        mainNavigation.classList.remove('sticky-nav');
        body.classList.remove('has-sticky-nav');
      }
    }
    
    // Initial check on page load
    toggleStickyNav();
    
    // Add scroll event listener
    window.addEventListener('scroll', toggleStickyNav);
    
    // Update nav height and offset on window resize
    window.addEventListener('resize', function() {
      // Reset body padding
      body.classList.remove('has-sticky-nav');
      
      // Temporarily remove sticky class to get true offset
      mainNavigation.classList.remove('sticky-nav');
      
      // Recalculate values after brief timeout to ensure accurate measurements
      setTimeout(function() {
        navOffset = mainNavigation.offsetTop;
        navHeight = mainNavigation.offsetHeight;
        document.documentElement.style.setProperty('--nav-height', navHeight + 'px');
        
        // Reapply sticky state if needed
        toggleStickyNav();
      }, 100);
    });
  }
});
