/**
 * JavaScript functionality for single post template
 */

document.addEventListener('DOMContentLoaded', function() {
    // Reading Progress Bar
    const progressBar = document.getElementById('reading-progress');
    if (progressBar) {
        window.addEventListener('scroll', function() {
            // Calculate scroll position
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight - windowHeight;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollPercent = (scrollTop / documentHeight) * 100;
            
            // Update progress bar width
            progressBar.style.width = scrollPercent + '%';
            
            // Show progress bar only when scrolling has started
            if (scrollTop > 50) {
                progressBar.parentElement.style.opacity = '1';
            } else {
                progressBar.parentElement.style.opacity = '0';
            }
        });
    }
    
    // Smooth scroll for TOC links
    const tocLinks = document.querySelectorAll('.toc a[href^="#"]');
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                // Add smooth scroll
                window.scrollTo({
                    top: targetElement.offsetTop - 20,
                    behavior: 'smooth'
                });
                
                // Add highlight effect to target heading
                targetElement.classList.add('highlight-target');
                setTimeout(() => {
                    targetElement.classList.remove('highlight-target');
                }, 2000);
            }
        });
    });
    
    // Initialize SimpleLightbox for gallery images
    const galleryElements = document.querySelectorAll('.gallery-item a');
    if (galleryElements.length > 0 && typeof window.SimpleLightbox !== 'undefined') {
        // Initialize SimpleLightbox on gallery items
        // eslint-disable-next-line no-unused-vars, no-undef
        new SimpleLightbox('.gallery-item a', {
            captionPosition: 'bottom',
            captionsData: 'alt',
            enableKeyboard: true,
            showCounter: true,
            loop: true,
            animationSpeed: 300,
            swipeClose: true,
            navText: ['&larr;', '&rarr;'],
            closeText: '&times;',
            widthRatio: 0.9,
            heightRatio: 0.9,
            alertError: false,
            alertErrorMessage: ''
        });
    }
    
    // Initialize gallery carousel with direct DOM manipulation
    const galleryContainer = document.querySelector('.slideshow-container');
    if (galleryContainer) {
        // Get elements
        const slidesWrapper = galleryContainer.querySelector('.flex');
        const prevButton = galleryContainer.querySelector('.slider-prev');
        const nextButton = galleryContainer.querySelector('.slider-next');
        
        if (slidesWrapper && prevButton && nextButton) {
            // Set scroll amount - width of one gallery item plus margin (150px + 12px)
            const scrollAmount = 162;
            
            // Make buttons always visible for better UX
            prevButton.classList.add('opacity-100');
            nextButton.classList.add('opacity-100');
            
            // Handle next button click
            nextButton.addEventListener('click', () => {
                // Simple and direct approach - scroll right
                slidesWrapper.scrollLeft += scrollAmount * 2; // Scroll 2 items at once
            });
            
            // Handle previous button click
            prevButton.addEventListener('click', () => {
                // Simple and direct approach - scroll left
                slidesWrapper.scrollLeft -= scrollAmount * 2; // Scroll 2 items at once
            });
            
            // For mobile/touch screens - add swipe detection
            let touchStartX = 0;
            let touchEndX = 0;
            
            slidesWrapper.addEventListener('touchstart', (e) => {
                touchStartX = e.changedTouches[0].screenX;
            });
            
            slidesWrapper.addEventListener('touchend', (e) => {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });
            
            function handleSwipe() {
                if (touchEndX < touchStartX) {
                    // Swipe left - go next
                    slidesWrapper.scrollLeft += scrollAmount;
                }
                if (touchEndX > touchStartX) {
                    // Swipe right - go previous
                    slidesWrapper.scrollLeft -= scrollAmount;
                }
            }
        }
    }
});
