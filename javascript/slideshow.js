/**
 * Custom slideshow functionality for the homepage
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get all slideshow containers
    const slideshowContainers = document.querySelectorAll('.slideshow-container');
    
    // Initialize each slideshow
    slideshowContainers.forEach(container => {
        const slides = container.querySelectorAll('.slide');
        const dots = container.querySelectorAll('.slider-dot');
        const prevButton = container.querySelector('.slider-prev');
        const nextButton = container.querySelector('.slider-next');
        let currentSlide = 0;
        let slideInterval;
        const autoPlayDelay = 5000; // 5 seconds delay for autoplay
        
        // Function to show a specific slide
        function showSlide(n) {
            // Reset current slide
            slides[currentSlide].classList.remove('opacity-100', 'translate-x-0');
            slides[currentSlide].classList.add('opacity-0', 'translate-x-full');
            
            // Update dots if they exist
            if (dots.length > 0) {
                dots[currentSlide].classList.remove('bg-white/80');
                dots[currentSlide].classList.add('bg-white/30');
            }
            
            // Set new current slide
            currentSlide = (n + slides.length) % slides.length;
            
            // Show new current slide
            slides[currentSlide].classList.remove('opacity-0', 'translate-x-full');
            slides[currentSlide].classList.add('opacity-100', 'translate-x-0');
            
            // Update dots for new slide
            if (dots.length > 0) {
                dots[currentSlide].classList.remove('bg-white/30');
                dots[currentSlide].classList.add('bg-white/80');
            }
        }
        
        // Function to go to next slide
        function nextSlide() {
            showSlide(currentSlide + 1);
        }
        
        // Function to go to previous slide
        function prevSlide() {
            showSlide(currentSlide - 1);
        }
        
        // Set up autoplay
        function startAutoplay() {
            slideInterval = setInterval(nextSlide, autoPlayDelay);
        }
        
        // Clear autoplay
        function stopAutoplay() {
            clearInterval(slideInterval);
        }
        
        // Add event listeners for dots navigation
        if (dots.length > 0) {
            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    stopAutoplay();
                    showSlide(index);
                    startAutoplay();
                });
            });
        }
        
        // Add event listeners for arrow buttons
        if (prevButton && nextButton) {
            prevButton.addEventListener('click', () => {
                stopAutoplay();
                prevSlide();
                startAutoplay();
            });
            
            nextButton.addEventListener('click', () => {
                stopAutoplay();
                nextSlide();
                startAutoplay();
            });
        }
        
        // Pause autoplay on hover
        container.addEventListener('mouseenter', stopAutoplay);
        container.addEventListener('mouseleave', startAutoplay);
        
        // Start autoplay
        startAutoplay();
    });
});
