<?php
/**
 * The template for displaying category archives
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package dumabyt
 */

get_header();

// Get current category
$category = get_queried_object();
$category_slug = $category->slug;
$category_name = $category->name;
$category_description = $category->description;
$category_url = esc_url(get_category_link($category->term_id));

// Get category color classes
$color_class = dumabyt_get_category_color($category_slug);
$gradient_class = dumabyt_get_category_gradient($category_slug);
$text_color = str_replace('bg-', 'text-', $color_class);
$hover_color = str_replace('bg-', 'hover:bg-', $color_class);
$border_color = str_replace('bg-', 'border-', $color_class);
$text_hover_color = str_replace('bg-', 'hover:text-', $color_class);

// Get featured post (most recent with image)
$featured_post = dumabyt_get_category_articles($category_slug, 1, 0, true);

// Get remaining posts (skip the featured one on first page)
$posts_per_page = 18; // 3x6 grid

// Try different ways to get the current page number for custom URL structures
$paged = get_query_var('paged') ? get_query_var('paged') : 1;
if ($paged <= 1) {
    $paged = get_query_var('page') ? get_query_var('page') : 1;
}

// Handle custom URL structure with /page/X/ format
$request_uri = $_SERVER['REQUEST_URI'];
if (preg_match('/\/page\/(\d+)\/?$/', $request_uri, $matches)) {
    $paged = intval($matches[1]);
}

// Override for development/testing - to be removed in production
if (isset($_GET['testpage'])) {
    $paged = intval($_GET['testpage']);
}

// Offset calculation - only on page 1 we skip the featured post
$offset = ($paged == 1) ? 1 : ($paged - 1) * $posts_per_page + (($paged > 1) ? 1 : 0);

// Always fetch the same number of posts per page
$current_posts_per_page = $posts_per_page;

// Get posts without requiring featured image
$category_posts = dumabyt_get_category_articles($category_slug, $current_posts_per_page, $offset, false);

// UNIVERSAL PAGINATION SOLUTION - NO HARDCODED VALUES

// Get an accurate post count using a direct database query
// This ensures correct counts even when WordPress taxonomy counts are incorrect
global $wpdb;
$query = "
    SELECT COUNT(DISTINCT p.ID) 
    FROM {$wpdb->posts} p
    LEFT JOIN {$wpdb->term_relationships} tr ON (p.ID = tr.object_id)
    LEFT JOIN {$wpdb->term_taxonomy} tt ON (tr.term_taxonomy_id = tt.term_taxonomy_id)
    LEFT JOIN {$wpdb->terms} t ON (tt.term_id = t.term_id)
    WHERE p.post_type = 'post' 
    AND p.post_status = 'publish'
    AND t.slug = %s
";
$sql_count = $wpdb->prepare($query, $category_slug);
$db_count = (int)$wpdb->get_var($sql_count);

// Use the direct database count as it's most reliable
$total_posts = $db_count;

// Calculate total pages taking featured post into account
$posts_for_pagination = $total_posts - ($paged == 1 ? 1 : 0); // Subtract featured post only on first page
$total_pages = ceil($posts_for_pagination / $posts_per_page);

// Ensure at least 1 page
$total_pages = max(1, $total_pages);

// Debug information for server logs
error_log(sprintf(
    "Category %s: WP Count=%d, DB Count=%d, Total Pages=%d", 
    $category_slug, $category->count, $db_count, $total_pages
));

// Add a prominent debug display in the HTML
echo '<!-- PAGINATION DEBUG INFORMATION
     Current URL: ' . $_SERVER['REQUEST_URI'] . '
     Category: ' . $category_name . ' (' . $category_slug . ')
     WP Count: ' . $category->count . '
     DB Count: ' . $db_count . '
     Final Count Used: ' . $total_posts . '
     Posts Per Page: ' . $posts_per_page . '
     Total Pages: ' . $total_pages . '
     Current Page: ' . $paged . '
     Offset: ' . $offset . '
     Featured Post Displayed: ' . ($paged == 1 ? 'Yes' : 'No') . '
     Show Pagination: ' . ($total_pages > 1 ? 'Yes' : 'No') . '
-->';
?>

<main id="primary" class="site-main bg-gray-50 py-8">
    <div class="container mx-auto px-4">

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Main content - 3 columns wide -->
            <div class="lg:col-span-3">
                <?php if ($featured_post->have_posts() && $paged == 1) : ?>
                    <!-- Featured Post -->
                    <div class="mb-8">
                        <h2 class="text-xl font-bold mb-4 text-gray-800 font-trajan">Nejnovější článek</h2>
                        <?php 
                        while ($featured_post->have_posts()) : 
                            $featured_post->the_post();
                            get_template_part('template-parts/content/content', 'category-featured', array(
                                'category_name' => $category_name,
                                'category_url' => $category_url,
                                'color_class' => $color_class,
                                'text_color' => $text_color,
                                'text_hover_color' => $text_hover_color
                            ));
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php endif; ?>

                <!-- Grid of Posts -->
                <?php if ($category_posts->have_posts()) : ?>
                    <div class="mb-8">
                        <h2 class="text-xl font-bold mb-4 text-gray-800 font-trajan">Další články</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php 
                            while ($category_posts->have_posts()) : 
                                $category_posts->the_post();
                                get_template_part('template-parts/content/content', 'category-card', array(
                                    'category_name' => $category_name,
                                    'category_url' => $category_url,
                                    'color_class' => $color_class,
                                    'text_color' => $text_color,
                                    'text_hover_color' => $text_hover_color
                                ));
                            endwhile;
                            wp_reset_postdata();
                            ?>
                        </div>
                    </div>

                    <!-- Pagination - Always displayed regardless of page count -->
                    <div class="mt-8 flex justify-center">
                        <div class="inline-flex rounded-md shadow-sm">
                            <?php 
                            /* Debug info about pagination conditions */
                            echo '<!-- Pagination Display Check: $total_pages = ' . $total_pages . ', Should Show: ' . ($total_pages > 1 ? 'Yes' : 'No') . ' -->'; 
                            ?>
                            <?php /* Only display pagination elements if there are actually multiple pages */ ?>
                            <?php if ($total_pages > 1) : ?>
                            <?php
                            // Previous page
                            if ($paged > 1) : 
                                // Create manual previous page link
                                $current_url = strtok($_SERVER["REQUEST_URI"], '?');
                                $current_url = rtrim($current_url, '/');
                                
                                // Remove any existing /page/X from the URL
                                $base_url = preg_replace('/\/page\/\d+$/', '', $current_url);
                                
                                // For page 2, link to the base URL without page number
                                if ($paged - 1 == 1) {
                                    $prev_link = $base_url . '/';
                                } else {
                                    $prev_link = $base_url . '/page/' . ($paged - 1) . '/';
                                }
                                
                                // Fallback to standard WordPress function
                                if (!$prev_link || $prev_link === '/') {
                                    $prev_link = get_pagenum_link($paged - 1);
                                }
                            ?>
                                <a href="<?php echo esc_url($prev_link); ?>" class="relative inline-flex items-center rounded-l-md px-3 py-2 text-gray-600 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                    </svg>
                                    <span class="sr-only">Předchozí</span>
                                </a>
                            <?php endif; ?>

                            <?php
                            // Page numbers
                            $start_page = max(1, min($paged - 2, $total_pages - 4));
                            $end_page = min($total_pages, max($paged + 2, 5));

                            for ($i = $start_page; $i <= $end_page; $i++) :
                                $is_current = $paged == $i;
                                
                                // Create manual page links if necessary
                                $current_url = strtok($_SERVER["REQUEST_URI"], '?');
                                $current_url = rtrim($current_url, '/');
                                
                                // Remove any existing /page/X from the URL
                                $base_url = preg_replace('/\/page\/\d+$/', '', $current_url);
                                
                                // Add page number for all except page 1
                                if ($i > 1) {
                                    $page_link = $base_url . '/page/' . $i . '/';
                                } else {
                                    $page_link = $base_url . '/';
                                }
                                
                                // Fallback to standard WordPress function
                                if (!$page_link || $page_link === '/') {
                                    $page_link = get_pagenum_link($i);
                                }
                                $classes = $is_current 
                                    ? "relative z-10 inline-flex items-center $color_class px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2" 
                                    : "relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0";
                            ?>
                                <a href="<?php echo esc_url($page_link); ?>" class="<?php echo $classes; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php
                            // Next page
                            if ($paged < $total_pages) : 
                                // Create manual next page link
                                $current_url = strtok($_SERVER["REQUEST_URI"], '?');
                                $current_url = rtrim($current_url, '/');
                                
                                // Remove any existing /page/X from the URL
                                $base_url = preg_replace('/\/page\/\d+$/', '', $current_url);
                                
                                // Create next page link
                                $next_link = $base_url . '/page/' . ($paged + 1) . '/';
                                
                                // Fallback to standard WordPress function
                                if (!$next_link || $next_link === '/') {
                                    $next_link = get_pagenum_link($paged + 1);
                                }
                            ?>
                                <a href="<?php echo esc_url($next_link); ?>" class="relative inline-flex items-center rounded-r-md px-3 py-2 text-gray-600 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">
                                    <span class="sr-only">Další</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            <?php endif; ?>
                            <?php endif; /* End of pagination elements if check */ ?>
                        </div>
                    </div>
                <?php else : ?>
                    <div class="bg-white rounded-xl p-8 text-center">
                        <h2 class="text-xl font-bold mb-4 text-gray-800">Žádné články</h2>
                        <p class="text-gray-600">V této kategorii zatím nejsou žádné články.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar - 1 column wide -->
            <?php get_template_part('template-parts/layout/sidebar', 'partneri'); ?>
        </div>
    </div>
</main>

<?php
get_footer();
