<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package dumabyt
 */

get_header();
?>

<!-- Reading progress bar - fixed at top -->
<div class="progress-container">
    <div id="reading-progress" class="progress-bar"></div>
</div>

<!-- Main content area -->
<div class="bg-gray-50 py-8">
    <div class="container mx-auto px-4">
        <!-- Back to category link -->
        <?php if (!is_page()) : ?>
            <?php 
            $categories = get_the_category();
            if ($categories) :
                $category = $categories[0]; // Get the first category
                $category_color = 'bg-amber-600 hover:bg-amber-700';
                
                // Set color based on category slug - matching our category scheme from homepage
                if ($category->slug === 'interier') {
                    $category_color = 'bg-cyan-600 hover:bg-cyan-700';
                } elseif ($category->slug === 'stavba') {
                    $category_color = 'bg-emerald-600 hover:bg-emerald-700';
                } elseif ($category->slug === 'rekonstrukce') {
                    $category_color = 'bg-orange-600 hover:bg-orange-700';
                } elseif ($category->slug === 'zahrada') {
                    $category_color = 'bg-teal-600 hover:bg-teal-700';
                } elseif ($category->slug === 'blog') {
                    $category_color = 'bg-purple-600 hover:bg-purple-700';
                }
            ?>
            <div class="mb-4">
                <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="inline-flex items-center text-sm <?php echo $category_color; ?> text-white px-3 py-1 rounded-full transition-colors shadow-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                    Zpět na <?php echo esc_html($category->name); ?>
                </a>
            </div>
            <?php endif; ?>
        <?php endif; ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Main content - 3 columns wide -->
            <div class="lg:col-span-3">
                <main id="main" class="bg-white rounded-xl shadow-sm overflow-hidden card-elevation">
                    <?php
                    /* Start the Loop */
                    while ( have_posts() ) :
                        the_post();
                        get_template_part( 'template-parts/content/content', 'single' );

                        if ( is_singular( 'post' ) ) {
                            // Previous/next post navigation with thumbnails
                            $prev_post = get_previous_post();
                            $next_post = get_next_post();
                            
                            if ($prev_post || $next_post) :
                            ?>
                            <div class="px-6 pb-6">
                                <div class="border-t border-gray-100 pt-8 mt-6">
                                    <h3 class="text-lg font-bold text-gray-800 mb-4 font-trajan">Pokračujte ve čtení</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <?php if ($prev_post) : ?>
                                            <a href="<?php echo esc_url(get_permalink($prev_post->ID)); ?>" class="group border border-gray-100 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                                                <div class="flex items-start h-full bg-gradient-to-r from-gray-50 to-white">
                                                    <div class="flex-shrink-0 w-24 h-24 relative overflow-hidden bg-gray-100">
                                                        <?php if (has_post_thumbnail($prev_post->ID)) : ?>
                                                            <?php echo get_the_post_thumbnail($prev_post->ID, 'thumbnail', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-300')); ?>
                                                        <?php endif; ?>
                                                        <div class="absolute inset-0 flex items-center justify-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                                            </svg>
                                                        </div>
                                                    </div>
                                                    <div class="p-3">
                                                        <span class="text-xs uppercase text-gray-500 block mb-1">Předchozí článek</span>
                                                        <h4 class="text-sm font-medium text-gray-800 group-hover:text-amber-700 transition-colors line-clamp-2"><?php echo esc_html(get_the_title($prev_post->ID)); ?></h4>
                                                    </div>
                                                </div>
                                            </a>
                                        <?php endif; ?>
                                        <?php if ($next_post) : ?>
                                            <a href="<?php echo esc_url(get_permalink($next_post->ID)); ?>" class="group border border-gray-100 rounded-lg overflow-hidden hover:shadow-md transition-shadow text-right">
                                                <div class="flex items-start h-full flex-row-reverse bg-gradient-to-l from-gray-50 to-white">
                                                    <div class="flex-shrink-0 w-24 h-24 relative overflow-hidden bg-gray-100">
                                                        <?php if (has_post_thumbnail($next_post->ID)) : ?>
                                                            <?php echo get_the_post_thumbnail($next_post->ID, 'thumbnail', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-300')); ?>
                                                        <?php endif; ?>
                                                        <div class="absolute inset-0 flex items-center justify-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                                            </svg>
                                                        </div>
                                                    </div>
                                                    <div class="p-3 text-right">
                                                        <span class="text-xs uppercase text-gray-500 block mb-1">Další článek</span>
                                                        <h4 class="text-sm font-medium text-gray-800 group-hover:text-amber-700 transition-colors line-clamp-2"><?php echo esc_html(get_the_title($next_post->ID)); ?></h4>
                                                    </div>
                                                </div>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php 
                            endif;
                        }

                        // If comments are open, or we have at least one comment, load
                        // the comment template.
                        if ( comments_open() || get_comments_number() ) {
                            comments_template();
                        }

                        // End the loop.
                    endwhile;
                    ?>
                </main><!-- #main -->
            </div>
            
            <!-- Sidebar - 1 column wide -->
            <?php get_template_part( 'template-parts/layout/sidebar', 'partneri' ); ?>
        </div>
    </div>
</div>

<?php
// Enqueue the single post script
wp_enqueue_script('dumabyt-single-post', get_template_directory_uri() . '/js/single-post.min.js', array(), '1.0', true);

get_footer();
