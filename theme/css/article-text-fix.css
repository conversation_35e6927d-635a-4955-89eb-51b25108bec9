/**
 * Opravy pro barvu textu v článku
 *
 * @package dumabyt
 */

/* Reset barvy textu v článku na černou */
.entry-content,
.entry-content p,
.entry-content div,
.entry-content ul,
.entry-content ol,
.entry-content li,
.entry-content h1,
.entry-content h2,
.entry-content h3,
.entry-content h4,
.entry-content h5,
.entry-content h6,
.entry-content blockquote,
.entry-content span,
.prose,
.prose p,
.prose div,
.prose span,
.prose ul,
.prose ol,
.prose li,
.article-content * {
    color: #374151 !important; /* <PERSON><PERSON><PERSON><PERSON>ed<PERSON>, blízká černé */
}

/* Výjimky pro odkazy */
.entry-content a,
.prose a {
    color: #2563eb !important; /* Modrá barva pro odkazy */
}

/* Výjimky pro jiné specifické prvky, které by měly mít jinou barvu */
.entry-content .has-text-color,
.prose .has-text-color {
    color: inherit !important;
}

/* <PERSON><PERSON><PERSON><PERSON>, že se barva nezmění při scrollování */
html, body {
    scroll-behavior: smooth;
}

/* Zabránit změně barvy při hoveru nebo focusu */
.entry-content *:hover,
.entry-content *:focus,
.prose *:hover,
.prose *:focus {
    color: #374151 !important;
}

/* Výjimka pro hover na odkazech */
.entry-content a:hover,
.prose a:hover {
    color: #1e40af !important; /* Tmavší modrá pro hover */
}

/* Přepíše jakékoliv inline styly */
[style*="color"] {
    color: #374151 !important;
}
