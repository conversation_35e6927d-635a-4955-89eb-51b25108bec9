/**
 * Styles for Table of Contents functionality
 */

/* Animation for highlighting the target section when clicking a TOC link */
@keyframes highlightFade {
    0%   { background-color: rgba(251, 191, 36, 0.2); }
    100% { background-color: transparent; }
}

.highlight-target {
    animation: highlightFade 2s ease-out;
}

/* Style for auto-detected headings in migrated content */
.auto-heading {
    font-weight: 600;
    font-size: 1.25rem;
    line-height: 1.4;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    color: #1f2937;
    position: relative;
}

/* Visual indicator when a heading has been identified */
.toc-target {
    position: relative;
}

.toc-target:before {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background: rgba(251, 191, 36, 0.2);
    position: absolute;
    bottom: -4px;
    left: 0;
}

/* Original TOC items styling - if needed to hide */
.original-toc-heading, 
.original-toc-items {
    /* Uncomment to hide original TOC in content
    display: none; */
}

/* Enhanced toc layout */
.toc ul {
    padding-left: 0;
    list-style: none;
}

.toc a {
    display: flex;
    align-items: center;
    padding: 3px 0;
    text-decoration: none;
    transition: color 0.2s, transform 0.2s;
}

.toc a:hover {
    transform: translateX(3px);
}

.toc a svg {
    flex-shrink: 0;
    transition: transform 0.2s;
}

.toc a:hover svg {
    transform: translateX(2px);
}

/* Make the TOC container sticky on larger screens */
@media (min-width: 768px) {
    .toc {
        position: sticky;
        top: 2rem;
    }
}
