/**
 * Extra agresivní opravy pro šířku článku
 */

/* Základní nastavení pro obsah článku */
.entry-content {
    max-width: 100% !important;
    width: 100% !important;
}

/* Oprava pro obsah generovaný přes the_content filter */
.entry-content > *,
.entry-content p,
.entry-content div,
.entry-content ul,
.entry-content ol,
.entry-content h1,
.entry-content h2,
.entry-content h3,
.entry-content h4,
.entry-content h5,
.entry-content h6,
.entry-content blockquote,
.entry-content figure,
.entry-content .wp-block,
.entry-content .wp-block-group {
    max-width: 100% !important;
    width: 100% !important;
}

/* Zrušen<PERSON> vešker<PERSON>ch prose limitů */
.prose,
.prose p,
.prose div,
.prose > *,
.prose *,
.prose.prose-lg,
.prose.prose-xl,
.prose.prose-2xl {
    max-width: none !important;
    width: 100% !important;
}

/* Odstranění všech <PERSON>rgan<PERSON>, kter<PERSON> by mohly z<PERSON><PERSON><PERSON><PERSON>vat r<PERSON><PERSON><PERSON><PERSON> */
.entry-content > p:first-child,
.entry-content > div:first-child {
    margin-top: 0 !important;
}

.entry-content > p:last-child,
.entry-content > div:last-child {
    margin-bottom: 0 !important;
}

/* Zru<PERSON>it veškeré velikosti sloupců uvnitř obsahu */
.entry-content .wp-block-columns {
    max-width: 100% !important;
    width: 100% !important;
}

/* Nejagresivnější způsob - aplikace na všechny potomky */
.entry-content * {
    max-width: none !important;
}

/* Centrování obrázků */
.entry-content img,
.entry-content figure,
.entry-content .wp-block-image {
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important;
    max-width: 800px !important;
}

.entry-content figure.wp-block-image {
    text-align: center !important;
}

.entry-content figure figcaption {
    text-align: center !important;
}
