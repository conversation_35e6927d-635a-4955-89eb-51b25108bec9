/**
 * <PERSON><PERSON>y pro vylep<PERSON><PERSON><PERSON> kome<PERSON>
 *
 * @package dumabyt
 */

/* <PERSON><PERSON><PERSON><PERSON> konte<PERSON> kome<PERSON> */
#comments {
    padding: 2rem;
    margin-top: 3rem;
    background-color: #fafafa;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Nadpis komentář<PERSON> */
.comment-reply-title,
#comments > h2 {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

/* Formulář pro komentáře */
#commentform {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background-color: white;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Pole formuláře */
#commentform input[type="text"],
#commentform input[type="email"],
#commentform textarea {
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;
    width: 100%;
    margin-bottom: 1rem;
    font-size: 1rem;
    transition: all 0.2s ease-in-out;
}

#commentform input[type="text"]:focus,
#commentform input[type="email"]:focus,
#commentform textarea:focus {
    outline: none;
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/* Větší textarea pro komentář */
#commentform textarea {
    min-height: 150px;
    resize: vertical;
}

/* Popisky formuláře */
#commentform label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 1rem;
    color: #374151;
}

/* Tlačítko odeslat */
#commentform .submit {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

/* Notifikace komentáře */
.comment-notes {
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    color: #6b7280;
    padding: 0.75rem;
    background-color: #f3f4f6;
    border-radius: 0.5rem;
}

/* Požadované pole */
.required {
    color: #ef4444;
}

/* Seznam komentářů */
.comment-list {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

/* Jednotlivé komentáře */
.comment {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    border-radius: 0.75rem;
}

/* Autor komentáře */
.comment-author {
    margin-right: 1rem;
}

.comment-author img {
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Obsah komentáře */
.comment-content {
    margin-top: 1rem;
    padding: 1rem;
    background-color: white;
    border-radius: 0.5rem;
    line-height: 1.6;
}

/* Oddělení komentářů */
.comment:not(:last-child) {
    margin-bottom: 1.5rem;
}

/* Odpověď na komentář */
.comment .children {
    margin-top: 1.5rem;
    margin-left: 2rem;
    padding-left: 1rem;
    border-left: 3px solid #e5e7eb;
}

/* Tlačítko odpovědět */
.comment-reply-link {
    display: inline-block;
    margin-top: 0.5rem;
    font-weight: 500;
    text-decoration: none;
}

/* Pole formuláře - správné mezery */
.comment-form-author,
.comment-form-email,
.comment-form-url {
    margin-bottom: 1.25rem;
}

/* Drobný text */
.comment-meta {
    margin-bottom: 1rem;
}

/* Informace o autorovi */
.comment-metadata {
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

/* Oprava pro mobilní zařízení */
@media (max-width: 640px) {
    #comments {
        padding: 1rem;
    }
    
    .comment {
        padding: 1rem;
    }
    
    .comment .children {
        margin-left: 1rem;
    }
}
