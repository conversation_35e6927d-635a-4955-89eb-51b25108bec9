/**
 * Enhanced menu styles for DŮM & BYT
 */

/* Header styles */
.site-header {
    position: relative;
    z-index: 1001; /* Increased to ensure it's above all other content */
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

/* Header sections */
.header-top-section {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-navigation-section {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Sticky header and navigation styles */
.sticky-header .header-top-section {
    display: none; /* Hide the top section when sticky */
}

.sticky-header .header-navigation-section {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(0);
    animation: smoothStickyHeader 0.3s ease-in-out;
    will-change: transform;
    width: 100%;
    z-index: 1001;
}

@keyframes smoothStickyHeader {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

/* Make all header components more compact in sticky mode */
.sticky-header .site-branding {
    padding: 0.2rem 0;
}

.sticky-header .logo-img {
    height: 35px;
}

/* Magazine widget responsive styling */
.magazine-top-widget {
    display: flex;
    align-items: center;
}

.magazine-top-widget img {
    height: auto;
    max-height: 75px;
    width: auto;
    transition: all 0.3s ease;
    object-fit: contain;
}

.sticky-header .magazine-top-widget img {
    max-height: 50px;
}

@media (max-width: 1023px) {
    .magazine-top-widget img {
        max-height: 60px;
    }
    
    .sticky-header .magazine-top-widget img {
        max-height: 45px;
    }
}

@media (max-width: 767px) {
    .magazine-top-widget img {
        max-height: 50px;
    }
    
    .sticky-header .magazine-top-widget img {
        max-height: 40px;
    }
}

/* Header right section */
.header-right-section {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-left: auto;
}

/* Improve mobile layout */
@media (max-width: 767px) {
    .header-content {
        justify-content: space-between;
    }
}

/* Adjust body padding when header is sticky */
body.has-sticky-header {
    padding-top: var(--header-height, 120px); /* Adjust based on your header height */
}

/* Site branding */
.site-branding {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.site-logo {
    flex-shrink: 0;
}

.logo-img {
    height: 40px;
    width: auto;
}

/* Utility navigation */
.utility-nav {
    display: flex;
    align-items: center;
}

.utility-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.utility-links li {
    margin-left: 1.5rem;
}

.utility-links a {
    color: #333;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.utility-links a:hover {
    color: var(--color-amber-600);
}

/* Search form */
.search-form {
    position: relative;
    margin-left: 1.5rem;
}

.search-field {
    padding: 0.5rem 2rem 0.5rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 2rem;
    font-size: 0.875rem;
    width: 200px;
    transition: all 0.3s ease;
}

.search-field:focus {
    outline: none;
    border-color: var(--color-amber-500);
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);
    width: 250px;
}

.search-submit {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 0;
    color: #666;
    cursor: pointer;
}

/* Main navigation */
.main-navigation {
    background-color: #fff;
    transition: all 0.3s ease;
    will-change: transform, box-shadow;
}

/* Sticky navigation - only applies to the nav menu */
.sticky-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    animation: slideDown 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* This class will be added to the body when nav is sticky to prevent content jump */
body.has-sticky-nav {
    padding-top: var(--nav-height, 60px);
}

/* Hide mobile menu when sticky on mobile devices to save space */
@media (max-width: 767px) {
    .sticky-nav .mobile-menu {
        top: 100%;
        max-height: calc(100vh - var(--nav-height, 60px));
    }
}

.main-navigation ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Zajištění, že pouze top-level menu je flex */
.main-navigation > ul {
    display: flex;
}

/* Submenu musí být vždy blokové, ne flex */
.main-navigation ul ul {
    display: block !important;
}

.main-navigation li {
    position: relative;
}

.main-navigation a {
    display: block;
    padding: 1rem 1.25rem;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.main-navigation a:hover {
    color: var(--color-amber-600);
}

/* Submenu indicator */
.submenu-indicator {
    display: inline-block;
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
}

.menu-item-has-children.submenu-active > a .submenu-indicator {
    transform: rotate(180deg);
}

/* Submenu wrapper */
.submenu-wrapper {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000; /* Increased z-index to ensure it's above page content */
    min-width: 320px;
    max-width: 400px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    padding-top: 0.5rem;
    pointer-events: none; /* Hide from mouse events when not active */
}

.submenu-wrapper.active {
    pointer-events: auto; /* Enable mouse events when active */
}

.submenu-wrapper.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Submenu */
.sub-menu {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 999px rgba(255, 255, 255, 0); /* Added invisible background to prevent content showing through */
    overflow: hidden;
    padding: 0.5rem 0;
    max-height: 80vh;
    overflow-y: auto;
    width: 100%;
    display: block !important;
    position: relative; /* Ensure proper stacking */
    z-index: 20; /* Ensure above any page content */
}

.sub-menu li {
    width: 100%;
    display: block !important;
}

.sub-menu a {
    padding: 0.75rem 1.5rem;
    font-weight: normal;
    font-size: 0.9rem;
    white-space: normal !important;
    line-height: 1.4;
    display: block;
    width: 100%;
}

.sub-menu a:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Mobile menu toggle */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    padding: 0;
    width: 30px;
    height: 24px;
    position: relative;
    cursor: pointer;
}

.menu-icon,
.menu-icon::before,
.menu-icon::after {
    display: block;
    width: 100%;
    height: 2px;
    background-color: #333;
    position: absolute;
    transition: all 0.3s ease;
}

.menu-icon {
    top: 50%;
    transform: translateY(-50%);
}

.menu-icon::before,
.menu-icon::after {
    content: '';
    left: 0;
}

.menu-icon::before {
    top: -8px;
}

.menu-icon::after {
    bottom: -8px;
}

.menu-toggle.is-active .menu-icon {
    background-color: transparent;
}

.menu-toggle.is-active .menu-icon::before {
    transform: rotate(45deg);
    top: 0;
}

.menu-toggle.is-active .menu-icon::after {
    transform: rotate(-45deg);
    bottom: 0;
}

/* Mobile menu */
.mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: 99;
    padding: 1rem;
    overflow-y: auto;
    max-height: 80vh;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    display: block !important; /* Always display but use visibility and opacity for showing/hiding */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-menu.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mobile-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-menu li {
    position: relative;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    z-index: 10; /* Base z-index for menu items */
}

.mobile-menu a {
    display: block;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.mobile-menu a:hover {
    background-color: rgba(0, 0, 0, 0.03);
    color: var(--color-amber-600);
}

/* Mobile submenu toggle */
.mobile-submenu-toggle {
    position: absolute;
    right: 0.5rem;
    top: 0.5rem;
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.2s ease;
    z-index: 10; /* Ensure it's above other elements */
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-submenu-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.mobile-submenu-toggle.is-active {
    transform: rotate(45deg);
    color: var(--color-amber-600);
    background-color: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
}

/* Mobile submenu */
.mobile-menu .sub-menu {
    background-color: rgba(0, 0, 0, 0.02);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    border-radius: 0.25rem;
    padding: 0;
    margin: 0.25rem 0 0.5rem 1rem;
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.5s ease;
    display: block !important; /* Ensure submenu is always in the DOM */
    position: relative; /* Ensure proper stacking context */
    z-index: 20; /* Higher than parent items */
}

.mobile-menu .sub-menu li {
    border-bottom: none;
    border-top: 1px solid rgba(0, 0, 0, 0.03);
}

.mobile-menu .sub-menu li:first-child {
    border-top: none;
}

.mobile-menu .sub-menu a {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    font-weight: normal;
    color: #555;
    white-space: normal;
    line-height: 1.3;
    display: block;
}

.mobile-menu .sub-menu a:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--color-amber-600);
}

/* Active submenu styling */
.mobile-menu .has-active-submenu {
    z-index: 30; /* Higher z-index for items with active submenu */
}

.mobile-menu .has-active-submenu > a {
    background-color: rgba(0, 0, 0, 0.02);
    font-weight: 600;
}

.mobile-menu .has-active-submenu .sub-menu {
    background-color: rgba(245, 158, 11, 0.03);
    border-left: 2px solid rgba(245, 158, 11, 0.2);
}

/* Mobile utility links */
.mobile-utility-links {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
}

.mobile-utility-links li {
    border-bottom: none;
    margin: 0;
    padding: 0;
}

.mobile-utility-links a {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    color: #666;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 2rem;
    display: inline-block;
}

/* Body class when menu is open */
body.menu-open {
    overflow: hidden;
}

/* Responsive styles */
@media (max-width: 1023px) {
    .utility-links {
        display: none;
    }

    .search-form {
        margin-left: 0;
    }

    .menu-toggle {
        display: block;
        margin-left: 1rem;
    }

    .main-navigation {
        display: none;
    }
}

@media (max-width: 767px) {
    .search-field {
        width: 150px;
    }

    .search-field:focus {
        width: 180px;
    }

    .logo-img {
        height: 32px;
    }
}
