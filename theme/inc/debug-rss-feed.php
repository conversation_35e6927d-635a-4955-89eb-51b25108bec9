<?php
/**
 * RSS Feed Debug Page
 * 
 * This standalone script forces WP_DEBUG mode and generates debug reports for RSS feeds.
 * Access via: /wp-content/themes/dumabyt/theme/inc/debug-rss-feed.php?partner=imaterialy
 */

// Initialize WordPress
define('WP_USE_THEMES', false);
require_once('../../../../wp-load.php');

// Security check - only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Force debug mode
if (!defined('WP_DEBUG')) {
    define('WP_DEBUG', true);
}

/**
 * Helper function to extract an image from a feed item
 * 
 * @param SimplePie_Item $item Feed item
 * @return string Image URL or empty string if not found
 */
function dumabyt_get_feed_item_image($item) {
    if (!$item) {
        return '';
    }
    
    $image_url = '';
    
    // Method 1: Check for media:content tags
    $media_content = $item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'content');
    if ($media_content && isset($media_content[0]['attribs']['']['url'])) {
        return $media_content[0]['attribs']['']['url'];
    }
    
    // Method 2: Check for media:thumbnail tags
    $thumbnails = $item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'thumbnail');
    if ($thumbnails && isset($thumbnails[0]['attribs']['']['url'])) {
        return $thumbnails[0]['attribs']['']['url'];
    }
    
    // Method 3: Extract image from content:encoded
    $content_encoded = $item->get_item_tags('http://purl.org/rss/1.0/modules/content/', 'encoded');
    if ($content_encoded && isset($content_encoded[0]['data'])) {
        $content = $content_encoded[0]['data'];
        if (!empty($content) && preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content, $matches)) {
            return $matches[1];
        }
    }
    
    // Method 4: Try regular content
    $content = $item->get_content();
    if (!empty($content) && preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content, $matches)) {
        return $matches[1];
    }
    
    // Method 5: Try description
    $description = $item->get_description();
    if (!empty($description) && preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $description, $matches)) {
        return $matches[1];
    }
    
    // Special case for mujdum.cz (non-RSS site)
    if (strpos($item->get_permalink(), 'mujdum.cz') !== false) {
        return 'https://www.mujdum.cz/images/logo.png';
    }
    
    return '';
}

// Get partner key from URL parameter
$partner_key = isset($_GET['partner']) ? sanitize_text_field($_GET['partner']) : '';

// List of valid partners
$valid_partners = array('mujdum', 'imaterialy', 'stavbaweb', 'rodinnydom', 'cihel');

// HTML header
echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>RSS Feed Debug Tool</title>
    <style>
        body { font-family: sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; }
        .partner-list { display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px; }
        .partner-link { padding: 8px 16px; background: #f0f0f0; border-radius: 4px; text-decoration: none; color: #333; }
        .partner-link:hover { background: #e0e0e0; }
        .partner-link.active { background: #4a6; color: white; }
        .debug-result { margin-top: 20px; }
        .error { color: #c00; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>RSS Feed Debug Tool</h1>
    <p>Select a partner website to debug its RSS feed:</p>
    <div class="partner-list">';

// Output partner links
foreach ($valid_partners as $key) {
    $active_class = ($key === $partner_key) ? ' active' : '';
    echo '<a href="?partner=' . $key . '" class="partner-link' . $active_class . '">' . $key . '</a>';
}

echo '</div>';

// If a partner is selected, debug its feed
if (!empty($partner_key) && in_array($partner_key, $valid_partners)) {
    echo '<div class="debug-result">';
    echo '<h2>Debugging RSS Feed for: ' . $partner_key . '</h2>';
    
    // Reference to the partner data
    $partners = array(
        'mujdum' => array(
            'name' => 'MŮJ DŮM KROK ZA KROKEM',
            'url' => 'https://www.mujdumkrokzakrokem.cz/feed/',
        ),
        'imaterialy' => array(
            'name' => 'MATERIÁLY PRO STAVBU',
            'url' => 'https://imaterialy.cz/rss/',
        ),
        'stavbaweb' => array(
            'name' => 'STAVBAWEB',
            'url' => 'https://www.stavbaweb.cz/rss/',
        ),
        'rodinnydom' => array(
            'name' => 'RODINNÝ DOM',
            'url' => 'https://rodinnydom.online/feed/',
        ),
        'cihel' => array(
            'name' => 'ČASOPIS MŮJ DŮM',
            'url' => 'https://www.mujdum.cz/',
        )
    );
    
    if (isset($partners[$partner_key])) {
        $partner = $partners[$partner_key];
        echo '<p>Name: <strong>' . $partner['name'] . '</strong></p>';
        echo '<p>Feed URL: <a href="' . $partner['url'] . '" target="_blank">' . $partner['url'] . '</a></p>';
        
        // Try to fetch the feed
        try {
            if (!class_exists('SimplePie')) {
                require_once(ABSPATH . WPINC . '/class-simplepie.php');
            }
            
            $feed = fetch_feed($partner['url']);
            
            if (is_wp_error($feed)) {
                echo '<p class="error">Error fetching feed: ' . $feed->get_error_message() . '</p>';
            } else {
                $feed->set_cache_duration(0); // No cache for debugging
                $feed->set_item_limit(1); // We only need the first item
                $feed->init();
                $feed->handle_content_type();
                
                if ($feed->get_item_quantity() > 0) {
                    $item = $feed->get_item(0);
                    
                    // Get image using our function
                    $image_url = dumabyt_get_feed_item_image($item);
                    
                    echo '<h3>Latest Article Found</h3>';
                    echo '<p>Title: <strong>' . $item->get_title() . '</strong></p>';
                    echo '<p>Link: <a href="' . $item->get_permalink() . '" target="_blank">' . $item->get_permalink() . '</a></p>';
                    
                    echo '<h3>Image Detection</h3>';
                    if (!empty($image_url)) {
                        echo '<p>Found image URL: <a href="' . $image_url . '" target="_blank">' . $image_url . '</a></p>';
                        echo '<p>Image preview:</p>';
                        echo '<img src="' . $image_url . '" style="max-width:600px; max-height:400px; border:1px solid #ccc; padding:5px;" />';
                        
                        // Check if this URL can be embedded here (CORS check)
                        echo '<h3>CORS Test</h3>';
                        echo '<p>Testing if this image can be loaded from another domain (CORS):</p>';
                        echo '<img src="' . $image_url . '" 
                                onerror="this.nextElementSibling.innerHTML = \'❌ CORS error: This image cannot be loaded from another domain.\'; this.style.display=\'none\';" 
                                onload="this.nextElementSibling.innerHTML = \'✅ CORS is allowed: This image can be loaded from another domain.\';" 
                                style="max-width:200px; max-height:150px;" />';
                        echo '<p id="cors-result"></p>';
                    } else {
                        echo '<p class="error">No image found in the feed.</p>';
                    }
                    
                    // Generate a detailed debug report
                    dumabyt_debug_rss_feed($partner_key, $item);
                    $debug_file = get_template_directory() . '/rss-debug-' . sanitize_file_name($partner_key) . '.html';
                    echo '<p>A detailed debug report has been generated: <a href="' . str_replace(ABSPATH, '/', $debug_file) . '" target="_blank">View Debug Report</a></p>';
                    
                } else {
                    echo '<p class="error">No items found in the feed.</p>';
                }
            }
        } catch (Exception $e) {
            echo '<p class="error">Exception: ' . $e->getMessage() . '</p>';
        }
    } else {
        echo '<p class="error">Partner information not found.</p>';
    }
    
    echo '</div>';
}

echo '</body></html>';
exit;
