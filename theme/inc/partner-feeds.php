<?php
/**
 * Partner RSS Feeds functions
 *
 * @package dumabyt
 */

/**
 * Fetch latest articles from partner websites via RSS feeds
 *
 * @return array Array of partner articles
 */
function dumabyt_get_partner_articles() {
    // Check for cached data
    $cached = get_transient('dumabyt_partner_articles');
    if (false !== $cached) {
        return $cached;
    }
    
    // If SimplePie is not loaded, load it
    if (!class_exists('SimplePie')) {
        require_once(ABSPATH . WPINC . '/class-simplepie.php');
    }
    
    // Define partner websites and their details
    $partners = array(
        'mujdum' => array(
            'name' => 'MŮJ DŮM KROK ZA KROKEM',
            'url' => 'https://www.mujdumkrokzakrokem.cz/feed/',
            'color' => 'amber',
            'color_class' => 'text-amber-800',
            'hover_class' => 'group-hover:text-amber-700',
            'fallback_image' => 'mujdum.svg'
        ),
        'imaterialy' => array(
            'name' => 'MATERIÁLY PRO STAVBU',
            'url' => 'https://imaterialy.cz/rss/',
            'color' => 'cyan',
            'color_class' => 'text-cyan-700',
            'hover_class' => 'group-hover:text-cyan-700',
            'fallback_image' => 'cihla.svg'
        ),
        'stavbaweb' => array(
            'name' => 'STAVBAWEB',
            'url' => 'https://www.stavbaweb.cz/feed/',
            'color' => 'emerald',
            'color_class' => 'text-emerald-700',
            'hover_class' => 'group-hover:text-emerald-700',
            'fallback_image' => 'vila.svg'
        ),
        'rodinnydom' => array(
            'name' => 'RODINNÝ DOM',
            'url' => 'https://rodinnydom.online/feed/',
            'color' => 'orange',
            'color_class' => 'text-orange-700',
            'hover_class' => 'group-hover:text-orange-700',
            'fallback_image' => 'dimenze.svg'
        )
    );
    
    $articles = array();
    
    foreach ($partners as $key => $partner) {
        // Set a default/fallback article in case we can't fetch the feed
        $articles[$key] = array(
            'title' => 'Navštivte web ' . $partner['name'],
            'permalink' => '#',
            'partner_name' => $partner['name'],
            'color_class' => $partner['color_class'],
            'hover_class' => $partner['hover_class'],
            'image_url' => '',
            'is_external_image' => false,
            'fallback_image' => $partner['fallback_image']
        );
        
        // Special handling for StavbaWEB due to SSL issues
        if ($key === 'stavbaweb') {
            try {
                $context = stream_context_create([
                    'ssl' => ['verify_peer' => false, 'verify_peer_name' => false],
                ]);
                $content = file_get_contents($partner['url'], false, $context);
                
                if ($content !== false) {
                    // Extract title, permalink and image
                    if (preg_match('/<item>.*?<title>(.*?)<\/title>.*?<link>(.*?)<\/link>/s', $content, $matches)) {
                        $title = trim($matches[1]);
                        $permalink = trim($matches[2]);
                        $image_url = '';
                        
                        // Try to find image in content:encoded
                        if (preg_match('/<content:encoded>(.*?)<\/content:encoded>/s', $content, $content_matches)) {
                            if (preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content_matches[1], $img_matches)) {
                                $image_url = $img_matches[1];
                            }
                        }
                        
                        // If no image found, check for media:content tag
                        if (empty($image_url) && preg_match('/media:content.*?url=[\'\"](.*?)[\'\"].*?>/s', $content, $media_matches)) {
                            $image_url = $media_matches[1];
                        }
                        
                        $articles[$key] = array(
                            'title' => html_entity_decode($title),
                            'permalink' => $permalink,
                            'partner_name' => $partner['name'],
                            'color_class' => $partner['color_class'],
                            'hover_class' => $partner['hover_class'],
                            'image_url' => $image_url,
                            'is_external_image' => !empty($image_url),
                            'fallback_image' => $partner['fallback_image']
                        );
                    }
                }
            } catch (Exception $e) {
                // Log the error if debugging is enabled
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('Error fetching StavbaWEB feed: ' . $e->getMessage());
                }
            }
            continue; // Skip the standard processing for StavbaWEB
        }
        

        // Pro "MŮJ DŮM KROK ZA KROKEM", použijeme vylepšenou extrakci obrázků
        if ($key === 'mujdum') {
            try {
                // Použijeme SimplePie pro základní data
                $feed = fetch_feed($partner['url']);
                
                if (!is_wp_error($feed)) {
                    $feed->set_cache_duration(3600);
                    $feed->set_item_limit(1);
                    $feed->init();
                    $feed->handle_content_type();
                    
                    if ($feed->get_item_quantity() > 0) {
                        $item = $feed->get_item(0);
                        $permalink = $item->get_permalink();
                        $image_url = '';
                        $is_external_image = false;
                        
                        // Zkusíme několik způsobů extrakce obrázku
                        
                        // 1. Zkusíme media:content tagy
                        $media_content = $item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'content');
                        if ($media_content && isset($media_content[0]['attribs']['']['url'])) {
                            $image_url = $media_content[0]['attribs']['']['url'];
                            $is_external_image = true;
                        }
                        
                        // 2. Zkusíme media:thumbnail tagy
                        if (empty($image_url)) {
                            $thumbnails = $item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'thumbnail');
                            if ($thumbnails && isset($thumbnails[0]['attribs']['']['url'])) {
                                $image_url = $thumbnails[0]['attribs']['']['url'];
                                $is_external_image = true;
                            }
                        }
                        
                        // 3. Extrahujeme obrázek z content:encoded
                        if (empty($image_url)) {
                            $content_encoded = $item->get_item_tags('http://purl.org/rss/1.0/modules/content/', 'encoded');
                            if ($content_encoded && isset($content_encoded[0]['data'])) {
                                $content = $content_encoded[0]['data'];
                                if (!empty($content) && preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content, $matches)) {
                                    $image_url = $matches[1];
                                    $is_external_image = true;
                                }
                            }
                        }
                        
                        // 4. Zkusíme běžný obsah
                        if (empty($image_url)) {
                            $content = $item->get_content();
                            if (!empty($content) && preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content, $matches)) {
                                $image_url = $matches[1];
                                $is_external_image = true;
                            }
                        }
                        
                        // 5. Zkusíme popis
                        if (empty($image_url)) {
                            $description = $item->get_description();
                            if (!empty($description) && preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $description, $matches)) {
                                $image_url = $matches[1];
                                $is_external_image = true;
                            }
                        }
                        
                        // 6. Pokud stále nemáme obrázek, načteme přímo stránku článku
                        if (empty($image_url) && !empty($permalink)) {
                            try {
                                $article_context = stream_context_create([
                                    'ssl' => ['verify_peer' => false, 'verify_peer_name' => false],
                                    'http' => ['timeout' => 5]
                                ]);
                                $article_content = file_get_contents($permalink, false, $article_context);
                                
                                if ($article_content !== false) {
                                    // Hledáme obrázek v článku - nejprve Open Graph obrázek
                                    if (preg_match('/<meta\s+property=[\'"]og:image[\'"]\s+content=[\'"]([^\'"]+)[\'"]/i', $article_content, $matches)) {
                                        $image_url = $matches[1];
                                        $is_external_image = true;
                                    }
                                    // Jako druhou možnost hledáme featured image
                                    elseif (preg_match('/<img[^>]+class=[\'"][^\'"]*wp-post-image[^\'"]*[\'"][^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $article_content, $matches)) {
                                        $image_url = $matches[1];
                                        $is_external_image = true;
                                    }
                                    // Jako poslední možnost hledáme jakýkoliv obrázek v článku
                                    elseif (preg_match('/<article[^>]*>.*?<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/is', $article_content, $matches)) {
                                        $image_url = $matches[1];
                                        $is_external_image = true;
                                    }
                                }
                            } catch (Exception $e) {
                                if (defined('WP_DEBUG') && WP_DEBUG) {
                                    error_log('Error fetching article content for MujDum: ' . $e->getMessage());
                                }
                            }
                        }
                        
                        // Fallback - pokud stále nemáme obrázek, použijeme staré logo, ale pouze jako poslední možnost
                        if (empty($image_url)) {
                            $image_url = 'https://www.mujdumkrokzakrokem.cz/wpweb/wp-content/uploads/2020/01/MDKK.jpg';
                            $is_external_image = true;
                        }
                        
                        // Sestavíme finální data článku
                        $articles[$key] = array(
                            'title' => $item->get_title(),
                            'permalink' => $permalink,
                            'partner_name' => $partner['name'],
                            'color_class' => $partner['color_class'],
                            'hover_class' => $partner['hover_class'],
                            'image_url' => $image_url,
                            'is_external_image' => $is_external_image,
                            'fallback_image' => $partner['fallback_image']
                        );
                    }
                }
            } catch (Exception $e) {
                // Log the error if debugging is enabled
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('Error fetching MujDum feed: ' . $e->getMessage());
                }
            }
            continue; // Skip the standard processing
        }
        
        // Standard processing for other feeds (imaterialy, rodinnydom)
        try {
            $feed = fetch_feed($partner['url']);
            
            if (!is_wp_error($feed)) {
                $feed->set_cache_duration(3600); // 1 hour cache
                $feed->set_item_limit(1); // We only need the latest article
                $feed->init();
                $feed->handle_content_type();
                
                if ($feed->get_item_quantity() > 0) {
                    $item = $feed->get_item(0);
                    $image_url = '';
                    $is_external_image = false;
                    
                    // Get article permalink
                    $permalink = $item->get_permalink();
                    // Ensure permalink is valid
                    if (empty($permalink) || $permalink === '#' || strpos($permalink, 'localhost') !== false) {
                        $permalink = $partner['url']; // Fallback to partner website
                    }
                    
                    // Try all image extraction methods
                    
                    // 1. Check for media:content tags
                    $media_content = $item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'content');
                    if ($media_content && isset($media_content[0]['attribs']['']['url'])) {
                        $image_url = $media_content[0]['attribs']['']['url'];
                        $is_external_image = true;
                    }
                    
                    // 2. Check for media:thumbnail tags
                    if (empty($image_url)) {
                        $thumbnails = $item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'thumbnail');
                        if ($thumbnails && isset($thumbnails[0]['attribs']['']['url'])) {
                            $image_url = $thumbnails[0]['attribs']['']['url'];
                            $is_external_image = true;
                        }
                    }
                    
                    // 3. Extract image from content:encoded
                    if (empty($image_url)) {
                        $content_encoded = $item->get_item_tags('http://purl.org/rss/1.0/modules/content/', 'encoded');
                        if ($content_encoded && isset($content_encoded[0]['data'])) {
                            $content = $content_encoded[0]['data'];
                            if (!empty($content) && preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content, $matches)) {
                                $image_url = $matches[1];
                                $is_external_image = true;
                            }
                        }
                    }
                    
                    // 4. Try regular content
                    if (empty($image_url)) {
                        $content = $item->get_content();
                        if (!empty($content) && preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content, $matches)) {
                            $image_url = $matches[1];
                            $is_external_image = true;
                        }
                    }
                    
                    // 5. Try description
                    if (empty($image_url)) {
                        $description = $item->get_description();
                        if (!empty($description) && preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $description, $matches)) {
                            $image_url = $matches[1];
                            $is_external_image = true;
                        }
                    }
                    
                    // Construct the final article data
                    $articles[$key] = array(
                        'title' => $item->get_title(),
                        'permalink' => $permalink,
                        'partner_name' => $partner['name'],
                        'color_class' => $partner['color_class'],
                        'hover_class' => $partner['hover_class'],
                        'image_url' => $image_url,
                        'is_external_image' => $is_external_image,
                        'fallback_image' => $partner['fallback_image']
                    );
                }
            }
        } catch (Exception $e) {
            // Log the error if debugging is enabled
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Error fetching feed from ' . $partner['url'] . ': ' . $e->getMessage());
            }
            // We'll use the fallback article defined above
        }
    }
    
    // Cache the results for 3 hours
    set_transient('dumabyt_partner_articles', $articles, 3 * HOUR_IN_SECONDS);
    
    return $articles;
}

/**
 * Clear the partner articles cache
 * 
 * This can be called when manually updating the articles
 */
function dumabyt_clear_partner_articles_cache() {
    delete_transient('dumabyt_partner_articles');
}
