<?php
/**
 * RSS Feed Debugging Tools
 *
 * @package dumabyt
 */

/**
 * Debug an RSS feed item and save the output to a file
 * 
 * @param string $partner_key The key identifier for the partner
 * @param SimplePie_Item $feed_item The feed item to debug
 * @return string Status message
 */
function dumabyt_debug_rss_feed($partner_key, $feed_item) {
    $output = "<!DOCTYPE html><html><head><title>RSS Debug for {$partner_key}</title>";
    $output .= "<style>body{font-family:sans-serif;margin:20px;} h1{color:#333;} h2{color:#555;} h3{color:#777;} pre{background:#f5f5f5;padding:10px;overflow:auto;} textarea{width:100%;}</style>";
    $output .= "</head><body>";
    
    $output .= "<h1>RSS Feed Debug: {$partner_key}</h1>";
    $output .= "<h2>Item Title: " . htmlspecialchars($feed_item->get_title()) . "</h2>";
    $output .= "<p>Permalink: <a href='" . esc_url($feed_item->get_permalink()) . "' target='_blank'>" . esc_url($feed_item->get_permalink()) . "</a></p>";
    
    // Debug enclosures
    $output .= "<h3>Enclosures:</h3>";
    $enclosures = $feed_item->get_enclosures();
    if (empty($enclosures)) {
        $output .= "<p>No enclosures found</p>";
    } else {
        $output .= "<ul>";
        foreach ($enclosures as $enclosure) {
            $output .= "<li>Type: " . $enclosure->get_type() . "<br>";
            $output .= "Medium: " . $enclosure->get_medium() . "<br>";
            $output .= "Link: <a href='" . esc_url($enclosure->get_link()) . "' target='_blank'>" . esc_url($enclosure->get_link()) . "</a>";
            
            // If it's an image, show it (safely check for null)
            $enclosure_type = $enclosure->get_type();
            if (($enclosure_type !== null && strpos($enclosure_type, 'image') !== false) || $enclosure->get_medium() === 'image') {
                $output .= "<br><img src='" . esc_url($enclosure->get_link()) . "' style='max-width:400px;max-height:300px;margin-top:10px;'>";
            }
            
            $output .= "</li>";
        }
        $output .= "</ul>";
    }
    
    // Debug media tags
    $output .= "<h3>Media Thumbnail Tags:</h3>";
    $thumbnails = $feed_item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'thumbnail');
    if (empty($thumbnails)) {
        $output .= "<p>No media thumbnail tags found</p>";
    } else {
        $output .= "<pre>" . print_r($thumbnails, true) . "</pre>";
        
        // Try to display the image
        if (isset($thumbnails[0]['attribs']['']['url'])) {
            $url = $thumbnails[0]['attribs']['']['url'];
            $output .= "<p>Thumbnail URL: <a href='" . esc_url($url) . "' target='_blank'>" . esc_url($url) . "</a></p>";
            $output .= "<img src='" . esc_url($url) . "' style='max-width:400px;max-height:300px;'>";
        }
    }
    
    // Debug media content tags
    $output .= "<h3>Media Content Tags:</h3>";
    $content_tags = $feed_item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'content');
    if (empty($content_tags)) {
        $output .= "<p>No media content tags found</p>";
    } else {
        $output .= "<pre>" . print_r($content_tags, true) . "</pre>";
        
        // Try to display the image
        if (isset($content_tags[0]['attribs']['']['url'])) {
            $url = $content_tags[0]['attribs']['']['url'];
            $output .= "<p>Content URL: <a href='" . esc_url($url) . "' target='_blank'>" . esc_url($url) . "</a></p>";
            $output .= "<img src='" . esc_url($url) . "' style='max-width:400px;max-height:300px;'>";
        }
    }
    
    // Look for images in content
    $output .= "<h3>Images in Content:</h3>";
    $content = $feed_item->get_content();
    $matches = array();
    preg_match_all('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content, $matches);
    
    if (empty($matches[1])) {
        $output .= "<p>No images found in content</p>";
    } else {
        $output .= "<ul>";
        foreach ($matches[1] as $img_url) {
            $output .= "<li><a href='" . esc_url($img_url) . "' target='_blank'>" . esc_url($img_url) . "</a>";
            $output .= "<br><img src='" . esc_url($img_url) . "' style='max-width:400px;max-height:300px;margin-top:10px;'></li>";
        }
        $output .= "</ul>";
    }
    
    // Look for images in description
    $output .= "<h3>Images in Description:</h3>";
    $description = $feed_item->get_description();
    $matches = array();
    preg_match_all('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $description, $matches);
    
    if (empty($matches[1])) {
        $output .= "<p>No images found in description</p>";
    } else {
        $output .= "<ul>";
        foreach ($matches[1] as $img_url) {
            $output .= "<li><a href='" . esc_url($img_url) . "' target='_blank'>" . esc_url($img_url) . "</a>";
            $output .= "<br><img src='" . esc_url($img_url) . "' style='max-width:400px;max-height:300px;margin-top:10px;'></li>";
        }
        $output .= "</ul>";
    }
    
    // Raw feed data for reference
    $output .= "<h3>Raw Content:</h3>";
    $output .= "<textarea rows='10'>" . htmlspecialchars($content) . "</textarea>";
    
    $output .= "<h3>Raw Description:</h3>";
    $output .= "<textarea rows='10'>" . htmlspecialchars($description) . "</textarea>";
    
    $output .= "</body></html>";
    
    // Save to file
    $filename = 'rss-debug-' . sanitize_file_name($partner_key) . '.html';
    $filepath = get_template_directory() . '/' . $filename;
    file_put_contents($filepath, $output);
    
    return "Debug information saved to: " . $filepath;
}
