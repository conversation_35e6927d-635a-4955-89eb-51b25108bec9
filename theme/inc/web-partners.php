<?php
/**
 * <PERSON><PERSON>or pro správu a zobrazení partnerů webu
 *
 * @package dumabyt
 */

if (!class_exists('Dumabyt_Web_Partners')) {

    /**
     * Třída pro správu partnerů webu
     */
    class Dumabyt_Web_Partners {
        /**
         * Instance této třídy (Singleton pattern)
         *
         * @var Dumabyt_Web_Partners
         */
        private static $instance = null;
        
        /**
         * Získání instance třídy
         *
         * @return Dumabyt_Web_Partners Instance třídy
         */
        public static function get_instance() {
            if (null === self::$instance) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * Konstruktor třídy.
         */
        private function __construct() {
            // Registrace post typu
            add_action('init', array($this, 'register_partner_post_type'));
            
            // Přidání metaboxů
            add_action('add_meta_boxes', array($this, 'add_partner_meta_boxes'));
            
            // Uložení metadat
            add_action('save_post_partner_web', array($this, 'save_partner_meta'));
            
            // <PERSON><PERSON>id<PERSON><PERSON> sloupců do přehledu
            add_filter('manage_partner_web_posts_columns', array($this, 'partner_columns'));
            add_action('manage_partner_web_posts_custom_column', array($this, 'partner_custom_column'), 10, 2);
            
            // Řazení podle pořadí
            add_filter('manage_edit-partner_web_sortable_columns', array($this, 'partner_sortable_columns'));
            
            // Přidání stylů do admin rozhraní
            add_action('admin_enqueue_scripts', array($this, 'admin_styles'));
        }
        
        /**
         * Registrace post typu partner_web
         */
        public function register_partner_post_type() {
            $labels = array(
                'name'               => 'Partneři webu',
                'singular_name'      => 'Partner webu',
                'menu_name'          => 'Partneři webu',
                'name_admin_bar'     => 'Partner webu',
                'add_new'            => 'Přidat nového',
                'add_new_item'       => 'Přidat nového partnera',
                'new_item'           => 'Nový partner',
                'edit_item'          => 'Upravit partnera',
                'view_item'          => 'Zobrazit partnera',
                'all_items'          => 'Všichni partneři',
                'search_items'       => 'Hledat partnery',
                'not_found'          => 'Žádní partneři nenalezeni',
                'not_found_in_trash' => 'Žádní partneři v koši',
            );
            
            $args = array(
                'labels'             => $labels,
                'public'             => false,
                'publicly_queryable' => false,
                'show_ui'            => true,
                'show_in_menu'       => true,
                'query_var'          => false,
                'rewrite'            => false,
                'capability_type'    => 'post',
                'has_archive'        => false,
                'hierarchical'       => false,
                'menu_position'      => 22,
                'menu_icon'          => 'dashicons-businessman',
                'supports'           => array('title', 'thumbnail'),
            );
            
            register_post_type('partner_web', $args);
        }
        
        /**
         * Přidání metaboxů pro nastavení partnera
         */
        public function add_partner_meta_boxes() {
            add_meta_box(
                'partner_settings',
                'Nastavení partnera',
                array($this, 'partner_settings_callback'),
                'partner_web',
                'normal',
                'high'
            );
        }
        
        /**
         * Callback pro metabox s nastavením partnera
         *
         * @param WP_Post $post Objekt aktuálního postu
         */
        public function partner_settings_callback($post) {
            // Nonce pro bezpečnost
            wp_nonce_field('save_partner_settings', 'partner_settings_nonce');
            
            // Načtení aktuálních hodnot
            $partner_url = get_post_meta($post->ID, 'partner_url', true);
            $partner_order = get_post_meta($post->ID, 'partner_order', true);
            
            echo '<style>
                .partner-field { margin-bottom: 20px; }
                .partner-field label { display: block; font-weight: bold; margin-bottom: 5px; }
                .partner-field input { width: 100%; max-width: 400px; padding: 8px; }
                .partner-section { background: #f9f9f9; padding: 15px; margin-bottom: 20px; border-left: 3px solid #007cba; }
            </style>';
            
            // URL odkazu
            echo '<div class="partner-section">';
            echo '<h3 style="margin-top: 0; color: #007cba;">URL odkazu</h3>';
            echo '<div class="partner-field">';
            echo '<label for="partner_url">URL kam bude logo partnera odkazovat:</label>';
            echo '<input type="url" name="partner_url" id="partner_url" value="' . esc_url($partner_url) . '" placeholder="https://" />';
            echo '<p class="description">Zadejte adresu včetně https://</p>';
            echo '</div>';
            echo '</div>';
            
            // Pořadí zobrazení
            echo '<div class="partner-section">';
            echo '<h3 style="margin-top: 0; color: #007cba;">Pořadí zobrazení</h3>';
            echo '<div class="partner-field">';
            echo '<label for="partner_order">Pořadí zobrazení:</label>';
            echo '<input type="number" name="partner_order" id="partner_order" value="' . esc_attr($partner_order) . '" min="0" step="1" />';
            echo '<p class="description">Nižší číslo = zobrazí se dříve. Výchozí hodnota je 0.</p>';
            echo '</div>';
            echo '</div>';
            
            // Informace o obrázku
            echo '<div class="partner-section">';
            echo '<h3 style="margin-top: 0; color: #007cba;">Logo partnera</h3>';
            echo '<p>Logo partnera nastavte jako "Náhledový obrázek" v pravém panelu.</p>';
            echo '<p>Doporučené rozměry: 200px × 50px</p>';
            echo '</div>';
        }
        
        /**
         * Uložení metadat partnera
         *
         * @param int $post_id ID aktuálního postu
         */
        public function save_partner_meta($post_id) {
            // Kontrola, zda jde o autosave
            if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
                return;
            }
            
            // Kontrola nonce
            if (!isset($_POST['partner_settings_nonce']) || !wp_verify_nonce($_POST['partner_settings_nonce'], 'save_partner_settings')) {
                return;
            }
            
            // Kontrola oprávnění
            if (!current_user_can('edit_post', $post_id)) {
                return;
            }
            
            // Uložení URL
            if (isset($_POST['partner_url'])) {
                update_post_meta($post_id, 'partner_url', esc_url_raw($_POST['partner_url']));
            }
            
            // Uložení pořadí
            if (isset($_POST['partner_order'])) {
                update_post_meta($post_id, 'partner_order', intval($_POST['partner_order']));
            }
        }
        
        /**
         * Definice sloupců pro přehled partnerů
         *
         * @param array $columns Existující sloupce
         * @return array Upravené sloupce
         */
        public function partner_columns($columns) {
            $new_columns = array();
            
            // Vložíme sloupec s obrázkem za checkbox
            foreach ($columns as $key => $value) {
                $new_columns[$key] = $value;
                if ($key === 'cb') {
                    $new_columns['thumbnail'] = 'Logo';
                }
            }
            
            // Přidáme další sloupce
            $new_columns['partner_url'] = 'URL odkazu';
            $new_columns['partner_order'] = 'Pořadí';
            
            return $new_columns;
        }
        
        /**
         * Zobrazení vlastních dat v sloupcích
         *
         * @param string $column   Název sloupce
         * @param int    $post_id  ID aktuálního postu
         */
        public function partner_custom_column($column, $post_id) {
            switch ($column) {
                case 'thumbnail':
                    if (has_post_thumbnail($post_id)) {
                        echo get_the_post_thumbnail($post_id, array(80, 40), array('style' => 'max-height: 40px; width: auto;'));
                    } else {
                        echo 'Není nastaveno';
                    }
                    break;
                    
                case 'partner_url':
                    $url = get_post_meta($post_id, 'partner_url', true);
                    if (!empty($url)) {
                        echo '<a href="' . esc_url($url) . '" target="_blank">' . esc_url($url) . '</a>';
                    } else {
                        echo '<em>Není nastaveno</em>';
                    }
                    break;
                    
                case 'partner_order':
                    $order = get_post_meta($post_id, 'partner_order', true);
                    echo !empty($order) ? intval($order) : '0';
                    break;
            }
        }
        
        /**
         * Nastavení řaditelných sloupců
         *
         * @param array $columns Existující sloupce
         * @return array Upravené sloupce
         */
        public function partner_sortable_columns($columns) {
            $columns['partner_order'] = 'partner_order';
            return $columns;
        }
        
        /**
         * Přidání stylů do admin rozhraní
         *
         * @param string $hook Aktuální admin stránka
         */
        public function admin_styles($hook) {
            global $post_type;
            
            if ('partner_web' === $post_type) {
                wp_enqueue_style(
                    'partner-admin-styles',
                    get_template_directory_uri() . '/css/partner-admin.css',
                    array(),
                    '1.0.0'
                );
            }
        }
        
        /**
         * Získání partnerů pro zobrazení na webu
         *
         * @param int $count Počet partnerů k získání
         * @return array Pole partnerů
         */
        public function get_partners($count = -1) {
            $args = array(
                'post_type'      => 'partner_web',
                'posts_per_page' => $count,
                'meta_key'       => 'partner_order',
                'orderby'        => 'meta_value_num',
                'order'          => 'ASC',
                'post_status'    => 'publish',
            );
            
            $partners_query = new WP_Query($args);
            
            if ($partners_query->have_posts()) {
                return $partners_query->posts;
            }
            
            return array();
        }
        
        /**
         * Vykreslení sekce partnerů na homepage
         */
        public function render_partners_section() {
            $partners = $this->get_partners();
            
            if (empty($partners)) {
                return;
            }
            
            echo '<div class="container mx-auto px-4 sm:px-6 lg:px-8 mb-8">';
            echo '<h2 class="uppercase text-gray-700 font-semibold text-lg mb-6 font-trajan text-center">PARTNEŘI WEBU</h2>';
            
            echo '<div class="flex flex-wrap justify-center items-center gap-x-8 gap-y-6">';
            
            foreach ($partners as $partner) {
                $url = get_post_meta($partner->ID, 'partner_url', true);
                
                echo '<a href="' . esc_url($url) . '" class="transform transition-all duration-300 hover:-translate-y-1 hover:opacity-80" target="_blank" rel="noopener">';
                
                if (has_post_thumbnail($partner->ID)) {
                    echo get_the_post_thumbnail($partner->ID, 'full', array('class' => 'h-10 md:h-12', 'alt' => esc_attr(get_the_title($partner->ID))));
                } else {
                    echo '<span class="text-gray-400">' . esc_html(get_the_title($partner->ID)) . '</span>';
                }
                
                echo '</a>';
            }
            
            echo '</div>'; // end flex container
            echo '</div>'; // end container
        }
    }
    
    // Inicializace třídy
    add_action('after_setup_theme', function() {
        Dumabyt_Web_Partners::get_instance();
    });
    
    /**
     * Funkce pro vykreslení sekce partnerů.
     * Lze volat přímo z šablony.
     */
    function dumabyt_render_partners() {
        $partners = Dumabyt_Web_Partners::get_instance();
        $partners->render_partners_section();
    }
}
