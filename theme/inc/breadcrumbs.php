<?php
/**
 * Breadcrumbs functionality for DUM A BYT theme
 *
 * @package _uw-theme
 */

/**
 * Display breadcrumbs for the current page
 */
function dumabyt_breadcrumbs() {
    // Home page
    $home_link = esc_url(home_url('/'));
    $home_text = __('<PERSON>lavn<PERSON> stránka', '_uw-theme');
    
    // Start the breadcrumb with a wrapper
    echo '<div class="breadcrumbs">';
    
    // Don't display breadcrumbs on the home page
    if (!is_front_page()) {
        echo '<a href="' . $home_link . '">' . $home_text . '</a>';
        
        if (is_category() || is_single()) {
            echo ' &gt; ';
            
            // Display category
            $categories = get_the_category();
            if (!empty($categories)) {
                echo '<a href="' . esc_url(get_category_link($categories[0]->term_id)) . '">' . esc_html($categories[0]->name) . '</a>';
            }
            
            // If it's a single post, also display the title
            if (is_single()) {
                echo ' &gt; ';
                the_title();
            }
        } elseif (is_page()) {
            global $post;
            // If page has parent
            if ($post && $post->post_parent) {
                $parents = get_post_ancestors($post->ID);
                
                // Display each parent in order
                foreach (array_reverse($parents) as $parent_id) {
                    echo ' &gt; ';
                    echo '<a href="' . get_permalink($parent_id) . '">' . get_the_title($parent_id) . '</a>';
                }
                
                echo ' &gt; ';
                the_title();
            } else {
                // If simple page without parent
                echo ' &gt; ';
                the_title();
            }
        } elseif (is_search()) {
            echo ' &gt; ';
            echo __('Výsledky vyhledávání pro:', '_uw-theme') . ' ' . get_search_query();
        } elseif (is_tag()) {
            echo ' &gt; ';
            echo __('Příspěvky označené:', '_uw-theme') . ' ' . single_tag_title('', false);
        } elseif (is_author()) {
            echo ' &gt; ';
            echo __('Autor:', '_uw-theme') . ' ' . get_the_author();
        } elseif (is_archive()) {
            echo ' &gt; ';
            
            if (is_day()) {
                echo get_the_date();
            } elseif (is_month()) {
                echo get_the_date('F Y');
            } elseif (is_year()) {
                echo get_the_date('Y');
            } else {
                echo __('Archiv', '_uw-theme');
            }
        }
    }
    
    echo '</div>';
}
