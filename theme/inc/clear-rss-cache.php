<?php
/**
 * <PERSON>ript to clear RSS cache when loaded through WordPress
 * 
 * Place this file in wp-content/themes/dumabyt/theme/inc/
 * Access via: /wp-content/themes/dumabyt/theme/inc/clear-rss-cache.php
 */

// Initialize WordPress
define('WP_USE_THEMES', false);

// Get WordPress absolute path
$absolute_path = dirname(dirname(dirname(dirname(__FILE__))));

// Require WordPress core
require_once($absolute_path . '/wp-load.php');

// Security check - only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Delete the transient
delete_transient('dumabyt_partner_articles');

// Output success message
echo '<html><head><title>Cache Cleared</title>';
echo '<style>body{font-family:sans-serif;max-width:600px;margin:50px auto;padding:20px;background:#f7f7f7;border-radius:10px;box-shadow:0 2px 5px rgba(0,0,0,0.1);}h1{color:#333}p{line-height:1.6;color:#555}.success{color:#0a0;font-weight:bold;}</style>';
echo '</head><body>';
echo '<h1>RSS Cache Management</h1>';
echo '<p class="success">✓ RSS cache was cleared successfully!</p>';
echo '<p>The system will download fresh RSS feeds from partner websites the next time the footer is loaded.</p>';
echo '<p><a href="/">&larr; Return to website</a></p>';
echo '</body></html>';
