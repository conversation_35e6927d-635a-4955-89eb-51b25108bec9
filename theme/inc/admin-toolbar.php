<?php
/**
 * Admin Toolbar Customizations
 *
 * @package dumabyt
 */

/**
 * Add custom options to the WordPress admin toolbar
 * 
 * @param WP_Admin_Bar $admin_bar The WP_Admin_Bar instance.
 */
function dumabyt_custom_admin_toolbar_menu($admin_bar) {
    // Only add for administrators
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Add parent menu item
    $admin_bar->add_node(array(
        'id'    => 'dumabyt-tools',
        'title' => 'DŮM & BYT Nástroje',
        'href'  => '#',
    ));
    
    // Add RSS cache clearing option
    $admin_bar->add_node(array(
        'id'     => 'dumabyt-clear-rss-cache',
        'parent' => 'dumabyt-tools',
        'title'  => 'Vymazat cache RSS',
        'href'   => get_template_directory_uri() . '/inc/clear-rss-cache.php',
        'meta'   => array(
            'target' => '_blank'
        )
    ));
    
}
add_action('admin_bar_menu', 'dumabyt_custom_admin_toolbar_menu', 100);

/**
 * Handle the RSS cache clearing action
 */
function dumabyt_handle_admin_actions() {
    if (isset($_GET['action']) && $_GET['action'] === 'dumabyt_clear_rss_cache') {
        // Check nonce
        if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'dumabyt_clear_rss_cache_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_die('You do not have sufficient permissions to perform this action');
        }
        
        // Clear the RSS cache
        dumabyt_clear_partner_articles_cache();
        
        // Redirect back with notice
        wp_redirect(add_query_arg('dumabyt_notice', 'rss_cache_cleared', wp_get_referer()));
        exit;
    }
}
add_action('admin_init', 'dumabyt_handle_admin_actions');

/**
 * Display admin notices
 */
function dumabyt_admin_notices() {
    if (isset($_GET['dumabyt_notice']) && $_GET['dumabyt_notice'] === 'rss_cache_cleared') {
        ?>
        <div class="notice notice-success is-dismissible">
            <p><?php _e('Cache RSS partnerských webů byla úspěšně vymazána.', 'dumabyt'); ?></p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'dumabyt_admin_notices');
