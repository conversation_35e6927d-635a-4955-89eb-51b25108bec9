<?php
/**
 * RSS Image Proxy
 *
 * This script fetches remote images from RSS feeds and serves them locally,
 * solving potential CORS issues and ensuring images load properly.
 * 
 * Usage: /wp-content/themes/dumabyt/theme/inc/rss-proxy.php?url=https://example.com/image.jpg
 */

// Initialize WordPress 
define('WP_USE_THEMES', false);
require_once('../../../../wp-load.php');

// Check URL parameter
if (!isset($_GET['url']) || empty($_GET['url'])) {
    header('HTTP/1.0 400 Bad Request');
    exit('Missing URL parameter');
}

// Sanitize URL
$image_url = filter_var($_GET['url'], FILTER_SANITIZE_URL);
if (!filter_var($image_url, FILTER_VALIDATE_URL)) {
    header('HTTP/1.0 400 Bad Request');
    exit('Invalid URL');
}

// Check for allowed domains (for security)
$allowed_domains = array(
    'www.mujdumkrokzakrokem.cz',
    'mujdumkrokzakrokem.cz',
    'imaterialy.cz',
    'www.imaterialy.cz',
    'www.stavbaweb.cz',
    'stavbaweb.cz',
    'rodinnydom.online',
    'www.rodinnydom.online',
    'stavimezcihel.cz',
    'www.stavimezcihel.cz'
);

$host = parse_url($image_url, PHP_URL_HOST);
$allowed = false;

foreach ($allowed_domains as $domain) {
    if ($host === $domain || substr($host, -(strlen($domain) + 1)) === '.' . $domain) {
        $allowed = true;
        break;
    }
}

if (!$allowed) {
    header('HTTP/1.0 403 Forbidden');
    exit('Domain not allowed');
}

// Set up caching
$cache_dir = get_template_directory() . '/cache/rss-images/';
if (!file_exists($cache_dir)) {
    mkdir($cache_dir, 0755, true);
}

$cache_file = $cache_dir . md5($image_url);
$cache_time = 7 * 24 * 60 * 60; // 1 week

// Use cached file if available and not expired
if (file_exists($cache_file) && (time() - filemtime($cache_file) < $cache_time)) {
    $image_data = file_get_contents($cache_file);
    $content_type = mime_content_type($cache_file);
} else {
    // Fetch the remote image
    $response = wp_remote_get($image_url, array(
        'timeout' => 15,
        'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ));
    
    if (is_wp_error($response)) {
        header('HTTP/1.0 500 Internal Server Error');
        exit('Failed to fetch image: ' . $response->get_error_message());
    }
    
    $status_code = wp_remote_retrieve_response_code($response);
    if ($status_code !== 200) {
        header('HTTP/1.0 ' . $status_code . ' ' . wp_remote_retrieve_response_message($response));
        exit('Failed to fetch image: HTTP ' . $status_code);
    }
    
    $image_data = wp_remote_retrieve_body($response);
    $content_type = wp_remote_retrieve_header($response, 'content-type');
    
    // Validate that it's actually an image
    if (!preg_match('/^image\//i', $content_type)) {
        header('HTTP/1.0 415 Unsupported Media Type');
        exit('Not an image');
    }
    
    // Cache the result
    file_put_contents($cache_file, $image_data);
}

// Output the image with appropriate headers
header('Content-Type: ' . $content_type);
header('Content-Length: ' . strlen($image_data));
header('Cache-Control: max-age=' . $cache_time . ', public');
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $cache_time) . ' GMT');

echo $image_data;
exit;
