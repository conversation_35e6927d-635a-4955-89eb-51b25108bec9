<?php
/**
 * Magazine Widget Class
 *
 * @package dumabyt
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * Dumabyt Magazine Widget Class
 * 
 * Fetches and displays the latest issue of Můj Dům magazine
 */
class Dumabyt_Magazine_Widget {

    /**
     * The single instance of the class
     *
     * @var Dumabyt_Magazine_Widget
     */
    protected static $_instance = null;

    /**
     * The cache key used for transients
     *
     * @var string
     */
    protected $cache_key = 'dumabyt_magazine_data';

    /**
     * The cache expiration time in seconds (1 day)
     *
     * @var int
     */
    protected $cache_expiration = 86400;

    /**
     * The URL to fetch magazine data from
     *
     * @var string
     */
    protected $source_url = 'https://www.mujdumkrokzakrokem.cz/nase-casopisy/';

    /**
     * Main Instance
     *
     * Ensures only one instance is loaded or can be loaded.
     *
     * @return Dumabyt_Magazine_Widget
     */
    public static function instance() {
        if ( is_null( self::$_instance ) ) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        // Nepotřebujeme žádn<PERSON> styly, použijeme Tailwind CSS
    }

    /**
     * Get magazine data
     *
     * @param bool $force_refresh Force refresh the cache
     * @return array|false Magazine data or false on failure
     */
    public function get_magazine_data( $force_refresh = false ) {
        // Check if we have cached data
        $magazine_data = get_transient( $this->cache_key );

        // If no cached data or force refresh, fetch new data
        if ( false === $magazine_data || $force_refresh ) {
            $magazine_data = $this->fetch_magazine_data();
            
            // If we got valid data, cache it
            if ( $magazine_data ) {
                set_transient( $this->cache_key, $magazine_data, $this->cache_expiration );
            }
        }

        return $magazine_data;
    }

    /**
     * Fetch magazine data from the source
     *
     * @return array|false Magazine data or false on failure
     */
    protected function fetch_magazine_data() {
        // Get the HTML content from the source URL
        $response = wp_remote_get( $this->source_url );

        // Check for errors
        if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
            return false;
        }

        $html = wp_remote_retrieve_body( $response );
        if ( empty( $html ) ) {
            return false;
        }

        // Default fallback values in case scraping fails
        $magazine_title = 'Můj dům 5/2025';
        $magazine_link = 'https://www.mujdumkrokzakrokem.cz/muj-dum-5-2025/';
        $magazine_image_url = 'https://www.mujdumkrokzakrokem.cz/wpweb/wp-content/uploads/2025/04/000_obal1_MD05-787x1024.jpg';
        
        // Find all magazine items by searching for the div structure pattern
        if (preg_match_all('/<div class="uw_lcp_item uw_lcp_magazines[^>]*>.*?<a href="([^"]*muj-dum-[^"]*)"[^>]*title="([^"]*)".*?style="background-image:url\(\'([^\']*)\'\)".*?<\/div>\s*<\/div>\s*<\/div>/s', $html, $matches, PREG_SET_ORDER)) {
            
            // Extract information about each magazine
            $magazines = array();
            foreach ($matches as $match) {
                // Extract URL, title and image URL
                $link = $match[1];
                $title = $match[2];
                $image = $match[3];
                
                // Extract year and issue number from title
                if (preg_match('/Můj dům (\d+)\/(\d+)/', $title, $title_parts)) {
                    $issue = $title_parts[1];
                    $year = $title_parts[2];
                    
                    $magazines[] = array(
                        'link' => $link,
                        'title' => $title,
                        'image' => $image,
                        'year' => (int)$year,
                        'issue' => (int)$issue
                    );
                }
            }
            
            // Sort magazines by year and issue (descending) to find most recent
            if (!empty($magazines)) {
                usort($magazines, function($a, $b) {
                    // First compare by year
                    if ($a['year'] != $b['year']) {
                        return $b['year'] - $a['year']; // Descending order
                    }
                    // If same year, compare by issue
                    return $b['issue'] - $a['issue']; // Descending order
                });
                
                // Get the most recent magazine (first after sorting)
                $latest = $magazines[0];
                $magazine_title = $latest['title'];
                $magazine_link = $latest['link'];
                $magazine_image_url = $latest['image'];
            }
        }
        
        // Alternative approach if the first method fails
        if (empty($magazine_image_url) || strpos($magazine_image_url, 'placeholder') !== false) {
            // Look for background-image styles that might contain the magazine cover
            if (preg_match('/background-image:url\(\'([^\']*muj-dum[^\']*\.jpg)\'\)/i', $html, $bg_matches)) {
                $magazine_image_url = $bg_matches[1];
            }
        }

        // Return the magazine data
        return array(
            'title'     => $magazine_title,
            'link'      => $magazine_link,
            'image_url' => $magazine_image_url,
        );
    }

    /**
     * Renders the magazine widget
     * 
     * @param bool $is_top_header Whether the widget is being rendered in the top header
     */
    public function render($is_top_header = false) {
        $magazine_data = $this->get_magazine_data();
        
        // If no magazine data, return empty
        if ( !$magazine_data ) {
            return;
        }
        
        // Extract data
        $title     = isset( $magazine_data['title'] ) ? esc_html( $magazine_data['title'] ) : '';
        $link      = isset( $magazine_data['link'] ) ? esc_url( $magazine_data['link'] ) : '#';
        $image_url = isset( $magazine_data['image_url'] ) ? esc_url( $magazine_data['image_url'] ) : '';
        
        // If no image URL, return empty
        if ( empty( $image_url ) ) {
            return;
        }
        
        $html = '';
        
        // Single responsive version for all screen sizes
        $html = '<div class="magazine-widget relative group">';
        $html .= '<a href="' . $link . '" target="_blank" title="' . $title . '" class="magazine-link block">';
        $html .= '<img src="' . $image_url . '" alt="' . $title . '" class="magazine-cover rounded shadow-sm hover:shadow-md transition-shadow" />';
        $html .= '</a>';
        $html .= '<div class="magazine-tooltip absolute -bottom-5 right-0 bg-white text-[10px] py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50 shadow-sm">' . $title . '</div>';
        $html .= '</div>';
        
        echo $html;
    }

    /**
     * Clear the widget cache
     */
    public function clear_cache() {
        delete_transient( $this->cache_key );
    }
}

// Initialize the magazine widget
function dumabyt_magazine_widget() {
    return Dumabyt_Magazine_Widget::instance();
}
