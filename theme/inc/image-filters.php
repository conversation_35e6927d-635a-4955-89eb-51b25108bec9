<?php
/**
 * Image handling filters
 *
 * @package dumabyt
 */

/**
 * Add onerror attribute to external RSS images to handle loading failures gracefully
 * 
 * @param string $html The HTML for the image
 * @param int $id The attachment ID (not used in this context)
 * @param string $alt The alt text
 * @param string $title The title attribute (not used in this context)
 * @param string $align The alignment (not used in this context)
 * @param string|array $size The size (not used in this context)
 * @return string Modified HTML
 */
function dumabyt_handle_rss_image_errors($html, $id, $alt, $title, $align, $size) {
    // Only process images from RSS feeds
    if (strpos($html, 'data-source="rss"') !== false) {
        // Get theme directory URL for fallback image
        $theme_url = get_template_directory_uri();
        
        // Extract partner type from the alt text to determine which fallback image to use
        $partner_type = '';
        if (strpos($alt, 'MŮJ DŮM') !== false) {
            $partner_type = 'mujdum.svg';
        } elseif (strpos($alt, 'MATERIÁLY') !== false) {
            $partner_type = 'cihla.svg';
        } elseif (strpos($alt, 'STAVBAWEB') !== false) {
            $partner_type = 'vila.svg';
        } elseif (strpos($alt, 'RODINNÝ') !== false) {
            $partner_type = 'dimenze.svg';
        } elseif (strpos($alt, 'STAVÍME Z CIHEL') !== false) {
            $partner_type = 'pasivni.svg';
        } else {
            // Default fallback
            $partner_type = 'mujdum.svg';
        }
        
        $fallback_url = esc_url("{$theme_url}/images/partner-articles/{$partner_type}");
        
        // Add onerror attribute to handle image loading failures
        $html = str_replace('<img ', "<img onerror=\"this.onerror=null; this.src='{$fallback_url}';\" ", $html);
    }
    
    return $html;
}
add_filter('get_image_tag', 'dumabyt_handle_rss_image_errors', 10, 6);

/**
 * Add a CSS class for feed images to ensure proper display
 */
function dumabyt_feed_image_styles() {
    ?>
    <style>
    .partner-feed-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        aspect-ratio: 16/9;
        background-color: #f9fafb; /* bg-gray-50 equivalent */
    }
    
    .partner-feed-image.error {
        object-fit: contain;
        padding: 1rem;
    }
    </style>
    <?php
}
add_action('wp_head', 'dumabyt_feed_image_styles');
