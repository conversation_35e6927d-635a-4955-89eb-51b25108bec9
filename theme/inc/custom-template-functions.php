<?php
/**
 * Custom Template Functions for DŮM & BYT theme
 *
 * Functions specific to homepage components and reusable article queries
 *
 * @package _uw-theme
 */

/**
 * Get featured articles for a specific category
 *
 * @param string $category_slug The category slug
 * @param int    $posts_count   Number of posts to retrieve (default 1)
 * @param int    $offset        Offset for query (default 0)
 * @param bool   $require_image Whether posts must have featured image (default true)
 * @return WP_Query             Query result object
 */
function dumabyt_get_category_articles($category_slug, $posts_count = 1, $offset = 0, $require_image = true) {
    $args = array(
        'category_name' => $category_slug,
        'posts_per_page' => $posts_count,
        'offset' => $offset,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    );

    if ($require_image) {
        $args['meta_query'] = array(
            array(
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            )
        );
    }

    return new WP_Query($args);
}

// Funkce dumabyt_get_partners byla přesunuta do functions.php a implementována jako součást UmimeWeby modulu

/**
 * Display a category badge with consistent styling
 *
 * @param string $category_name The category name
 * @param string $category_url  The category URL
 * @param string $color_class   Tailwind color class for the badge
 * @return void                 Outputs the HTML for category badge
 */
function dumabyt_category_badge($category_name, $category_url, $color_class) {
    ?>
    <a href="<?php echo esc_url($category_url); ?>" class="absolute top-4 left-4 z-10">
        <span class="<?php echo esc_attr($color_class); ?> text-white font-bold uppercase py-1.5 px-4 rounded-full text-xs tracking-wider badge-shadow"><?php echo esc_html($category_name); ?></span>
    </a>
    <?php
}

/**
 * Generate HTML for partner placeholder
 *
 * @return void Outputs the HTML for partner placeholder
 */
function dumabyt_partner_placeholder() {
    ?>
    <div class="p-4 text-center">
        <h3 class="text-gray-500 font-medium mb-2">Místo pro vašeho partnera</h3>
        <div class="bg-gray-100 py-10 px-4 rounded">
            <span class="text-gray-400 text-2xl block mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1H2a1 1 0 01-1-1v-3a1 1 0 011-1h1a2 2 0 100-4H2a1 1 0 01-1-1V7a1 1 0 011-1h3a1 1 0 001-1V4a2 2 0 114 0v1a1 1 0 001 1h1a2 2 0 100-4h-1a1 1 0 00-1-1V1a1 1 0 00-1-1" />
                </svg>
            </span>
            <p class="text-gray-400 text-sm">Banner 300x250 px</p>
        </div>
    </div>
    <?php
}

/**
 * Get category color class based on category slug
 *
 * @param string $category_slug The category slug
 * @return string               Tailwind background color class
 */
function dumabyt_get_category_color($category_slug) {
    $colors = array(
        'dum' => 'bg-amber-600',
        'interier' => 'bg-cyan-600',
        'stavba' => 'bg-emerald-600',
        'rekonstrukce' => 'bg-orange-600',
        'zahrada' => 'bg-teal-600',
        'blog' => 'bg-purple-600',
        'akce' => 'bg-indigo-600',
        'doporucujeme' => 'bg-rose-600',
        'default' => 'bg-blue-600'
    );

    return isset($colors[$category_slug]) ? $colors[$category_slug] : $colors['default'];
}

/**
 * Get category gradient colors based on category slug
 *
 * @param string $category_slug The category slug
 * @return string               Tailwind gradient class
 */
function dumabyt_get_category_gradient($category_slug) {
    $gradients = array(
        'dum' => 'from-amber-500 to-amber-700',
        'interier' => 'from-cyan-500 to-cyan-700',
        'stavba' => 'from-emerald-500 to-emerald-700',
        'rekonstrukce' => 'from-orange-500 to-orange-700',
        'zahrada' => 'from-teal-500 to-teal-700',
        'blog' => 'from-purple-500 to-purple-700',
        'akce' => 'from-indigo-500 to-indigo-700',
        'doporucujeme' => 'from-rose-500 to-rose-700',
        'default' => 'from-blue-500 to-blue-700'
    );

    return isset($gradients[$category_slug]) ? $gradients[$category_slug] : $gradients['default'];
}
