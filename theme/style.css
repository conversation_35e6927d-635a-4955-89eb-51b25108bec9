/*! tailwindcss v4.0.14 | MIT License | https://tailwindcss.com */
/*!
Theme Name: _uw-theme
Theme URI: https://umimeweby.cz
Author: Team UW
Author URI: https://underscoretw.com/
Description: Custom sablona pro DUM A BYT
Version: 1.1.1
Tested up to: 6.2
Requires PHP: 7.4
License: GNU General Public License v2 or later
License URI: LICENSE
Text Domain: _uw-theme
Tags:

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned.

_uw-theme is based on _tw https://underscoretw.com/, (C) 2021-2025 Greg Sullivan
_tw is distributed under the terms of the GNU GPL v2 or later.

_tw is based on Underscores https://underscores.me/ and Varia https://github.com/Automattic/themes/tree/master/varia, (C) 2012-2025 Automattic, Inc.
Underscores and Varia are distributed under the terms of the GNU GPL v2 or later.
*/
:root, :host {
  --font-sans: "futura-pt", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
		"Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: "trajan-pro-3", ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
		"Liberation Mono", "Courier New", monospace;
  --color-red-500: oklch(0.637 0.237 25.331);
  --color-orange-50: oklch(0.98 0.016 73.684);
  --color-orange-100: oklch(0.954 0.038 75.164);
  --color-orange-200: oklch(0.901 0.076 70.697);
  --color-orange-300: oklch(0.837 0.128 66.29);
  --color-orange-500: oklch(0.705 0.213 47.604);
  --color-orange-600: oklch(0.646 0.222 41.116);
  --color-orange-700: oklch(0.553 0.195 38.402);
  --color-orange-800: oklch(0.47 0.157 37.304);
  --color-amber-50: oklch(0.987 0.022 95.277);
  --color-amber-100: oklch(0.962 0.059 95.617);
  --color-amber-200: oklch(0.924 0.12 95.746);
  --color-amber-300: oklch(0.879 0.169 91.605);
  --color-amber-400: oklch(0.828 0.189 84.429);
  --color-amber-500: oklch(0.769 0.188 70.08);
  --color-amber-600: oklch(0.666 0.179 58.318);
  --color-amber-700: oklch(0.555 0.163 48.998);
  --color-amber-800: oklch(0.473 0.137 46.201);
  --color-amber-900: oklch(0.414 0.112 45.904);
  --color-amber-950: oklch(0.279 0.077 45.635);
  --color-emerald-50: oklch(0.979 0.021 166.113);
  --color-emerald-100: oklch(0.95 0.052 163.051);
  --color-emerald-200: oklch(0.905 0.093 164.15);
  --color-emerald-300: oklch(0.845 0.143 164.978);
  --color-emerald-500: oklch(0.696 0.17 162.48);
  --color-emerald-600: oklch(0.596 0.145 163.225);
  --color-emerald-700: oklch(0.508 0.118 165.612);
  --color-emerald-800: oklch(0.432 0.095 166.913);
  --color-emerald-900: oklch(0.378 0.077 168.94);
  --color-teal-50: oklch(0.984 0.014 180.72);
  --color-teal-100: oklch(0.953 0.051 180.801);
  --color-teal-200: oklch(0.91 0.096 180.426);
  --color-teal-300: oklch(0.855 0.138 181.071);
  --color-teal-500: oklch(0.704 0.14 182.503);
  --color-teal-600: oklch(0.6 0.118 184.704);
  --color-teal-700: oklch(0.511 0.096 186.391);
  --color-teal-800: oklch(0.437 0.078 188.216);
  --color-cyan-50: oklch(0.984 0.019 200.873);
  --color-cyan-100: oklch(0.956 0.045 203.388);
  --color-cyan-200: oklch(0.917 0.08 205.041);
  --color-cyan-300: oklch(0.865 0.127 207.078);
  --color-cyan-500: oklch(0.715 0.143 215.221);
  --color-cyan-600: oklch(0.609 0.126 221.723);
  --color-cyan-700: oklch(0.52 0.105 223.128);
  --color-cyan-800: oklch(0.45 0.085 224.283);
  --color-cyan-900: oklch(0.398 0.07 227.392);
  --color-blue-100: oklch(0.932 0.032 255.585);
  --color-blue-400: oklch(0.707 0.165 254.624);
  --color-blue-500: oklch(0.623 0.214 259.815);
  --color-blue-600: oklch(0.546 0.245 262.881);
  --color-blue-700: oklch(0.488 0.243 264.376);
  --color-blue-800: oklch(0.424 0.199 265.638);
  --color-indigo-100: oklch(0.93 0.034 272.788);
  --color-indigo-300: oklch(0.785 0.115 274.713);
  --color-indigo-500: oklch(0.585 0.233 277.117);
  --color-indigo-600: oklch(0.511 0.262 276.966);
  --color-indigo-700: oklch(0.457 0.24 277.023);
  --color-indigo-800: oklch(0.398 0.195 277.366);
  --color-purple-50: oklch(0.977 0.014 308.299);
  --color-purple-100: oklch(0.946 0.033 307.174);
  --color-purple-200: oklch(0.902 0.063 306.703);
  --color-purple-300: oklch(0.827 0.119 306.383);
  --color-purple-500: oklch(0.627 0.265 303.9);
  --color-purple-600: oklch(0.558 0.288 302.321);
  --color-purple-700: oklch(0.496 0.265 301.924);
  --color-purple-800: oklch(0.438 0.218 303.724);
  --color-rose-100: oklch(0.941 0.03 12.58);
  --color-rose-300: oklch(0.81 0.117 11.638);
  --color-rose-500: oklch(0.645 0.246 16.439);
  --color-rose-600: oklch(0.586 0.253 17.585);
  --color-rose-700: oklch(0.514 0.222 16.935);
  --color-rose-800: oklch(0.455 0.188 13.697);
  --color-gray-50: oklch(0.985 0.002 247.839);
  --color-gray-100: oklch(0.967 0.003 264.542);
  --color-gray-200: oklch(0.928 0.006 264.531);
  --color-gray-300: oklch(0.872 0.01 258.338);
  --color-gray-400: oklch(0.707 0.022 261.325);
  --color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-neutral-900: oklch(0.205 0 0);
  --color-black: #000;
  --color-white: #fff;
  --spacing: 0.25rem;
  --container-md: 28rem;
  --container-xl: 36rem;
  --container-3xl: 48rem;
  --container-6xl: 72rem;
  --text-xs: 0.75rem;
  --text-xs--line-height: calc(1 / 0.75);
  --text-sm: 0.875rem;
  --text-sm--line-height: calc(1.25 / 0.875);
  --text-base: 1rem;
  --text-base--line-height: calc(1.5 / 1);
  --text-lg: 1.125rem;
  --text-lg--line-height: calc(1.75 / 1.125);
  --text-xl: 1.25rem;
  --text-xl--line-height: calc(1.75 / 1.25);
  --text-2xl: 1.5rem;
  --text-2xl--line-height: calc(2 / 1.5);
  --text-3xl: 1.875rem;
  --text-3xl--line-height: calc(2.25 / 1.875);
  --text-4xl: 2.25rem;
  --text-4xl--line-height: calc(2.5 / 2.25);
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --tracking-wider: 0.05em;
  --leading-tight: 1.25;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --aspect-video: 16 / 9;
  --default-transition-duration: 150ms;
  --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  --default-font-family: var(--font-sans);
  --default-font-feature-settings: var(--font-sans--font-feature-settings);
  --default-font-variation-settings: var(--font-sans--font-variation-settings);
  --default-mono-font-family: var(--font-mono);
  --default-mono-font-feature-settings: var(--font-mono--font-feature-settings);
  --default-mono-font-variation-settings: var(--font-mono--font-variation-settings);
  --color-background: var(--wp--preset--color--background);
  --color-foreground: var(--wp--preset--color--foreground);
  --color-primary: var(--wp--preset--color--primary);
  --color-secondary: var(--wp--preset--color--secondary);
  --color-tertiary: var(--wp--preset--color--tertiary);
  --container-content: var(--wp--style--global--content-size);
  --container-wide: var(--wp--style--global--wide-size);
}
:root {
  --font-heading-primary: "trajan-pro-3", serif;
  --font-heading-secondary: "futura-pt", sans-serif;
  --font-body: "futura-pt", sans-serif;
}
html, body {
  font-family: var(--font-body);
}
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading-secondary);
  font-weight: 500;
}
h1, .entry-title, .page-title {
  font-family: var(--font-heading-primary);
  font-weight: 600;
}
.font-trajan {
  font-family: var(--font-heading-primary);
}
.font-futura {
  font-family: var(--font-heading-secondary);
}
.shadow-text {
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.8);
}
.article-content h2 {
  font-family: var(--font-trajan), serif;
  color: #1f2937;
  margin-top: 2em;
  font-weight: 700;
}
.article-content h3 {
  color: #374151;
  margin-top: 1.5em;
  font-weight: 600;
}
.article-content p {
  margin-bottom: 1.25em;
}
.article-content blockquote {
  border-left-color: #f59e0b;
  background-color: #fffbeb;
}
.article-content figure {
  margin: 2em 0;
}
.article-content figcaption {
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
  margin-top: 0.5em;
}
.article-content img {
  border-radius: 0.375rem;
}
.article-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1.5em 0;
}
.article-content table th {
  background-color: #f9fafb;
  font-weight: 600;
}
.article-content table th, .article-content table td {
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  text-align: left;
}
.gallery-item img {
  transition: transform 0.4s ease;
}
.gallery-item:hover img {
  transform: scale(1.05);
}
.card-elevation {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.card-elevation:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.progress-container {
  width: 100%;
  height: 4px;
  background-color: transparent;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  transition: opacity 0.3s ease;
}
.progress-bar {
  height: 4px;
  background-color: #f59e0b;
  width: 0%;
  transition: width 0.1s ease;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.entry-title {
  text-align: left !important;
}
*, ::after, ::before, ::backdrop, ::file-selector-button {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0 solid;
}
html, :host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  tab-size: 4;
  font-family: var( --default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' );
  font-feature-settings: var(--default-font-feature-settings, normal);
  font-variation-settings: var(--default-font-variation-settings, normal);
  -webkit-tap-highlight-color: transparent;
}
body {
  line-height: inherit;
}
hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}
h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}
a {
  color: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}
b, strong {
  font-weight: bolder;
}
code, kbd, samp, pre {
  font-family: var( --default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace );
  font-feature-settings: var(--default-mono-font-feature-settings, normal);
  font-variation-settings: var(--default-mono-font-variation-settings, normal);
  font-size: 1em;
}
small {
  font-size: 80%;
}
sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}
:-moz-focusring {
  outline: auto;
}
progress {
  vertical-align: baseline;
}
summary {
  display: list-item;
}
ol, ul, menu {
  list-style: none;
}
img, svg, video, canvas, audio, iframe, embed, object {
  display: block;
  vertical-align: middle;
}
img, video {
  max-width: 100%;
  height: auto;
}
button, input, select, optgroup, textarea, ::file-selector-button {
  font: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  letter-spacing: inherit;
  color: inherit;
  border-radius: 0;
  background-color: transparent;
  opacity: 1;
}
:where(select:is([multiple], [size])) optgroup {
  font-weight: bolder;
}
:where(select:is([multiple], [size])) optgroup option {
  padding-inline-start: 20px;
}
::file-selector-button {
  margin-inline-end: 4px;
}
::placeholder {
  opacity: 1;
  color: color-mix(in oklab, currentColor 50%, transparent);
}
textarea {
  resize: vertical;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-date-and-time-value {
  min-height: 1lh;
  text-align: inherit;
}
::-webkit-datetime-edit {
  display: inline-flex;
}
::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}
::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
  padding-block: 0;
}
:-moz-ui-invalid {
  box-shadow: none;
}
button, input:where([type='button'], [type='reset'], [type='submit']), ::file-selector-button {
  appearance: button;
}
::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}
[hidden]:where(:not([hidden='until-found'])) {
  display: none !important;
}
body {
  background-color: var(--color-background);
  font-family: var(--font-sans);
  color: var(--color-foreground);
}
.container {
  margin-inline: auto;
  padding-inline: calc(var(--spacing) * 4);
  max-width: 1200px;
}
.site-header {
  width: 100%;
  background-color: var(--color-white);
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.site-branding {
  width: 100%;
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: var(--color-amber-100);
  --tw-gradient-position: to right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
  --tw-gradient-from: var(--color-amber-50);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  --tw-gradient-to: var(--color-white);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.header-content {
  margin-inline: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-block: calc(var(--spacing) * 3);
}
.site-logo {
  flex-shrink: 0;
}
.site-title {
  margin: calc(var(--spacing) * 0);
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
}
.site-title a {
  display: block;
  text-decoration-line: none;
}
.logo-img {
  height: calc(var(--spacing) * 14);
  width: auto;
}
.utility-nav {
  display: flex;
  align-items: center;
}
.utility-links {
  margin: calc(var(--spacing) * 0);
  display: flex;
  list-style-type: none;
  padding: calc(var(--spacing) * 0);
}
.utility-links li {
  margin-inline: calc(var(--spacing) * 2);
}
.utility-links a {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  color: var(--color-amber-800);
  text-transform: uppercase;
  text-decoration-line: none;
}
@media (hover: hover) {
  :is(.utility-links a):hover {
    color: var(--color-amber-950);
  }
}
.search-form {
  margin-left: calc(var(--spacing) * 4);
  display: flex;
  align-items: center;
}
.search-form input[type="search"] {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-amber-200);
  background-color: var(--color-white);
  padding-inline: calc(var(--spacing) * 3);
  padding-block: calc(var(--spacing) * 1);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  width: 150px;
}
.search-form .search-submit {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-amber-800);
  background-color: var(--color-amber-800);
  padding-inline: calc(var(--spacing) * 3);
  padding-block: calc(var(--spacing) * 1);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  color: var(--color-white);
}
.menu-toggle {
  margin-left: calc(var(--spacing) * 4);
  display: none;
  border-style: var(--tw-border-style);
  border-width: 0px;
  background-color: transparent;
  padding: calc(var(--spacing) * 2);
}
@media (max-width: 768px) {
  .menu-toggle {
    display: block;
  }
  .utility-links {
    display: none;
  }
  .utility-nav .search-form {
    display: none;
  }
}
.menu-icon {
  position: relative;
  display: block;
  height: calc(var(--spacing) * 0.5);
  width: calc(var(--spacing) * 6);
  background-color: var(--color-amber-800);
}
.menu-icon:before, .menu-icon:after {
  position: absolute;
  left: calc(var(--spacing) * 0);
  display: block;
  height: calc(var(--spacing) * 0.5);
  width: calc(var(--spacing) * 6);
  background-color: var(--color-amber-800);
  content: '';
}
.menu-icon:before {
  top: -6px;
}
.menu-icon:after {
  bottom: -6px;
}
.main-navigation {
  width: 100%;
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: var(--color-amber-100);
  background-color: var(--color-white);
}
.main-navigation .container {
  display: flex;
  justify-content: space-between;
}
#primary-menu {
  margin: calc(var(--spacing) * 0);
  display: flex;
  list-style-type: none;
  padding: calc(var(--spacing) * 0);
}
#primary-menu > li {
  position: relative;
}
#primary-menu > li > a {
  display: block;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 3);
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-amber-900);
  text-transform: uppercase;
  text-decoration-line: none;
}
@media (hover: hover) {
  :is(#primary-menu > li > a):hover {
    color: var(--color-amber-950);
  }
}
#primary-menu > li.current-menu-item > a {
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
}
.sub-menu {
  position: absolute;
  z-index: 10;
  display: none;
  min-width: 200px;
  border-radius: 0.25rem;
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-200);
  background-color: var(--color-white);
  padding: calc(var(--spacing) * 2);
  --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
#primary-menu > li:hover > .sub-menu {
  display: block;
}
.sub-menu li {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: var(--color-gray-100);
}
:is(.sub-menu li):last-child {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 0px;
}
.sub-menu a {
  display: block;
  padding-inline: calc(var(--spacing) * 3);
  padding-block: calc(var(--spacing) * 2);
  color: var(--color-gray-700);
  text-decoration-line: none;
}
@media (hover: hover) {
  :is(.sub-menu a):hover {
    background-color: var(--color-gray-50);
  }
}
@media (hover: hover) {
  :is(.sub-menu a):hover {
    color: var(--color-gray-900);
  }
}
.mobile-menu {
  width: 100%;
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
  border-color: var(--color-amber-100);
  background-color: var(--color-white);
  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.mobile-menu .container {
  margin-inline: auto;
}
#mobile-menu-items {
  margin: calc(var(--spacing) * 0);
  list-style-type: none;
  padding: calc(var(--spacing) * 0);
}
#mobile-menu-items li {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: var(--color-gray-100);
}
#mobile-menu-items a {
  display: block;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 3);
  color: var(--color-gray-700);
  text-decoration-line: none;
}
.mobile-utility-links {
  margin: calc(var(--spacing) * 0);
  list-style-type: none;
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
  border-color: var(--color-amber-100);
  background-color: var(--color-amber-50);
  padding: calc(var(--spacing) * 0);
}
.mobile-utility-links li {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: var(--color-gray-100);
}
:is(.mobile-utility-links li):last-child {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 0px;
}
.mobile-utility-links a {
  display: block;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 3);
  color: var(--color-amber-800);
  text-decoration-line: none;
}
.breadcrumbs {
  width: 100%;
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: var(--color-amber-100);
  background-color: var(--color-amber-50);
  padding-block: calc(var(--spacing) * 2);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  color: var(--color-amber-800);
}
.breadcrumbs .container {
  width: 100%;
}
.breadcrumbs a {
  color: var(--color-amber-800);
  text-decoration-line: none;
}
@media (hover: hover) {
  :is(.breadcrumbs a):hover {
    color: var(--color-amber-950);
  }
}
.page-title, .entry-title {
  margin-inline: auto;
  margin-bottom: calc(var(--spacing) * 6);
  max-width: var(--container-content);
  font-size: var(--text-3xl);
  line-height: var(--tw-leading, var(--text-3xl--line-height));
  --tw-font-weight: var(--font-weight-extrabold);
  font-weight: var(--font-weight-extrabold);
  color: var(--color-neutral-900);
}
.page-content > *, .entry-content > * {
  margin-inline: auto;
  max-width: var(--container-content);
}
.entry-content > .alignwide {
  max-width: var(--container-wide);
}
.entry-content > .alignfull {
  max-width: none;
}
.entry-content > .alignleft {
  float: left;
  margin-right: calc(var(--spacing) * 8);
}
.entry-content > .alignright {
  float: right;
  margin-left: calc(var(--spacing) * 8);
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.relative {
  position: relative;
}
.static {
  position: static;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: calc(var(--spacing) * 0);
}
.top-1\/2 {
  top: calc(1/2 * 100%);
}
.top-2 {
  top: calc(var(--spacing) * 2);
}
.top-3 {
  top: calc(var(--spacing) * 3);
}
.top-4 {
  top: calc(var(--spacing) * 4);
}
.top-6 {
  top: calc(var(--spacing) * 6);
}
.right-0 {
  right: calc(var(--spacing) * 0);
}
.right-3 {
  right: calc(var(--spacing) * 3);
}
.right-4 {
  right: calc(var(--spacing) * 4);
}
.-bottom-5 {
  bottom: calc(var(--spacing) * -5);
}
.bottom-0 {
  bottom: calc(var(--spacing) * 0);
}
.bottom-3 {
  bottom: calc(var(--spacing) * 3);
}
.bottom-4 {
  bottom: calc(var(--spacing) * 4);
}
.left-0 {
  left: calc(var(--spacing) * 0);
}
.left-3 {
  left: calc(var(--spacing) * 3);
}
.left-4 {
  left: calc(var(--spacing) * 4);
}
.left-6 {
  left: calc(var(--spacing) * 6);
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-50 {
  z-index: 50;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.float-left {
  float: left;
}
.float-right {
  float: right;
}
.container {
  width: 100%;
}
@media (width >= 40rem) {
  .container {
    max-width: 40rem;
  }
}
@media (width >= 48rem) {
  .container {
    max-width: 48rem;
  }
}
@media (width >= 64rem) {
  .container {
    max-width: 64rem;
  }
}
@media (width >= 80rem) {
  .container {
    max-width: 80rem;
  }
}
@media (width >= 96rem) {
  .container {
    max-width: 96rem;
  }
}
.mx-4 {
  margin-inline: calc(var(--spacing) * 4);
}
.mx-auto {
  margin-inline: auto;
}
.my-6 {
  margin-block: calc(var(--spacing) * 6);
}
.my-8 {
  margin-block: calc(var(--spacing) * 8);
}
.prose {
  color: var(--tw-prose-body);
  max-width: none;
}
.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where([class~="lead"],[class~="is-style-lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}
.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}
.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}
.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}
.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}
.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}
.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}
.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1;
  margin-top: 3em;
  margin-bottom: 3em;
  border-bottom: none;
}
.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
  border-left-style: solid;
}
.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}
.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}
.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}
.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}
.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}
.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}
.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}
.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}
.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "`";
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "`";
}
.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}
.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}
.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}
.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}
.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}
.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}
.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}
.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}
.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}
.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}
.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}
.prose :where(th,td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}
.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}
.prose {
  --tw-prose-body: oklch(0.373 0.034 259.733);
  --tw-prose-headings: oklch(0.21 0.034 264.665);
  --tw-prose-lead: oklch(0.446 0.03 256.802);
  --tw-prose-links: oklch(0.21 0.034 264.665);
  --tw-prose-bold: oklch(0.21 0.034 264.665);
  --tw-prose-counters: oklch(0.551 0.027 264.364);
  --tw-prose-bullets: oklch(0.872 0.01 258.338);
  --tw-prose-hr: oklch(0.928 0.006 264.531);
  --tw-prose-quotes: oklch(0.21 0.034 264.665);
  --tw-prose-quote-borders: oklch(0.928 0.006 264.531);
  --tw-prose-captions: oklch(0.551 0.027 264.364);
  --tw-prose-kbd: oklch(0.21 0.034 264.665);
  --tw-prose-kbd-shadows: NaN NaN NaN;
  --tw-prose-code: oklch(0.21 0.034 264.665);
  --tw-prose-pre-code: oklch(0.928 0.006 264.531);
  --tw-prose-pre-bg: oklch(0.278 0.033 256.848);
  --tw-prose-th-borders: oklch(0.872 0.01 258.338);
  --tw-prose-td-borders: oklch(0.928 0.006 264.531);
  --tw-prose-invert-body: oklch(0.872 0.01 258.338);
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: oklch(0.707 0.022 261.325);
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: oklch(0.707 0.022 261.325);
  --tw-prose-invert-bullets: oklch(0.446 0.03 256.802);
  --tw-prose-invert-hr: oklch(0.373 0.034 259.733);
  --tw-prose-invert-quotes: oklch(0.967 0.003 264.542);
  --tw-prose-invert-quote-borders: oklch(0.373 0.034 259.733);
  --tw-prose-invert-captions: oklch(0.707 0.022 261.325);
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: oklch(0.872 0.01 258.338);
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: oklch(0.446 0.03 256.802);
  --tw-prose-invert-td-borders: oklch(0.373 0.034 259.733);
  font-size: 1rem;
  line-height: 1.75;
}
.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}
.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}
.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose :where(tbody td,tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}
.prose :where(tbody td:first-child,tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose :where(tbody td:last-child,tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}
.prose :where(blockquote > cite):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-body);
  font-style: normal;
  font-weight: 400;
}
.prose :where(blockquote > cite):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "\2014";
}
.prose :where(table.has-fixed-layout):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  table-layout: fixed;
  width: 100%;
}
.prose-lg {
  font-size: 1.125rem;
  line-height: 1.7777778;
}
.prose-lg :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}
.prose-lg :where([class~="lead"],[class~="is-style-lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2222222em;
  line-height: 1.4545455;
  margin-top: 1.0909091em;
  margin-bottom: 1.0909091em;
}
.prose-lg :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  padding-inline-start: 1em;
}
.prose-lg :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.6666667em;
  margin-top: 0;
  margin-bottom: 0.8333333em;
  line-height: 1;
}
.prose-lg :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.6666667em;
  margin-top: 1.8666667em;
  margin-bottom: 1.0666667em;
  line-height: 1.3333333;
}
.prose-lg :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.3333333em;
  margin-top: 1.6666667em;
  margin-bottom: 0.6666667em;
  line-height: 1.5;
}
.prose-lg :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}
.prose-lg :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-lg :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  border-radius: 0.3125rem;
  padding-top: 0.2222222em;
  padding-inline-end: 0.4444444em;
  padding-bottom: 0.2222222em;
  padding-inline-start: 0.4444444em;
}
.prose-lg :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
}
.prose-lg :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8666667em;
}
.prose-lg :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.875em;
}
.prose-lg :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.75;
  margin-top: 2em;
  margin-bottom: 2em;
  border-radius: 0.375rem;
  padding-top: 1em;
  padding-inline-end: 1.5em;
  padding-bottom: 1em;
  padding-inline-start: 1.5em;
}
.prose-lg :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-inline-start: 1.5555556em;
}
.prose-lg :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-inline-start: 1.5555556em;
}
.prose-lg :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6666667em;
  margin-bottom: 0.6666667em;
}
.prose-lg :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4444444em;
}
.prose-lg :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4444444em;
}
.prose-lg :where(.prose-lg > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}
.prose-lg :where(.prose-lg > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}
.prose-lg :where(.prose-lg > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}
.prose-lg :where(.prose-lg > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}
.prose-lg :where(.prose-lg > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}
.prose-lg :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}
.prose-lg :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}
.prose-lg :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}
.prose-lg :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6666667em;
  padding-inline-start: 1.5555556em;
}
.prose-lg :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 3.1111111em;
  margin-bottom: 3.1111111em;
}
.prose-lg :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.5;
}
.prose-lg :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0.75em;
  padding-bottom: 0.75em;
  padding-inline-start: 0.75em;
}
.prose-lg :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose-lg :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose-lg :where(tbody td,tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.75em;
  padding-inline-end: 0.75em;
  padding-bottom: 0.75em;
  padding-inline-start: 0.75em;
}
.prose-lg :where(tbody td:first-child,tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose-lg :where(tbody td:last-child,tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose-lg :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-lg :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.5;
  margin-top: 1em;
}
.prose-lg :where(.prose-lg > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(.prose-lg > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}
.mt-1 {
  margin-top: calc(var(--spacing) * 1);
}
.mt-2 {
  margin-top: calc(var(--spacing) * 2);
}
.mt-3 {
  margin-top: calc(var(--spacing) * 3);
}
.mt-4 {
  margin-top: calc(var(--spacing) * 4);
}
.mt-6 {
  margin-top: calc(var(--spacing) * 6);
}
.mt-8 {
  margin-top: calc(var(--spacing) * 8);
}
.mt-auto {
  margin-top: auto;
}
.mr-1 {
  margin-right: calc(var(--spacing) * 1);
}
.mr-2 {
  margin-right: calc(var(--spacing) * 2);
}
.mr-3 {
  margin-right: calc(var(--spacing) * 3);
}
.mb-1 {
  margin-bottom: calc(var(--spacing) * 1);
}
.mb-2 {
  margin-bottom: calc(var(--spacing) * 2);
}
.mb-3 {
  margin-bottom: calc(var(--spacing) * 3);
}
.mb-4 {
  margin-bottom: calc(var(--spacing) * 4);
}
.mb-6 {
  margin-bottom: calc(var(--spacing) * 6);
}
.mb-8 {
  margin-bottom: calc(var(--spacing) * 8);
}
.mb-12 {
  margin-bottom: calc(var(--spacing) * 12);
}
.ml-1 {
  margin-left: calc(var(--spacing) * 1);
}
.ml-2 {
  margin-left: calc(var(--spacing) * 2);
}
.ml-4 {
  margin-left: calc(var(--spacing) * 4);
}
.ml-auto {
  margin-left: auto;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.contents {
  display: contents;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-video {
  aspect-ratio: var(--aspect-video);
}
.h-2\.5 {
  height: calc(var(--spacing) * 2.5);
}
.h-3 {
  height: calc(var(--spacing) * 3);
}
.h-3\.5 {
  height: calc(var(--spacing) * 3.5);
}
.h-4 {
  height: calc(var(--spacing) * 4);
}
.h-5 {
  height: calc(var(--spacing) * 5);
}
.h-6 {
  height: calc(var(--spacing) * 6);
}
.h-8 {
  height: calc(var(--spacing) * 8);
}
.h-10 {
  height: calc(var(--spacing) * 10);
}
.h-12 {
  height: calc(var(--spacing) * 12);
}
.h-16 {
  height: calc(var(--spacing) * 16);
}
.h-20 {
  height: calc(var(--spacing) * 20);
}
.h-24 {
  height: calc(var(--spacing) * 24);
}
.h-28 {
  height: calc(var(--spacing) * 28);
}
.h-32 {
  height: calc(var(--spacing) * 32);
}
.h-44 {
  height: calc(var(--spacing) * 44);
}
.h-48 {
  height: calc(var(--spacing) * 48);
}
.h-64 {
  height: calc(var(--spacing) * 64);
}
.h-\[300px\] {
  height: 300px;
}
.h-\[400px\] {
  height: 400px;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.max-h-\[600px\] {
  max-height: 600px;
}
.w-1\/2 {
  width: calc(1/2 * 100%);
}
.w-2\.5 {
  width: calc(var(--spacing) * 2.5);
}
.w-2\/3 {
  width: calc(2/3 * 100%);
}
.w-3 {
  width: calc(var(--spacing) * 3);
}
.w-3\.5 {
  width: calc(var(--spacing) * 3.5);
}
.w-3\/4 {
  width: calc(3/4 * 100%);
}
.w-4 {
  width: calc(var(--spacing) * 4);
}
.w-5 {
  width: calc(var(--spacing) * 5);
}
.w-6 {
  width: calc(var(--spacing) * 6);
}
.w-8 {
  width: calc(var(--spacing) * 8);
}
.w-10 {
  width: calc(var(--spacing) * 10);
}
.w-12 {
  width: calc(var(--spacing) * 12);
}
.w-16 {
  width: calc(var(--spacing) * 16);
}
.w-20 {
  width: calc(var(--spacing) * 20);
}
.w-24 {
  width: calc(var(--spacing) * 24);
}
.w-48 {
  width: calc(var(--spacing) * 48);
}
.w-auto {
  width: auto;
}
.w-full {
  width: 100%;
}
.max-w-3xl {
  max-width: var(--container-3xl);
}
.max-w-6xl {
  max-width: var(--container-6xl);
}
.max-w-content {
  max-width: var(--container-content);
}
.max-w-md {
  max-width: var(--container-md);
}
.max-w-none {
  max-width: none;
}
.max-w-xl {
  max-width: var(--container-xl);
}
.min-w-0 {
  min-width: calc(var(--spacing) * 0);
}
.min-w-\[200px\] {
  min-width: 200px;
}
.flex-1 {
  flex: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.border-collapse {
  border-collapse: collapse;
}
.-translate-x-full {
  --tw-translate-x: -100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-x-0 {
  --tw-translate-x: calc(var(--spacing) * 0);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-x-full {
  --tw-translate-x: 100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.-translate-y-1\/2 {
  --tw-translate-y: calc(calc(1/2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.translate-y-2 {
  --tw-translate-y: calc(var(--spacing) * 2);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.transform {
  transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
}
.resize {
  resize: both;
}
.list-disc {
  list-style-type: disc;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.place-content-center {
  place-content: center;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.items-start {
  align-items: flex-start;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.justify-start {
  justify-content: flex-start;
}
.justify-items-center {
  justify-items: center;
}
.gap-2 {
  gap: calc(var(--spacing) * 2);
}
.gap-3 {
  gap: calc(var(--spacing) * 3);
}
.gap-4 {
  gap: calc(var(--spacing) * 4);
}
.gap-6 {
  gap: calc(var(--spacing) * 6);
}
:where(.space-y-1 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
}
:where(.space-y-2 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
}
:where(.space-y-3 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
}
:where(.space-y-4 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
}
:where(.space-y-6 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
}
.gap-x-8 {
  column-gap: calc(var(--spacing) * 8);
}
:where(.space-x-1 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
}
:where(.space-x-2 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
}
:where(.space-x-3 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
}
:where(.space-x-4 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
}
.gap-y-6 {
  row-gap: calc(var(--spacing) * 6);
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-full {
  border-radius: calc(infinity * 1px);
}
.rounded-lg {
  border-radius: var(--radius-lg);
}
.rounded-md {
  border-radius: var(--radius-md);
}
.rounded-xl {
  border-radius: var(--radius-xl);
}
.rounded-l-md {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}
.rounded-r-md {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}
.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}
.border-0 {
  border-style: var(--tw-border-style);
  border-width: 0px;
}
.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}
.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}
.border-amber-200 {
  border-color: var(--color-amber-200);
}
.border-cyan-200 {
  border-color: var(--color-cyan-200);
}
.border-emerald-200 {
  border-color: var(--color-emerald-200);
}
.border-gray-100 {
  border-color: var(--color-gray-100);
}
.border-gray-200 {
  border-color: var(--color-gray-200);
}
.border-gray-300 {
  border-color: var(--color-gray-300);
}
.border-orange-200 {
  border-color: var(--color-orange-200);
}
.border-purple-200 {
  border-color: var(--color-purple-200);
}
.border-teal-200 {
  border-color: var(--color-teal-200);
}
.border-white\/20 {
  border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
}
.bg-amber-50 {
  background-color: var(--color-amber-50);
}
.bg-amber-100 {
  background-color: var(--color-amber-100);
}
.bg-amber-600 {
  background-color: var(--color-amber-600);
}
.bg-background {
  background-color: var(--color-background);
}
.bg-black {
  background-color: var(--color-black);
}
.bg-black\/20 {
  background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
}
.bg-black\/30 {
  background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
}
.bg-black\/60 {
  background-color: color-mix(in oklab, var(--color-black) 60%, transparent);
}
.bg-blue-600 {
  background-color: var(--color-blue-600);
}
.bg-blue-700 {
  background-color: var(--color-blue-700);
}
.bg-cyan-50 {
  background-color: var(--color-cyan-50);
}
.bg-cyan-100 {
  background-color: var(--color-cyan-100);
}
.bg-cyan-600 {
  background-color: var(--color-cyan-600);
}
.bg-emerald-50 {
  background-color: var(--color-emerald-50);
}
.bg-emerald-100 {
  background-color: var(--color-emerald-100);
}
.bg-emerald-600 {
  background-color: var(--color-emerald-600);
}
.bg-gray-50 {
  background-color: var(--color-gray-50);
}
.bg-gray-100 {
  background-color: var(--color-gray-100);
}
.bg-gray-200 {
  background-color: var(--color-gray-200);
}
.bg-gray-600 {
  background-color: var(--color-gray-600);
}
.bg-gray-700 {
  background-color: var(--color-gray-700);
}
.bg-gray-800 {
  background-color: var(--color-gray-800);
}
.bg-indigo-100 {
  background-color: var(--color-indigo-100);
}
.bg-indigo-600 {
  background-color: var(--color-indigo-600);
}
.bg-orange-50 {
  background-color: var(--color-orange-50);
}
.bg-orange-100 {
  background-color: var(--color-orange-100);
}
.bg-orange-600 {
  background-color: var(--color-orange-600);
}
.bg-purple-50 {
  background-color: var(--color-purple-50);
}
.bg-purple-100 {
  background-color: var(--color-purple-100);
}
.bg-purple-600 {
  background-color: var(--color-purple-600);
}
.bg-rose-100 {
  background-color: var(--color-rose-100);
}
.bg-rose-600 {
  background-color: var(--color-rose-600);
}
.bg-teal-50 {
  background-color: var(--color-teal-50);
}
.bg-teal-100 {
  background-color: var(--color-teal-100);
}
.bg-teal-600 {
  background-color: var(--color-teal-600);
}
.bg-white {
  background-color: var(--color-white);
}
.bg-white\/30 {
  background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
}
.bg-white\/50 {
  background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
}
.bg-white\/80 {
  background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
}
.bg-white\/90 {
  background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
}
.bg-gradient-to-br {
  --tw-gradient-position: to bottom right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.bg-gradient-to-l {
  --tw-gradient-position: to left in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  --tw-gradient-position: to right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  --tw-gradient-position: to top in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.from-amber-50 {
  --tw-gradient-from: var(--color-amber-50);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-amber-500 {
  --tw-gradient-from: var(--color-amber-500);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-black {
  --tw-gradient-from: var(--color-black);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-black\/50 {
  --tw-gradient-from: color-mix(in oklab, var(--color-black) 50%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-black\/60 {
  --tw-gradient-from: color-mix(in oklab, var(--color-black) 60%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-black\/70 {
  --tw-gradient-from: color-mix(in oklab, var(--color-black) 70%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-black\/80 {
  --tw-gradient-from: color-mix(in oklab, var(--color-black) 80%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-blue-500 {
  --tw-gradient-from: var(--color-blue-500);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-cyan-500 {
  --tw-gradient-from: var(--color-cyan-500);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-emerald-500 {
  --tw-gradient-from: var(--color-emerald-500);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-gray-50 {
  --tw-gradient-from: var(--color-gray-50);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-gray-800 {
  --tw-gradient-from: var(--color-gray-800);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-indigo-500 {
  --tw-gradient-from: var(--color-indigo-500);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-orange-500 {
  --tw-gradient-from: var(--color-orange-500);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-purple-500 {
  --tw-gradient-from: var(--color-purple-500);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-rose-500 {
  --tw-gradient-from: var(--color-rose-500);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-teal-500 {
  --tw-gradient-from: var(--color-teal-500);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-transparent {
  --tw-gradient-from: transparent;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.from-white {
  --tw-gradient-from: var(--color-white);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.via-black\/20 {
  --tw-gradient-via: color-mix(in oklab, var(--color-black) 20%, transparent);
  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-via-stops);
}
.via-black\/30 {
  --tw-gradient-via: color-mix(in oklab, var(--color-black) 30%, transparent);
  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-via-stops);
}
.via-black\/50 {
  --tw-gradient-via: color-mix(in oklab, var(--color-black) 50%, transparent);
  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-via-stops);
}
.via-gray-300 {
  --tw-gradient-via: var(--color-gray-300);
  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-via-stops);
}
.to-amber-100 {
  --tw-gradient-to: var(--color-amber-100);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-amber-700 {
  --tw-gradient-to: var(--color-amber-700);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-blue-700 {
  --tw-gradient-to: var(--color-blue-700);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-cyan-700 {
  --tw-gradient-to: var(--color-cyan-700);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-emerald-700 {
  --tw-gradient-to: var(--color-emerald-700);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-gray-100 {
  --tw-gradient-to: var(--color-gray-100);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-gray-900 {
  --tw-gradient-to: var(--color-gray-900);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-indigo-700 {
  --tw-gradient-to: var(--color-indigo-700);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-orange-700 {
  --tw-gradient-to: var(--color-orange-700);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-purple-700 {
  --tw-gradient-to: var(--color-purple-700);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-rose-700 {
  --tw-gradient-to: var(--color-rose-700);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-teal-700 {
  --tw-gradient-to: var(--color-teal-700);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-transparent {
  --tw-gradient-to: transparent;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-transparent\/20 {
  --tw-gradient-to: color-mix(in oklab, transparent 20%, transparent);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.to-white {
  --tw-gradient-to: var(--color-white);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.object-cover {
  object-fit: cover;
}
.p-2 {
  padding: calc(var(--spacing) * 2);
}
.p-3 {
  padding: calc(var(--spacing) * 3);
}
.p-4 {
  padding: calc(var(--spacing) * 4);
}
.p-5 {
  padding: calc(var(--spacing) * 5);
}
.p-6 {
  padding: calc(var(--spacing) * 6);
}
.p-8 {
  padding: calc(var(--spacing) * 8);
}
.px-1 {
  padding-inline: calc(var(--spacing) * 1);
}
.px-2 {
  padding-inline: calc(var(--spacing) * 2);
}
.px-3 {
  padding-inline: calc(var(--spacing) * 3);
}
.px-4 {
  padding-inline: calc(var(--spacing) * 4);
}
.px-6 {
  padding-inline: calc(var(--spacing) * 6);
}
.py-1 {
  padding-block: calc(var(--spacing) * 1);
}
.py-1\.5 {
  padding-block: calc(var(--spacing) * 1.5);
}
.py-2 {
  padding-block: calc(var(--spacing) * 2);
}
.py-3 {
  padding-block: calc(var(--spacing) * 3);
}
.py-6 {
  padding-block: calc(var(--spacing) * 6);
}
.py-8 {
  padding-block: calc(var(--spacing) * 8);
}
.py-10 {
  padding-block: calc(var(--spacing) * 10);
}
.py-16 {
  padding-block: calc(var(--spacing) * 16);
}
.pt-1 {
  padding-top: calc(var(--spacing) * 1);
}
.pt-2 {
  padding-top: calc(var(--spacing) * 2);
}
.pt-3 {
  padding-top: calc(var(--spacing) * 3);
}
.pt-4 {
  padding-top: calc(var(--spacing) * 4);
}
.pt-6 {
  padding-top: calc(var(--spacing) * 6);
}
.pt-8 {
  padding-top: calc(var(--spacing) * 8);
}
.pt-14 {
  padding-top: calc(var(--spacing) * 14);
}
.pr-12 {
  padding-right: calc(var(--spacing) * 12);
}
.pb-0 {
  padding-bottom: calc(var(--spacing) * 0);
}
.pb-2 {
  padding-bottom: calc(var(--spacing) * 2);
}
.pb-3 {
  padding-bottom: calc(var(--spacing) * 3);
}
.pb-4 {
  padding-bottom: calc(var(--spacing) * 4);
}
.pb-6 {
  padding-bottom: calc(var(--spacing) * 6);
}
.pl-4 {
  padding-left: calc(var(--spacing) * 4);
}
.pl-6 {
  padding-left: calc(var(--spacing) * 6);
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-2xl {
  font-size: var(--text-2xl);
  line-height: var(--tw-leading, var(--text-2xl--line-height));
}
.text-3xl {
  font-size: var(--text-3xl);
  line-height: var(--tw-leading, var(--text-3xl--line-height));
}
.text-base {
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
}
.text-lg {
  font-size: var(--text-lg);
  line-height: var(--tw-leading, var(--text-lg--line-height));
}
.text-sm {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
}
.text-xl {
  font-size: var(--text-xl);
  line-height: var(--tw-leading, var(--text-xl--line-height));
}
.text-xs {
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
}
.text-\[10px\] {
  font-size: 10px;
}
.leading-6 {
  --tw-leading: calc(var(--spacing) * 6);
  line-height: calc(var(--spacing) * 6);
}
.leading-tight {
  --tw-leading: var(--leading-tight);
  line-height: var(--leading-tight);
}
.font-bold {
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
}
.font-extrabold {
  --tw-font-weight: var(--font-weight-extrabold);
  font-weight: var(--font-weight-extrabold);
}
.font-medium {
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
}
.font-semibold {
  --tw-font-weight: var(--font-weight-semibold);
  font-weight: var(--font-weight-semibold);
}
.tracking-wider {
  --tw-tracking: var(--tracking-wider);
  letter-spacing: var(--tracking-wider);
}
.whitespace-nowrap {
  white-space: nowrap;
}
.text-amber-50 {
  color: var(--color-amber-50);
}
.text-amber-200 {
  color: var(--color-amber-200);
}
.text-amber-300 {
  color: var(--color-amber-300);
}
.text-amber-600 {
  color: var(--color-amber-600);
}
.text-amber-800 {
  color: var(--color-amber-800);
}
.text-amber-900 {
  color: var(--color-amber-900);
}
.text-blue-500 {
  color: var(--color-blue-500);
}
.text-blue-600 {
  color: var(--color-blue-600);
}
.text-cyan-50 {
  color: var(--color-cyan-50);
}
.text-cyan-200 {
  color: var(--color-cyan-200);
}
.text-cyan-300 {
  color: var(--color-cyan-300);
}
.text-cyan-600 {
  color: var(--color-cyan-600);
}
.text-cyan-700 {
  color: var(--color-cyan-700);
}
.text-emerald-50 {
  color: var(--color-emerald-50);
}
.text-emerald-200 {
  color: var(--color-emerald-200);
}
.text-emerald-300 {
  color: var(--color-emerald-300);
}
.text-emerald-600 {
  color: var(--color-emerald-600);
}
.text-emerald-700 {
  color: var(--color-emerald-700);
}
.text-foreground {
  color: var(--color-foreground);
}
.text-gray-400 {
  color: var(--color-gray-400);
}
.text-gray-500 {
  color: var(--color-gray-500);
}
.text-gray-600 {
  color: var(--color-gray-600);
}
.text-gray-700 {
  color: var(--color-gray-700);
}
.text-gray-800 {
  color: var(--color-gray-800);
}
.text-gray-900 {
  color: var(--color-gray-900);
}
.text-indigo-300 {
  color: var(--color-indigo-300);
}
.text-indigo-600 {
  color: var(--color-indigo-600);
}
.text-orange-50 {
  color: var(--color-orange-50);
}
.text-orange-200 {
  color: var(--color-orange-200);
}
.text-orange-300 {
  color: var(--color-orange-300);
}
.text-orange-600 {
  color: var(--color-orange-600);
}
.text-orange-700 {
  color: var(--color-orange-700);
}
.text-purple-300 {
  color: var(--color-purple-300);
}
.text-purple-600 {
  color: var(--color-purple-600);
}
.text-red-500 {
  color: var(--color-red-500);
}
.text-rose-300 {
  color: var(--color-rose-300);
}
.text-rose-600 {
  color: var(--color-rose-600);
}
.text-teal-50 {
  color: var(--color-teal-50);
}
.text-teal-200 {
  color: var(--color-teal-200);
}
.text-teal-300 {
  color: var(--color-teal-300);
}
.text-teal-600 {
  color: var(--color-teal-600);
}
.text-white {
  color: var(--color-white);
}
.text-white\/80 {
  color: color-mix(in oklab, var(--color-white) 80%, transparent);
}
.text-white\/90 {
  color: color-mix(in oklab, var(--color-white) 90%, transparent);
}
.uppercase {
  text-transform: uppercase;
}
.italic {
  font-style: italic;
}
.no-underline {
  text-decoration-line: none;
}
.underline {
  text-decoration-line: underline;
}
.opacity-0 {
  opacity: 0%;
}
.opacity-70 {
  opacity: 70%;
}
.opacity-75 {
  opacity: 75%;
}
.opacity-100 {
  opacity: 100%;
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.shadow-none {
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.ring-1 {
  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.ring-gray-300 {
  --tw-ring-color: var(--color-gray-300);
}
.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}
.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
}
.filter {
  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
}
.transition {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.duration-200 {
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
.duration-300 {
  --tw-duration: 300ms;
  transition-duration: 300ms;
}
.duration-500 {
  --tw-duration: 500ms;
  transition-duration: 500ms;
}
.duration-700 {
  --tw-duration: 700ms;
  transition-duration: 700ms;
}
.duration-1000 {
  --tw-duration: 1000ms;
  transition-duration: 1000ms;
}
.ease-in-out {
  --tw-ease: var(--ease-in-out);
  transition-timing-function: var(--ease-in-out);
}
.content-none {
  --tw-content: none;
  content: none;
}
.prose-neutral {
  --tw-prose-body: oklch(0.371 0 0);
  --tw-prose-headings: oklch(0.205 0 0);
  --tw-prose-lead: oklch(0.439 0 0);
  --tw-prose-links: oklch(0.205 0 0);
  --tw-prose-bold: oklch(0.205 0 0);
  --tw-prose-counters: oklch(0.556 0 0);
  --tw-prose-bullets: oklch(0.87 0 0);
  --tw-prose-hr: oklch(0.922 0 0);
  --tw-prose-quotes: oklch(0.205 0 0);
  --tw-prose-quote-borders: oklch(0.922 0 0);
  --tw-prose-captions: oklch(0.556 0 0);
  --tw-prose-kbd: oklch(0.205 0 0);
  --tw-prose-kbd-shadows: NaN NaN NaN;
  --tw-prose-code: oklch(0.205 0 0);
  --tw-prose-pre-code: oklch(0.922 0 0);
  --tw-prose-pre-bg: oklch(0.269 0 0);
  --tw-prose-th-borders: oklch(0.87 0 0);
  --tw-prose-td-borders: oklch(0.922 0 0);
  --tw-prose-invert-body: oklch(0.87 0 0);
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: oklch(0.708 0 0);
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: oklch(0.708 0 0);
  --tw-prose-invert-bullets: oklch(0.439 0 0);
  --tw-prose-invert-hr: oklch(0.371 0 0);
  --tw-prose-invert-quotes: oklch(0.97 0 0);
  --tw-prose-invert-quote-borders: oklch(0.371 0 0);
  --tw-prose-invert-captions: oklch(0.708 0 0);
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: oklch(0.87 0 0);
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: oklch(0.439 0 0);
  --tw-prose-invert-td-borders: oklch(0.371 0 0);
}
.ring-inset {
  --tw-ring-inset: inset;
}
@media (hover: hover) {
  .group-hover\:translate-y-0:is(:where(.group):hover *) {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
}
@media (hover: hover) {
  .group-hover\:scale-105:is(:where(.group):hover *) {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}
@media (hover: hover) {
  .group-hover\:scale-110:is(:where(.group):hover *) {
    --tw-scale-x: 110%;
    --tw-scale-y: 110%;
    --tw-scale-z: 110%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}
@media (hover: hover) {
  .group-hover\:bg-amber-50:is(:where(.group):hover *) {
    background-color: var(--color-amber-50);
  }
}
@media (hover: hover) {
  .group-hover\:text-amber-600:is(:where(.group):hover *) {
    color: var(--color-amber-600);
  }
}
@media (hover: hover) {
  .group-hover\:text-amber-700:is(:where(.group):hover *) {
    color: var(--color-amber-700);
  }
}
@media (hover: hover) {
  .group-hover\:text-cyan-700:is(:where(.group):hover *) {
    color: var(--color-cyan-700);
  }
}
@media (hover: hover) {
  .group-hover\:text-emerald-700:is(:where(.group):hover *) {
    color: var(--color-emerald-700);
  }
}
@media (hover: hover) {
  .group-hover\:text-indigo-700:is(:where(.group):hover *) {
    color: var(--color-indigo-700);
  }
}
@media (hover: hover) {
  .group-hover\:text-orange-700:is(:where(.group):hover *) {
    color: var(--color-orange-700);
  }
}
@media (hover: hover) {
  .group-hover\:text-purple-700:is(:where(.group):hover *) {
    color: var(--color-purple-700);
  }
}
@media (hover: hover) {
  .group-hover\:text-rose-700:is(:where(.group):hover *) {
    color: var(--color-rose-700);
  }
}
@media (hover: hover) {
  .group-hover\:text-teal-700:is(:where(.group):hover *) {
    color: var(--color-teal-700);
  }
}
@media (hover: hover) {
  .group-hover\:opacity-100:is(:where(.group):hover *) {
    opacity: 100%;
  }
}
.last\:mb-0:last-child {
  margin-bottom: calc(var(--spacing) * 0);
}
.last\:border-0:last-child {
  border-style: var(--tw-border-style);
  border-width: 0px;
}
.last\:pb-0:last-child {
  padding-bottom: calc(var(--spacing) * 0);
}
@media (hover: hover) {
  .hover\:-translate-y-1:hover {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
}
@media (hover: hover) {
  .hover\:translate-y-px:hover {
    --tw-translate-y: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
}
@media (hover: hover) {
  .hover\:scale-105:hover {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}
@media (hover: hover) {
  .hover\:bg-amber-700:hover {
    background-color: var(--color-amber-700);
  }
}
@media (hover: hover) {
  .hover\:bg-black\/40:hover {
    background-color: color-mix(in oklab, var(--color-black) 40%, transparent);
  }
}
@media (hover: hover) {
  .hover\:bg-black\/80:hover {
    background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
  }
}
@media (hover: hover) {
  .hover\:bg-blue-700:hover {
    background-color: var(--color-blue-700);
  }
}
@media (hover: hover) {
  .hover\:bg-blue-800:hover {
    background-color: var(--color-blue-800);
  }
}
@media (hover: hover) {
  .hover\:bg-cyan-700:hover {
    background-color: var(--color-cyan-700);
  }
}
@media (hover: hover) {
  .hover\:bg-emerald-700:hover {
    background-color: var(--color-emerald-700);
  }
}
@media (hover: hover) {
  .hover\:bg-gray-50:hover {
    background-color: var(--color-gray-50);
  }
}
@media (hover: hover) {
  .hover\:bg-gray-200:hover {
    background-color: var(--color-gray-200);
  }
}
@media (hover: hover) {
  .hover\:bg-gray-700:hover {
    background-color: var(--color-gray-700);
  }
}
@media (hover: hover) {
  .hover\:bg-gray-900:hover {
    background-color: var(--color-gray-900);
  }
}
@media (hover: hover) {
  .hover\:bg-orange-700:hover {
    background-color: var(--color-orange-700);
  }
}
@media (hover: hover) {
  .hover\:bg-purple-700:hover {
    background-color: var(--color-purple-700);
  }
}
@media (hover: hover) {
  .hover\:bg-teal-700:hover {
    background-color: var(--color-teal-700);
  }
}
@media (hover: hover) {
  .hover\:bg-white\/80:hover {
    background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
  }
}
@media (hover: hover) {
  .hover\:text-amber-600:hover {
    color: var(--color-amber-600);
  }
}
@media (hover: hover) {
  .hover\:text-amber-700:hover {
    color: var(--color-amber-700);
  }
}
@media (hover: hover) {
  .hover\:text-amber-800:hover {
    color: var(--color-amber-800);
  }
}
@media (hover: hover) {
  .hover\:text-blue-600:hover {
    color: var(--color-blue-600);
  }
}
@media (hover: hover) {
  .hover\:text-blue-700:hover {
    color: var(--color-blue-700);
  }
}
@media (hover: hover) {
  .hover\:text-blue-800:hover {
    color: var(--color-blue-800);
  }
}
@media (hover: hover) {
  .hover\:text-cyan-700:hover {
    color: var(--color-cyan-700);
  }
}
@media (hover: hover) {
  .hover\:text-cyan-800:hover {
    color: var(--color-cyan-800);
  }
}
@media (hover: hover) {
  .hover\:text-cyan-900:hover {
    color: var(--color-cyan-900);
  }
}
@media (hover: hover) {
  .hover\:text-emerald-700:hover {
    color: var(--color-emerald-700);
  }
}
@media (hover: hover) {
  .hover\:text-emerald-800:hover {
    color: var(--color-emerald-800);
  }
}
@media (hover: hover) {
  .hover\:text-emerald-900:hover {
    color: var(--color-emerald-900);
  }
}
@media (hover: hover) {
  .hover\:text-gray-900:hover {
    color: var(--color-gray-900);
  }
}
@media (hover: hover) {
  .hover\:text-indigo-800:hover {
    color: var(--color-indigo-800);
  }
}
@media (hover: hover) {
  .hover\:text-orange-700:hover {
    color: var(--color-orange-700);
  }
}
@media (hover: hover) {
  .hover\:text-orange-800:hover {
    color: var(--color-orange-800);
  }
}
@media (hover: hover) {
  .hover\:text-purple-700:hover {
    color: var(--color-purple-700);
  }
}
@media (hover: hover) {
  .hover\:text-purple-800:hover {
    color: var(--color-purple-800);
  }
}
@media (hover: hover) {
  .hover\:text-rose-800:hover {
    color: var(--color-rose-800);
  }
}
@media (hover: hover) {
  .hover\:text-teal-700:hover {
    color: var(--color-teal-700);
  }
}
@media (hover: hover) {
  .hover\:text-teal-800:hover {
    color: var(--color-teal-800);
  }
}
@media (hover: hover) {
  .hover\:underline:hover {
    text-decoration-line: underline;
  }
}
@media (hover: hover) {
  .hover\:opacity-80:hover {
    opacity: 80%;
  }
}
@media (hover: hover) {
  .hover\:opacity-90:hover {
    opacity: 90%;
  }
}
@media (hover: hover) {
  .hover\:shadow-md:hover {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}
.focus\:z-10:focus {
  z-index: 10;
}
.focus\:z-20:focus {
  z-index: 20;
}
.focus\:border-amber-400:focus {
  border-color: var(--color-amber-400);
}
.focus\:border-amber-500:focus {
  border-color: var(--color-amber-500);
}
.focus\:border-blue-400:focus {
  border-color: var(--color-blue-400);
}
.focus\:border-white:focus {
  border-color: var(--color-white);
}
.focus\:ring:focus {
  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.focus\:ring-2:focus {
  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.focus\:ring-amber-100:focus {
  --tw-ring-color: var(--color-amber-100);
}
.focus\:ring-amber-200:focus {
  --tw-ring-color: var(--color-amber-200);
}
.focus\:ring-amber-500:focus {
  --tw-ring-color: var(--color-amber-500);
}
.focus\:ring-blue-100:focus {
  --tw-ring-color: var(--color-blue-100);
}
.focus\:ring-white\/30:focus {
  --tw-ring-color: color-mix(in oklab, var(--color-white) 30%, transparent);
}
.focus\:outline-offset-0:focus {
  outline-offset: 0px;
}
.focus\:outline-none:focus {
  --tw-outline-style: none;
  outline-style: none;
}
.focus-visible\:outline:focus-visible {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}
.focus-visible\:outline-2:focus-visible {
  outline-style: var(--tw-outline-style);
  outline-width: 2px;
}
.focus-visible\:outline-offset-2:focus-visible {
  outline-offset: 2px;
}
@media (width >= 40rem) {
  .sm\:h-\[400px\] {
    height: 400px;
  }
}
@media (width >= 40rem) {
  .sm\:h-\[500px\] {
    height: 500px;
  }
}
@media (width >= 40rem) {
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (width >= 40rem) {
  .sm\:flex-row {
    flex-direction: row;
  }
}
@media (width >= 40rem) {
  .sm\:items-center {
    align-items: center;
  }
}
@media (width >= 40rem) {
  .sm\:px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
}
@media (width >= 48rem) {
  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }
}
@media (width >= 48rem) {
  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }
}
@media (width >= 48rem) {
  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }
}
@media (width >= 48rem) {
  .md\:row-span-1 {
    grid-row: span 1 / span 1;
  }
}
@media (width >= 48rem) {
  .md\:row-span-2 {
    grid-row: span 2 / span 2;
  }
}
@media (width >= 48rem) {
  .md\:block {
    display: block;
  }
}
@media (width >= 48rem) {
  .md\:flex {
    display: flex;
  }
}
@media (width >= 48rem) {
  .md\:hidden {
    display: none;
  }
}
@media (width >= 48rem) {
  .md\:inline {
    display: inline;
  }
}
@media (width >= 48rem) {
  .md\:h-12 {
    height: calc(var(--spacing) * 12);
  }
}
@media (width >= 48rem) {
  .md\:h-60 {
    height: calc(var(--spacing) * 60);
  }
}
@media (width >= 48rem) {
  .md\:h-\[500px\] {
    height: 500px;
  }
}
@media (width >= 48rem) {
  .md\:h-\[600px\] {
    height: 600px;
  }
}
@media (width >= 48rem) {
  .md\:h-auto {
    height: auto;
  }
}
@media (width >= 48rem) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (width >= 48rem) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
@media (width >= 48rem) {
  .md\:gap-4 {
    gap: calc(var(--spacing) * 4);
  }
}
@media (width >= 48rem) {
  .md\:gap-8 {
    gap: calc(var(--spacing) * 8);
  }
}
@media (width >= 48rem) {
  .md\:p-6 {
    padding: calc(var(--spacing) * 6);
  }
}
@media (width >= 48rem) {
  .md\:text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
}
@media (width >= 48rem) {
  .md\:text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
}
@media (width >= 48rem) {
  .md\:text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
}
@media (width >= 48rem) {
  .md\:text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}
@media (width >= 64rem) {
  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }
}
@media (width >= 64rem) {
  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }
}
@media (width >= 64rem) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
@media (width >= 64rem) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
@media (width >= 64rem) {
  .lg\:px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
}
@media (width >= 64rem) {
  .lg\:text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
}
@media (width >= 64rem) {
  .lg\:text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
}
@media (width >= 80rem) {
  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
.prose-a\:text-primary :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  color: var(--color-primary);
}
.full-width {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}
.bg-gradient-superlight {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
}
@media print {
  .print-cols-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  .print-cols-3 {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}
.toc-scrollbar::-webkit-scrollbar {
  width: 6px;
}
.toc-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
.toc-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}
.toc-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #666;
}
.carousel-sidebar-fix {
  margin-top: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
.carousel-sidebar-fix .uw-banner:first-child, .carousel-sidebar-fix > div:first-child {
  margin-top: 0 !important;
}
.lg\:col-span-1 .uw-banner {
  margin-top: 0 !important;
  margin-bottom: 1.5rem !important;
}
.lg\:col-span-1 > div:first-child {
  margin-top: 0 !important;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
