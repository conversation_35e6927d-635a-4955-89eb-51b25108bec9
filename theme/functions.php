<?php
/**
 * _uw-theme functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package _uw-theme
 */

if ( ! defined( '_UW_THEME_VERSION' ) ) {
	/*
	 * Set the theme's version number.
	 *
	 * This is used primarily for cache busting. If you use `npm run bundle`
	 * to create your production build, the value below will be replaced in the
	 * generated zip file with a timestamp, converted to base 36.
	 * 
	 * To force browsers to reload CSS and JS after making changes, simply
	 * update this version number. For development, adding a timestamp
	 * (e.g., '1.0.1' to '1.0.2') is sufficient to invalidate browser caches.
	 */
	define( '_UW_THEME_VERSION', '1.1.1' );
}

if ( ! defined( '_UW_THEME_TYPOGRAPHY_CLASSES' ) ) {
	/*
	 * Set Tailwind Typography classes for the front end, block editor and
	 * classic editor using the constant below.
	 *
	 * For the front end, these classes are added by the `_uw_theme_content_class`
	 * function. You will see that function used everywhere an `entry-content`
	 * or `page-content` class has been added to a wrapper element.
	 *
	 * For the block editor, these classes are converted to a JavaScript array
	 * and then used by the `./javascript/block-editor.js` file, which adds
	 * them to the appropriate elements in the block editor (and adds them
	 * again when they’re removed.)
	 *
	 * For the classic editor (and anything using TinyMCE, like Advanced Custom
	 * Fields), these classes are added to TinyMCE’s body class when it
	 * initializes.
	 */
	define(
		'_UW_THEME_TYPOGRAPHY_CLASSES',
		'prose prose-neutral max-w-none prose-a:text-primary'
	);
}

if ( ! function_exists( '_uw_theme_setup' ) ) :
	/**
	 * Sets up theme defaults and registers support for various WordPress features.
	 *
	 * Note that this function is hooked into the after_setup_theme hook, which
	 * runs before the init hook. The init hook is too late for some features, such
	 * as indicating support for post thumbnails.
	 */
	function _uw_theme_setup() {
		/*
		 * Make theme available for translation.
		 * Translations can be filed in the /languages/ directory.
		 * If you're building a theme based on _uw-theme, use a find and replace
		 * to change '_uw-theme' to the name of your theme in all the template files.
		 */
		load_theme_textdomain( '_uw-theme', get_template_directory() . '/languages' );

		// Add default posts and comments RSS feed links to head.
		add_theme_support( 'automatic-feed-links' );

		/*
		 * Let WordPress manage the document title.
		 * By adding theme support, we declare that this theme does not use a
		 * hard-coded <title> tag in the document head, and expect WordPress to
		 * provide it for us.
		 */
		add_theme_support( 'title-tag' );

		/*
		 * Enable support for Post Thumbnails on posts and pages.
		 *
		 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		 */
		add_theme_support( 'post-thumbnails' );

		// This theme uses wp_nav_menu() in multiple locations.
		register_nav_menus(
			array(
				'menu-1' => __( 'Primary', '_uw-theme' ),
				'menu-2' => __( 'Footer Menu', '_uw-theme' ),
				'top-menu' => __( 'Top Menu', '_uw-theme' ),
			)
		);

		/*
		 * Switch default core markup for search form, comment form, and comments
		 * to output valid HTML5.
		 */
		add_theme_support(
			'html5',
			array(
				'search-form',
				'comment-form',
				'comment-list',
				'gallery',
				'caption',
				'style',
				'script',
			)
		);

		// Add theme support for selective refresh for widgets.
		add_theme_support( 'customize-selective-refresh-widgets' );

		// Add support for editor styles.
		add_theme_support( 'editor-styles' );

		// Enqueue editor styles.
		add_editor_style( 'style-editor.css' );
		add_editor_style( 'style-editor-extra.css' );

		// Add support for responsive embedded content.
		add_theme_support( 'responsive-embeds' );

		// Remove support for block templates.
		remove_theme_support( 'block-templates' );
	}
endif;
add_action( 'after_setup_theme', '_uw_theme_setup' );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function _uw_theme_widgets_init() {
	register_sidebar(
		array(
			'name'          => __( 'Footer', '_uw-theme' ),
			'id'            => 'sidebar-1',
			'description'   => __( 'Add widgets here to appear in your footer.', '_uw-theme' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action( 'widgets_init', '_uw_theme_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function _uw_theme_scripts() {
	wp_enqueue_style( '_uw-theme-style', get_stylesheet_uri(), array(), _UW_THEME_VERSION );
	wp_enqueue_style( '_uw-theme-menu-style', get_template_directory_uri() . '/css/menu.css', array(), _UW_THEME_VERSION );
	
	// Homepage specific fixes
	if (is_front_page()) {
		wp_enqueue_style( '_uw-theme-homepage-fixes', get_template_directory_uri() . '/css/homepage-fixes.css', array(), _UW_THEME_VERSION );
	}
	
	// Styles for single posts
	if (is_single()) {
		wp_enqueue_style( '_uw-theme-toc-style', get_template_directory_uri() . '/css/toc.css', array(), _UW_THEME_VERSION );
		wp_enqueue_style( '_uw-theme-article-fixes', get_template_directory_uri() . '/css/article-fixes.css', array(), _UW_THEME_VERSION );
		wp_enqueue_style( '_uw-theme-article-width-fix', get_template_directory_uri() . '/css/article-width-fix.css', array(), _UW_THEME_VERSION . '.' . time() );
		wp_enqueue_style( '_uw-theme-comments-style', get_template_directory_uri() . '/css/comments-style.css', array(), _UW_THEME_VERSION . '.' . time() );
		wp_enqueue_style( '_uw-theme-article-text-fix', get_template_directory_uri() . '/css/article-text-fix.css', array(), _UW_THEME_VERSION . '.' . time() );
		// Simple Lightbox for galleries
		wp_enqueue_style( '_uw-theme-simplelightbox', get_template_directory_uri() . '/css/simplelightbox.min.css', array(), _UW_THEME_VERSION );
	}
	wp_enqueue_script( '_uw-theme-script', get_template_directory_uri() . '/js/script.min.js', array(), _UW_THEME_VERSION, true );
	wp_enqueue_script( '_uw-theme-menu-script', get_template_directory_uri() . '/js/menu.js', array('jquery'), _UW_THEME_VERSION, true );
	
	// Enhanced TOC script for better article navigation
	if (is_single()) {
		wp_enqueue_script( '_uw-theme-toc-enhancer', get_template_directory_uri() . '/js/toc-enhancer.js', array('jquery'), _UW_THEME_VERSION, true );
		// Simple Lightbox for gallery
		wp_enqueue_script( '_uw-theme-simplelightbox', get_template_directory_uri() . '/js/simplelightbox.min.js', array('jquery'), _UW_THEME_VERSION, true );
	}

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}
}
add_action( 'wp_enqueue_scripts', '_uw_theme_scripts' );

/**
 * Enqueue the block editor script.
 */
function _uw_theme_enqueue_block_editor_script() {
	if ( is_admin() ) {
		wp_enqueue_script(
			'_uw-theme-editor',
			get_template_directory_uri() . '/js/block-editor.min.js',
			array(
				'wp-blocks',
				'wp-edit-post',
			),
			_UW_THEME_VERSION,
			true
		);
		wp_add_inline_script( '_uw-theme-editor', "tailwindTypographyClasses = '" . esc_attr( _UW_THEME_TYPOGRAPHY_CLASSES ) . "'.split(' ');", 'before' );
	}
}
add_action( 'enqueue_block_assets', '_uw_theme_enqueue_block_editor_script' );

/**
 * Add the Tailwind Typography classes to TinyMCE.
 *
 * @param array $settings TinyMCE settings.
 * @return array
 */
function _uw_theme_tinymce_add_class( $settings ) {
	$settings['body_class'] = _UW_THEME_TYPOGRAPHY_CLASSES;
	return $settings;
}
add_filter( 'tiny_mce_before_init', '_uw_theme_tinymce_add_class' );

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Custom template functions for advanced features.
 */
require get_template_directory() . '/inc/custom-template-functions.php';

/**
 * Custom Walker classes for navigation.
 */
require get_template_directory() . '/inc/class-dumabyt-walker-nav-menu.php';
require get_template_directory() . '/inc/class-dumabyt-footer-walker-nav-menu.php';

/**
 * Breadcrumbs functionality.
 */
require get_template_directory() . '/inc/breadcrumbs.php';

/**
 * Partner RSS feeds functionality.
 */
require get_template_directory() . '/inc/partner-feeds.php';

/**
 * Admin toolbar customizations.
 */
require get_template_directory() . '/inc/admin-toolbar.php';

/**
 * Image handling filters.
 */
require get_template_directory() . '/inc/image-filters.php';

/**
 * Partneři webu - správa a zobrazení
 */
require get_template_directory() . '/inc/web-partners.php';

/**
 * Magazine Widget - zobrazení aktuálního čísla časopisu Můj dům
 */
require get_template_directory() . '/inc/class-dumabyt-magazine-widget.php';

/**
 * UmimeWeby modul pro pokročilou správu bannerů
 */
require_once get_template_directory() . '/umimeweby/class-umimeweby-core.php';

// Přidání diagnostických zpráv
function dumabyt_add_debug_notice($message, $type = 'info') {
    add_action('admin_notices', function() use ($message, $type) {
        ?>
        <div class="notice notice-<?php echo esc_attr($type); ?> is-dismissible">
            <p><?php echo esc_html($message); ?></p>
        </div>
        <?php
    });
}

// Inicializace modulu UmimeWeby
function dumabyt_init_umimeweby() {
    // Inicializace modulu
    UmimewebyCore::get_instance();
}
// Změna hooku pro co nejdřívější inicializaci
add_action('after_setup_theme', 'dumabyt_init_umimeweby');

// Načtení admin assets pro bannery
function dumabyt_load_banner_admin_assets() {
    $screen = get_current_screen();
    
    if ($screen && 'uw_banner' === $screen->post_type) {
        // Načtení stylů
        wp_enqueue_style(
            'uw-banner-admin-direct-styles',
            get_template_directory_uri() . '/umimeweby/assets/css/admin-enhanced.css',
            array(),
            _UW_THEME_VERSION
        );
        
        // Načtení JavaScriptu pro validaci, atd.
        wp_enqueue_script(
            'uw-banner-admin-direct-script',
            get_template_directory_uri() . '/umimeweby/assets/js/admin-direct.js',
            array('jquery'),
            _UW_THEME_VERSION,
            true
        );
    }
}
add_action('admin_enqueue_scripts', 'dumabyt_load_banner_admin_assets');

// Poznámka: Funkce pro metaboxy bannerů byly přesunuty do tříd UmimeWeby
// Nyní jsou spravovány centrálně přes BannerConfig, BannerMetabox a BannerNativeMetabox

/**
 * Funkce pro získání bannerů (kompatibilita se starým systémem partnerů) 
 */

/**
 * Odstranění třídy 'prose' z obsahu článku a nahrazení vlastní třídou, centrování obrázků
 */
add_filter('the_content', 'dumabyt_remove_prose_class', 999);
function dumabyt_remove_prose_class($content) {
    if (is_single()) {
        // Přidáváme inline styl přímo do obsahu pro jistotu
        $content = '<div style="max-width: 100% !important; width: 100% !important;">' . $content . '</div>';
        
        // Upravíme obrázky v obsahu, aby byly centrované
        $content = preg_replace(
            '/<img(.*?)>/',
            '<img$1 style="display: block; margin-left: auto; margin-right: auto; max-width: 800px;">',
            $content
        );
        
        // Upravíme figcaptions, aby byly centrované
        $content = preg_replace(
            '/<figcaption(.*?)>/',
            '<figcaption$1 style="text-align: center;">',
            $content
        );
    }
    return $content;
}

/**
 * Funkce pro získání bannerů (kompatibilita se starým systémem partnerů)
 */
function dumabyt_get_partners($count = 3, $offset = 0) {
    // Využívání nového systému bannerů místo původního
    $args = array(
        'post_type' => 'uw_banner',
        'posts_per_page' => absint($count),
        'offset' => absint($offset),
        'meta_query' => array(
            array(
                'key' => 'banner_type',
                'value' => 'square',
                'compare' => '='
            )
        ),
        'orderby' => 'rand'
    );
    
    return new WP_Query($args);
}
