<?php
/**
 * The template for displaying comments
 *
 * This is the template that displays the area of the page that contains both
 * the current comments and the comment form.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package dumabyt
 */

/*
 * If the current post is protected by a password and the visitor has not yet
 * entered the password we will return early without loading the comments.
 */
if (post_password_required()) {
    return;
}

// Get category color for styling
$category_color = 'text-amber-600';
$category_bg = 'bg-amber-100';
$category_border = 'border-amber-200';
$category_button = 'bg-amber-600 hover:bg-amber-700';

// Get main category
$categories = get_the_category();
if ($categories) {
    $category = $categories[0]; // Get the first category

    // Set color based on category slug - matching our category scheme
    if ($category->slug === 'interier') {
        $category_color = 'text-cyan-600';
        $category_bg = 'bg-cyan-50';
        $category_border = 'border-cyan-200';
        $category_button = 'bg-cyan-600 hover:bg-cyan-700';
    } elseif ($category->slug === 'stavba') {
        $category_color = 'text-emerald-600';
        $category_bg = 'bg-emerald-50';
        $category_border = 'border-emerald-200';
        $category_button = 'bg-emerald-600 hover:bg-emerald-700';
    } elseif ($category->slug === 'rekonstrukce') {
        $category_color = 'text-orange-600';
        $category_bg = 'bg-orange-50';
        $category_border = 'border-orange-200';
        $category_button = 'bg-orange-600 hover:bg-orange-700';
    } elseif ($category->slug === 'zahrada') {
        $category_color = 'text-teal-600';
        $category_bg = 'bg-teal-50';
        $category_border = 'border-teal-200';
        $category_button = 'bg-teal-600 hover:bg-teal-700';
    } elseif ($category->slug === 'blog') {
        $category_color = 'text-purple-600';
        $category_bg = 'bg-purple-50';
        $category_border = 'border-purple-200';
        $category_button = 'bg-purple-600 hover:bg-purple-700';
    }
}
?>

<div id="comments" class="border-t border-gray-100 mt-6 pt-6">
    <?php
    if (have_comments()) :
        $_uw_theme_comment_count = get_comments_number();
    ?>
        <h2 class="text-xl font-bold mb-6 font-trajan flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 <?php echo $category_color; ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            <?php
            if ('1' === $_uw_theme_comment_count) {
                // phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped
                printf(
                    /* translators: 1: title. */
                    esc_html__('Jeden komentář k článku', '_uw-theme')
                );
                // phpcs:enable WordPress.Security.EscapeOutput.OutputNotEscaped
            } else {
                // phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped
                printf(
                    /* translators: 1: comment count number, 2: title. */
                    esc_html(_nx('%1$s komentářů k článku', '%1$s komentářů k článku', $_uw_theme_comment_count, 'comments title', '_uw-theme')),
                    number_format_i18n($_uw_theme_comment_count)
                );
                // phpcs:enable WordPress.Security.EscapeOutput.OutputNotEscaped
            }
            ?>
        </h2>

        <?php the_comments_navigation(); ?>

        <div class="space-y-4 mb-8">
            <?php
            wp_list_comments(
                array(
                    'style'      => 'div',
                    'callback'   => 'dumabyt_comment_callback',
                    'short_ping' => true,
                )
            );
            ?>
        </div>

        <?php
        the_comments_navigation();

        // If there are existing comments, but comments are closed, display a message.
        if (!comments_open()) :
        ?>
            <p class="text-gray-500 italic text-sm mt-4"><?php esc_html_e('Komentáře jsou uzavřeny.', '_uw-theme'); ?></p>
        <?php
        endif;

    endif;

    // Custom comment form styling
    $commenter = wp_get_current_commenter();
    $req = get_option('require_name_email');
    $aria_req = ($req ? " aria-required='true'" : '');

    $fields = array(
        'author' => '<div class="comment-form-author mb-4"><label for="author" class="block text-sm font-medium text-gray-700 mb-1">' . __('Jméno', '_uw-theme') . ($req ? ' <span class="text-red-500">*</span>' : '') . '</label><input id="author" name="author" type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-50" value="' . esc_attr($commenter['comment_author']) . '"' . $aria_req . ' /></div>',
        'email'  => '<div class="comment-form-email mb-4"><label for="email" class="block text-sm font-medium text-gray-700 mb-1">' . __('Email', '_uw-theme') . ($req ? ' <span class="text-red-500">*</span>' : '') . '</label><input id="email" name="email" type="email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-50" value="' . esc_attr($commenter['comment_author_email']) . '"' . $aria_req . ' /></div>',
    );

    $comment_field = '<div class="comment-form-comment mb-4"><label for="comment" class="block text-sm font-medium text-gray-700 mb-1">' . __('Komentář', '_uw-theme') . ' <span class="text-red-500">*</span></label><textarea id="comment" name="comment" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-50" rows="5" aria-required="true"></textarea></div>';

    $comment_form_args = array(
        'fields'               => $fields,
        'comment_field'        => $comment_field,
        'comment_notes_before' => '<p class="comment-notes text-sm text-gray-600 mb-4">' . __('Váš email nebude zveřejněn.', '_uw-theme') . '</p>',
        'comment_notes_after'  => '',
        'id_form'              => 'commentform',
        'id_submit'            => 'submit',
        'class_form'           => 'comment-form',
        'class_submit'         => $category_button . ' text-white font-medium py-2 px-4 rounded-md transition-colors',
        'title_reply'          => __('Zanechat komentář', '_uw-theme'),
        'title_reply_to'       => __('Odpovědět %s', '_uw-theme'),
        'title_reply_before'   => '<h3 id="reply-title" class="comment-reply-title text-xl font-bold mb-4 font-trajan flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 ' . $category_color . '" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" /></svg>',
        'title_reply_after'    => '</h3>',
        'cancel_reply_before'  => '<span class="cancel-reply text-sm ml-2">',
        'cancel_reply_after'   => '</span>',
        'cancel_reply_link'    => __('Zrušit odpověď', '_uw-theme'),
        'label_submit'         => __('Odeslat komentář', '_uw-theme'),
        'submit_button'        => '<button name="%1$s" type="submit" id="%2$s" class="%3$s" value="%4$s">%4$s</button>',
        'submit_field'         => '<div class="form-submit">%1$s %2$s</div>',
    );

    comment_form($comment_form_args);
    ?>

</div><!-- #comments -->

<?php
// Add custom comment callback function if it doesn't exist
if (!function_exists('dumabyt_comment_callback')) :
    function dumabyt_comment_callback($comment, $args, $depth) {
        $GLOBALS['comment'] = $comment;

        // Get category color for styling
        $category_color = 'text-amber-600';
        $category_bg = 'bg-amber-50';
        $category_border = 'border-amber-200';

        // Get post categories
        $categories = get_the_category($comment->comment_post_ID);
        if ($categories) {
            $category = $categories[0]; // Get the first category

            // Set color based on category slug - matching our category scheme
            if ($category->slug === 'interier') {
                $category_color = 'text-cyan-600';
                $category_bg = 'bg-cyan-50';
                $category_border = 'border-cyan-200';
            } elseif ($category->slug === 'stavba') {
                $category_color = 'text-emerald-600';
                $category_bg = 'bg-emerald-50';
                $category_border = 'border-emerald-200';
            } elseif ($category->slug === 'rekonstrukce') {
                $category_color = 'text-orange-600';
                $category_bg = 'bg-orange-50';
                $category_border = 'border-orange-200';
            } elseif ($category->slug === 'zahrada') {
                $category_color = 'text-teal-600';
                $category_bg = 'bg-teal-50';
                $category_border = 'border-teal-200';
            } elseif ($category->slug === 'blog') {
                $category_color = 'text-purple-600';
                $category_bg = 'bg-purple-50';
                $category_border = 'border-purple-200';
            }
        }
        ?>
        <div id="comment-<?php comment_ID(); ?>" class="comment <?php echo $category_bg; ?> p-4 rounded-lg border <?php echo $category_border; ?> <?php echo empty($args['has_children']) ? '' : 'parent'; ?>">
            <div class="comment-body">
                <div class="comment-meta flex items-start mb-3">
                    <div class="comment-author vcard mr-3">
                        <?php echo get_avatar($comment, 40, '', '', array('class' => 'rounded-full')); ?>
                    </div>
                    <div>
                        <div class="comment-author-name font-medium <?php echo $category_color; ?>">
                            <?php echo get_comment_author_link(); ?>
                        </div>
                        <div class="comment-metadata text-xs text-gray-500">
                            <time datetime="<?php echo get_comment_time('c'); ?>">
                                <?php
                                printf(
                                    /* translators: 1: Comment date, 2: Comment time. */
                                    __('%1$s v %2$s', '_uw-theme'),
                                    get_comment_date(''),
                                    get_comment_time()
                                );
                                ?>
                            </time>
                            <?php edit_comment_link(__('Upravit', '_uw-theme'), ' <span class="edit-link">', '</span>'); ?>
                        </div>
                    </div>
                </div>

                <?php if ('0' == $comment->comment_approved) : ?>
                    <p class="comment-awaiting-moderation text-sm italic text-amber-600 mb-2"><?php _e('Váš komentář čeká na schválení.', '_uw-theme'); ?></p>
                <?php endif; ?>

                <div class="comment-content text-gray-700">
                    <?php comment_text(); ?>
                </div>

                <div class="reply mt-2 text-sm">
                    <?php
                    comment_reply_link(
                        array_merge(
                            $args,
                            array(
                                'depth'     => $depth,
                                'max_depth' => $args['max_depth'],
                                'before'    => '<span class="' . $category_color . ' hover:underline">',
                                'after'     => '</span>'
                            )
                        )
                    );
                    ?>
                </div>
            </div>
        </div>
        <?php
    }
endif;
