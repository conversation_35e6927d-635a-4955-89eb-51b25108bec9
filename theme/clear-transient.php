<?php
/**
 * Directly delete WordPress transient from the database
 */

// Load WordPress core
require_once(__DIR__ . '/../../../wp-load.php');

// Get the database prefix
global $wpdb;

// Delete the transient directly from the database
$transient_name = 'dumabyt_partner_articles';
$option_name = '_transient_' . $transient_name;
$timeout_name = '_transient_timeout_' . $transient_name;

// Delete the transient value
$delete_value = $wpdb->query(
    $wpdb->prepare(
        "DELETE FROM $wpdb->options WHERE option_name = %s OR option_name = %s",
        $option_name,
        $timeout_name
    )
);

// Clear object cache if available
if (function_exists('wp_cache_delete')) {
    wp_cache_delete($transient_name, 'transient');
}

// Output result
if ($delete_value) {
    echo "Transient 'dumabyt_partner_articles' successfully deleted from the database.";
} else {
    echo "No transients found or error deleting transient. It may have already been deleted.";
}

// Link back to debug page
echo '<p><a href="rss-debug-cihel.html">Back to RSS Debug Page</a></p>';
