<?php
/**
 * Template part for displaying homepage sidebar
 *
 * @package dumabyt
 */
?>

<div class="lg:col-span-1 space-y-6 sidebar-fixed-width">
    <?php 
    // Display sidebar banners - this is the main location for square banners (300x300)
    // This action was previously called in both front-page.php and sidebar-homepage.php causing duplicates
    do_action('homepage_sidebar_banners', 5);
    ?>
    
    <!-- Newsletter signup -->
    <div class="bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg card-elevation overflow-hidden p-4">
        <h3 class="text-lg font-bold text-center text-amber-900 mb-3 font-trajan">Odebírejte newsletter</h3>
        <p class="text-sm text-amber-800 mb-3 text-center">Nechte si posílat novinky a inspiraci ze světa bydlení</p>
        <form class="flex flex-col gap-2">
            <input type="email" placeholder="Váš e-mail" class="w-full px-3 py-2 rounded-lg border-amber-200 focus:border-amber-400 focus:ring-2 focus:ring-amber-200 focus:outline-none transition-colors" required>
            <button type="submit" class="bg-amber-600 hover:bg-amber-700 text-white font-medium py-2 px-4 rounded-lg shadow-sm transition-colors w-full">
                Přihlásit se
            </button>
        </form>
    </div>
    
    <!-- Newest Articles Section -->
    <div class="bg-white rounded-lg card-elevation overflow-hidden">
        <div class="border-b border-gray-200">
            <h3 class="text-lg font-bold uppercase py-3 px-4 text-center text-gray-800 font-trajan">Nejnovější články</h3>
        </div>
        <div class="p-4">
            <?php
            // Get the 5 newest articles
            $newest_articles = new WP_Query(array(
                'posts_per_page' => 5,
                'orderby' => 'date',
                'order' => 'DESC'
            ));
            
            if ($newest_articles->have_posts()) :
                while ($newest_articles->have_posts()) : $newest_articles->the_post();
            ?>
                <div class="mb-3 pb-3 border-b border-gray-100 last:border-0 last:pb-0 last:mb-0 group">
                    <a href="<?php the_permalink(); ?>" class="flex items-start gap-3">
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="flex-shrink-0 w-20 h-20 overflow-hidden rounded shadow-sm">
                                <div class="w-full h-full overflow-hidden">
                                    <?php the_post_thumbnail('thumbnail', array('class' => 'w-full h-full object-cover transition-transform duration-500 group-hover:scale-110')); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div>
                            <h4 class="text-sm font-medium text-gray-800 group-hover:text-amber-600 transition-colors"><?php the_title(); ?></h4>
                            <span class="text-xs text-gray-500 mt-1 block"><?php echo get_the_date(); ?></span>
                        </div>
                    </a>
                </div>
            <?php
                endwhile;
                wp_reset_postdata();
            else:
                // If no articles are found
                echo '<div class="p-4 bg-gray-50 rounded-lg text-sm text-center text-gray-500">Zatím tu nejsou žádné články.</div>';
            endif;
            ?>
        </div>
    </div>
</div>

<style>
.card-elevation {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elevation:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>
