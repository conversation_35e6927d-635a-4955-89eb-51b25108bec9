<?php
/**
 * Template part for displaying the footer content
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package _uw-theme
 */

?>

<footer id="colophon" class="bg-gray-50 pt-8 pb-0">
    <!-- Horizontal divider -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div class="h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
    </div>

    <!-- ČLÁNKY NA PARTNERSKÝCH WEBECH -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <h2 class="uppercase text-gray-700 font-semibold text-lg mb-6 font-trajan text-center">ČLÁNKY NA PARTNERSKÝCH WEBECH</h2>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-6 justify-items-center place-content-center mx-auto max-w-6xl">
            <?php 
            // Get latest articles from partner websites via RSS feeds
            $partner_articles = dumabyt_get_partner_articles();
            
            // Display each partner article
            foreach ($partner_articles as $key => $article) :
            ?>
            <!-- Partner Article: <?php echo esc_html($article['partner_name']); ?> -->
            <div class="group">
                <a href="<?php echo esc_url($article['permalink']); ?>" class="block card-elevation bg-white rounded-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-1 hover:shadow-md" target="_blank" rel="noopener">
                    <div class="p-4">
                        <h3 class="uppercase <?php echo esc_attr($article['color_class']); ?> font-medium text-sm mb-3"><?php echo esc_html($article['partner_name']); ?></h3>
                        <div class="aspect-video overflow-hidden rounded mb-3">
                            <?php 
                            $fallback_image = isset($article['fallback_image']) ? $article['fallback_image'] : 'mujdum.svg';
                            $fallback_url = esc_url(get_template_directory_uri()) . '/images/partner-articles/' . esc_attr($fallback_image);
                            
                            if (!empty($article['image_url'])) : 
                            ?>
                                <img src="<?php echo esc_url($article['image_url']); ?>" 
                                     alt="<?php echo esc_attr($article['partner_name']); ?>" 
                                     class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                                     onerror="this.onerror=null; this.src='<?php echo $fallback_url; ?>';">
                            <?php else : ?>
                                <img src="<?php echo $fallback_url; ?>" 
                                     alt="<?php echo esc_attr($article['partner_name']); ?>" 
                                     class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105">
                            <?php endif; ?>
                        </div>
                        <h4 class="text-gray-700 text-sm font-medium <?php echo esc_attr($article['hover_class']); ?> transition-colors"><?php echo esc_html($article['title']); ?></h4>
                    </div>
                </a>
            </div>
            <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Horizontal divider -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div class="h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
    </div>

    <!-- PARTNEŘI WEBU -->
    <?php
    // Získání partnerů z nového systému
    $partners_instance = Dumabyt_Web_Partners::get_instance();
    $partners = $partners_instance->get_partners();
    ?>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <h2 class="uppercase text-gray-700 font-semibold text-lg mb-6 font-trajan text-center">PARTNEŘI WEBU</h2>
        
        <?php if (!empty($partners)) : ?>
        <div class="flex flex-wrap justify-center items-center gap-x-8 gap-y-6">
            <?php foreach ($partners as $partner) : 
                $url = get_post_meta($partner->ID, 'partner_url', true);
                if (empty($url)) {
                    $url = '#';
                }
            ?>
            <a href="<?php echo esc_url($url); ?>" class="transform transition-all duration-300 hover:-translate-y-1 hover:opacity-80" target="_blank" rel="noopener">
                <?php if (has_post_thumbnail($partner->ID)) : ?>
                    <?php echo get_the_post_thumbnail($partner->ID, 'full', array('class' => 'h-10 md:h-12', 'alt' => esc_attr(get_the_title($partner->ID)))); ?>
                <?php else : ?>
                    <span class="text-gray-400"><?php echo esc_html(get_the_title($partner->ID)); ?></span>
                <?php endif; ?>
            </a>
            <?php endforeach; ?>
        </div>
        <?php else : ?>
        <!-- Žádní partneři nenalezeni - zobrazíme placeholdery -->
        <div class="flex flex-wrap justify-center items-center gap-x-8 gap-y-6">
            <?php for ($i = 0; $i < 5; $i++) : ?>
                <div class="h-10 md:h-12 bg-gray-100 rounded px-4 flex items-center justify-center">
                    <span class="text-gray-400 text-xs">Místo pro partnera</span>
                </div>
            <?php endfor; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- Horizontal divider -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div class="h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
    </div>

    <!-- Main navigation menu -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <nav aria-label="<?php esc_attr_e( 'Footer Main Menu', '_uw-theme' ); ?>">
            <?php
            // Use the same menu location as the main header menu (menu-1)
            wp_nav_menu(
                array(
                    'theme_location' => 'menu-1',
                    'menu_class'     => 'flex flex-wrap justify-center gap-4 md:gap-8',
                    'container'      => false,
                    'fallback_cb'    => false,
                    'depth'          => 1,
                    'walker'         => new Dumabyt_Footer_Walker_Nav_Menu(),
                )
            );
            ?>
        </nav>
    </div>

    <!-- Secondary menu and copyright -->
    <div class="bg-gray-100 py-6">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Secondary menu -->
            <div class="flex flex-wrap justify-center gap-2 md:gap-4 mb-4">
                <a href="/profil" class="secondary-link">Profil</a>
                <span class="hidden md:inline text-gray-400">|</span>
                <a href="/kontakt" class="secondary-link">Kontakt</a>
                <span class="hidden md:inline text-gray-400">|</span>
                <a href="/pro-inzerenty" class="secondary-link">Pro inzerenty</a>
                <span class="hidden md:inline text-gray-400">|</span>
                <a href="/gprd" class="secondary-link">GDPR</a>
            </div>

            <!-- Copyright -->
            <div class="text-center text-gray-600 text-sm mb-2">
                Copyright © BUSINESS MEDIA ONE, s. r. o. 2006–<?php echo date('Y'); ?>
            </div>
            
        </div>
    </div>

    <?php if ( is_active_sidebar( 'sidebar-1' ) ) : ?>
        <aside role="complementary" aria-label="<?php esc_attr_e( 'Footer', '_uw-theme' ); ?>" class="hidden">
            <?php dynamic_sidebar( 'sidebar-1' ); ?>
        </aside>
    <?php endif; ?>

    <?php if ( has_nav_menu( 'menu-2' ) ) : ?>
        <nav aria-label="<?php esc_attr_e( 'Footer Menu', '_uw-theme' ); ?>" class="hidden">
            <?php
            wp_nav_menu(
                array(
                    'theme_location' => 'menu-2',
                    'menu_class'     => 'footer-menu',
                    'depth'          => 1,
                )
            );
            ?>
        </nav>
    <?php endif; ?>
</footer><!-- #colophon -->

<style>
/* Card elevation effect */
.card-elevation {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elevation:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Category link styles */
.category-link {
    display: inline-block;
    padding: 0.5rem 1rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #6b7280; /* text-gray-500 */
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    text-decoration: none;
}

.category-link:hover {
    background-color: #f3f4f6; /* bg-gray-100 */
    color: #1f2937; /* text-gray-800 */
}

.category-link.active {
    background-color: #f3f4f6; /* bg-gray-100 */
    color: #1f2937; /* text-gray-800 */
}

/* Category specific hover colors */
.category-dum:hover {
    color: #b45309; /* amber-700 */
}

.category-interier:hover {
    color: #0891b2; /* cyan-600 */
}

.category-stavba:hover {
    color: #059669; /* emerald-600 */
}

.category-rekonstrukce:hover,
.category-rekonstrukce.active {
    color: #ea580c; /* orange-600 */
}

.category-zahrada:hover {
    color: #0d9488; /* teal-600 */
}

.category-blog:hover {
    color: #9333ea; /* purple-600 */
}

/* Secondary links */
.secondary-link {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    color: #6b7280; /* text-gray-500 */
    text-decoration: none;
    transition: all 0.2s ease;
}

.secondary-link:hover {
    color: #1f2937; /* text-gray-800 */
    background-color: #e5e7eb; /* bg-gray-200 */
    border-radius: 0.25rem;
}

@media (max-width: 768px) {
    .category-link {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
}
</style>
