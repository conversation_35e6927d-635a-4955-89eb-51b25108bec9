<?php
/**
 * Template part for displaying partners sidebar
 *
 * @package _uw-theme
 */
?>

<div class="lg:col-span-1 space-y-6">
    <?php 
    // Podle kontextu voláme různé akce - jiné bannery pro homepage a jin<PERSON> pro <PERSON>
    if (is_front_page()) {
        // Na homepage zobrazíme homepage bannery
        do_action('homepage_sidebar_banners', 5);
    } else {
        // V článcích zobrazíme article bannery
        do_action('article_sidebar_banners', 3);
    }
    ?>
    
    <!-- Related Articles Section -->
    <div class="bg-white rounded-lg card-elevation overflow-hidden">
        <div class="border-b border-gray-200">
            <h3 class="text-lg font-bold uppercase py-3 px-4 text-center text-gray-800 font-trajan">Podobn<PERSON></h3>
        </div>
        <div class="p-4">
            <?php
            // Get related posts based on tags or categories
            $tags = wp_get_post_tags(get_the_ID());
            $categories = get_the_category();
            
            if ($tags || $categories) {
                $tag_ids = array();
                if ($tags) {
                    foreach($tags as $tag) $tag_ids[] = $tag->term_id;
                }
                
                $category_ids = array();
                if ($categories) {
                    foreach($categories as $category) $category_ids[] = $category->term_id;
                }
                
                $related_query = new WP_Query(array(
                    'tag__in' => $tag_ids,
                    'category__in' => $category_ids,
                    'post__not_in' => array(get_the_ID()),
                    'posts_per_page' => 4,
                    'orderby' => 'rand'
                ));
                
                if ($related_query->have_posts()) :
                    while ($related_query->have_posts()) : $related_query->the_post();
            ?>
                <div class="mb-3 pb-3 border-b border-gray-100 last:border-0 last:pb-0 last:mb-0 group">
                    <a href="<?php the_permalink(); ?>" class="flex items-start gap-3">
                        <?php if (has_post_thumbnail()) : ?>
                        <div class="flex-shrink-0 w-20 h-20 overflow-hidden rounded shadow-sm">
                            <div class="w-full h-full overflow-hidden">
                                <?php the_post_thumbnail('thumbnail', array('class' => 'w-full h-full object-cover transition-transform duration-500 group-hover:scale-110')); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div>
                            <h4 class="text-sm font-medium text-gray-800 group-hover:text-amber-600 transition-colors"><?php the_title(); ?></h4>
                            <span class="text-xs text-gray-500 mt-1 block"><?php echo get_the_date(); ?></span>
                        </div>
                    </a>
                </div>
            <?php
                    endwhile;
                    wp_reset_postdata();
                else:
                    // If no related posts are found
                    echo '<div class="p-4 bg-gray-50 rounded-lg text-sm text-center text-gray-500">Zatím tu nejsou žádné podobné články.</div>';
                endif;
            } else {
                // If current post has no tags or categories
                echo '<div class="p-4 bg-gray-50 rounded-lg text-sm text-center text-gray-500">Zatím tu nejsou žádné podobné články.</div>';
            }
            ?>
        </div>
    </div>
    
    <!-- Newsletter signup - zobrazíme pouze pokud nejsme na homepage -->
    <?php if (!is_front_page()): ?>
    <div class="bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg card-elevation overflow-hidden p-4">
        <h3 class="text-lg font-bold text-center text-amber-900 mb-3 font-trajan">Odebírejte newsletter</h3>
        <p class="text-sm text-amber-800 mb-3 text-center">Nechte si posílat novinky a inspiraci ze světa bydlení</p>
        <form class="flex flex-col gap-2">
            <input type="email" placeholder="Váš e-mail" class="w-full px-3 py-2 rounded-lg border-amber-200 focus:border-amber-400 focus:ring-2 focus:ring-amber-200 focus:outline-none transition-colors" required>
            <button type="submit" class="bg-amber-600 hover:bg-amber-700 text-white font-medium py-2 px-4 rounded-lg shadow-sm transition-colors w-full">
                Přihlásit se
            </button>
        </form>
    </div>
    <?php endif; ?>
</div>

<style>
.card-elevation {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elevation:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}


</style>
