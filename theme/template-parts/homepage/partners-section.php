<?php
/**
 * Template part for displaying the partners section on homepage
 *
 * @package dumabyt
 */

// Získání instance tř<PERSON><PERSON> pro partnery webu
$partners_instance = Dumabyt_Web_Partners::get_instance();
$partners = $partners_instance->get_partners();
?>

<div class="container mx-auto px-4 sm:px-6 lg:px-8 mb-8">
    <h2 class="uppercase text-gray-700 font-semibold text-lg mb-6 font-trajan text-center">PARTNEŘI WEBU</h2>
    
    <?php if (!empty($partners)) : ?>
    <div class="flex flex-wrap justify-center items-center gap-x-8 gap-y-6">
        <?php foreach ($partners as $partner) : 
            $url = get_post_meta($partner->ID, 'partner_url', true);
            if (empty($url)) {
                $url = '#';
            }
        ?>
        <a href="<?php echo esc_url($url); ?>" class="transform transition-all duration-300 hover:-translate-y-1 hover:opacity-80" target="_blank" rel="noopener" onclick="window.open(this.href, '_blank'); return false;">
            <?php if (has_post_thumbnail($partner->ID)) : ?>
                <?php echo get_the_post_thumbnail($partner->ID, 'full', array('class' => 'h-10 md:h-12', 'alt' => esc_attr(get_the_title($partner->ID)))); ?>
            <?php else : ?>
                <span class="text-gray-400"><?php echo esc_html(get_the_title($partner->ID)); ?></span>
            <?php endif; ?>
        </a>
        <?php endforeach; ?>
    </div>
    <?php else : ?>
    <!-- Žádní partneři nenalezeni - zobrazíme placeholders -->
    <div class="flex flex-wrap justify-center items-center gap-x-8 gap-y-6">
        <?php for ($i = 0; $i < 5; $i++) : ?>
            <div class="h-10 md:h-12 bg-gray-100 rounded px-4 flex items-center justify-center">
                <span class="text-gray-400 text-xs">Místo pro partnera</span>
            </div>
        <?php endfor; ?>
    </div>
    <?php endif; ?>

    <div class="mt-4 text-center">
        <a href="#" class="text-xs text-gray-500 hover:underline">Informace o reklamě</a>
    </div>
</div>
