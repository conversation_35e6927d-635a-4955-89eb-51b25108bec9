<?php
/**
 * Template part for displaying the DOPORUČUJEME section on homepage
 *
 * @package dumabyt
 */

$category_slug = 'doporucujeme';
$category_name = 'DOPORUČUJEME';
$category_url = esc_url(get_category_link(get_cat_ID($category_slug)));
$color_class = 'bg-rose-600'; // Default color if function doesn't exist

// Use the color function if it exists
if (function_exists('dumabyt_get_category_color')) {
    $color_class = dumabyt_get_category_color($category_slug);
}

// Get the featured article
$featured_article = dumabyt_get_category_articles($category_slug, 1);
// Get additional articles
$more_articles = dumabyt_get_category_articles($category_slug, 3, 1, false);
?>

<!-- DOPORUČUJEME - Modern card with rose theme -->
<div class="md:col-span-3 relative group rounded-xl overflow-hidden card-elevation bg-white">
    <a href="<?php echo esc_url($category_url); ?>" class="absolute top-4 left-4 z-10">
        <span class="<?php echo esc_attr($color_class); ?> text-white font-bold uppercase py-1.5 px-4 rounded-full text-xs tracking-wider badge-shadow"><?php echo esc_html($category_name); ?></span>
    </a>

    <?php
    if ($featured_article->have_posts()) :
        $featured_article->the_post();
    ?>
        <a href="<?php the_permalink(); ?>" class="block p-4 pt-14">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                <div class="md:col-span-1 rounded-lg overflow-hidden card-shadow">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="relative h-44 overflow-hidden">
                            <?php the_post_thumbnail('medium', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-500')); ?>
                        </div>
                    <?php else: ?>
                        <div class="h-44 bg-rose-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-rose-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                            </svg>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="md:col-span-2">
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-rose-700 transition-colors mb-3"><?php the_title(); ?></h3>
                    <p class="text-gray-600 line-clamp-3"><?php echo wp_trim_words(get_the_excerpt(), 30); ?></p>

                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                </svg>
                                Doporučený produkt
                            </span>
                        </div>
                        <span class="inline-flex items-center text-rose-600 text-sm font-medium">
                            Zobrazit doporučení
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </span>
                    </div>
                </div>
            </div>
        </a>
    <?php
        wp_reset_postdata();
    else:
    ?>
        <div class="block p-4 pt-14">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                <div class="md:col-span-1 rounded-lg overflow-hidden card-shadow">
                    <div class="h-44 bg-rose-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-rose-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                        </svg>
                    </div>
                </div>

                <div class="md:col-span-2">
                    <h3 class="text-xl font-bold text-gray-800 mb-3">Nejlepší chytré spotřebiče pro moderní domov</h3>
                    <p class="text-gray-600 line-clamp-3">Vybrané chytré spotřebiče, které vám usnadní život a ušetří energii. Porovnání nejlepších modelů na trhu.</p>

                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                </svg>
                                Doporučený produkt
                            </span>
                        </div>
                        <span class="inline-flex items-center text-rose-600 text-sm font-medium">
                            Zobrazit doporučení
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Doporučujeme list snippets -->
    <div class="px-4 pb-4">
        <div class="border-t border-gray-100 pt-4 mt-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <?php
                if ($more_articles->have_posts()) :
                    while ($more_articles->have_posts()) : $more_articles->the_post();
                ?>
                    <div class="group">
                        <a href="<?php the_permalink(); ?>" class="block">
                            <h4 class="text-sm font-bold text-gray-800 group-hover:text-rose-700 transition-colors line-clamp-2"><?php the_title(); ?></h4>
                            <p class="text-xs text-gray-500 mt-1 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                </svg>
                                Doporučený produkt
                            </p>
                        </a>
                    </div>
                <?php
                    endwhile;
                    wp_reset_postdata();
                else:
                    // Display placeholder if no articles
                    for ($i = 0; $i < 3; $i++):
                ?>
                    <div>
                        <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div class="h-3 bg-gray-100 rounded w-1/2"></div>
                    </div>
                <?php
                    endfor;
                endif;
                ?>
            </div>

            <div class="mt-4 text-right">
                <a href="<?php echo esc_url($category_url); ?>" class="text-rose-600 text-sm font-medium inline-flex items-center hover:text-rose-800">
                    Všechna doporučení
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
</div>
