<?php
/**
 * Template part for displaying the INTERIÉR section on homepage
 *
 * @package _uw-theme
 */

$category_slug = 'interier';
$category_name = 'INTERIÉR';
$category_url = esc_url(get_category_link(get_cat_ID($category_slug)));
$color_class = dumabyt_get_category_color($category_slug);

// Get the featured article
$featured_article = dumabyt_get_category_articles($category_slug, 1);
// Get additional articles
$more_articles = dumabyt_get_category_articles($category_slug, 2, 1);
?>

<!-- Main INTERIÉR article -->
<div class="md:col-span-2 md:row-span-1 relative group">
    <?php 
    // Display category badge
    dumabyt_category_badge($category_name, $category_url, $color_class);
    
    // Display featured article or placeholder
    if ($featured_article->have_posts()) :
        $featured_article->the_post();
        $post = get_post();
        $height_class = 'h-48 md:h-60';
        include(get_template_directory() . '/template-parts/homepage/components/featured-card.php');
        wp_reset_postdata();
    else:
    ?>
        <div class="relative h-48 md:h-60 rounded-xl overflow-hidden card-shadow bg-gradient-to-br from-cyan-500 to-cyan-700">
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent z-0"></div>
            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Interiér" class="w-full h-full object-cover">
            <div class="absolute bottom-0 left-0 right-0 p-4 z-10">
                <h3 class="text-lg font-bold text-white mb-1">Jak vybrat správný nábytek pro malé prostory</h3>
                <p class="text-xs text-cyan-50">Praktické tipy a triky pro zařízení malých bytů a interiérů.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Extra small interior articles grid -->
<div class="md:col-span-2 md:row-span-1 grid grid-cols-2 gap-4">
    <?php
    if ($more_articles->have_posts()) :
        while ($more_articles->have_posts()) : $more_articles->the_post();
    ?>
        <div class="bg-white rounded-lg overflow-hidden card-elevation group">
            <a href="<?php the_permalink(); ?>" class="block">
                <div class="relative h-32 overflow-hidden">
                    <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('thumbnail', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-500')); ?>
                    <?php else: ?>
                        <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="<?php the_title_attribute(); ?>" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500">
                    <?php endif; ?>
                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="p-3">
                    <h4 class="text-sm font-bold text-gray-800 group-hover:text-cyan-700 transition-colors line-clamp-2"><?php the_title(); ?></h4>
                </div>
            </a>
        </div>
    <?php 
        endwhile;
        wp_reset_postdata();
    else:
        // Display placeholder if no articles
        for ($i = 0; $i < 2; $i++):
    ?>
        <div class="bg-white rounded-lg overflow-hidden card-elevation">
            <div class="h-32 bg-gray-100"></div>
            <div class="p-3">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div class="h-3 bg-gray-100 rounded w-1/2"></div>
            </div>
        </div>
    <?php 
        endfor;
    endif; 
    ?>
    
    <div class="col-span-2 mt-1 text-right">
        <a href="<?php echo esc_url(get_category_link(get_cat_ID('interier'))); ?>" class="text-cyan-700 text-sm font-medium inline-flex items-center hover:text-cyan-900">
            Všechny články
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </a>
    </div>
</div>
