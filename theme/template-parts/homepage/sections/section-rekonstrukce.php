<?php
/**
 * Template part for displaying the REKONSTRUKCE section on homepage
 *
 * @package _uw-theme
 */

$category_slug = 'rekonstrukce';
$category_name = 'REKONSTRUKCE';
$category_url = esc_url(get_category_link(get_cat_ID($category_slug)));
$color_class = dumabyt_get_category_color($category_slug);

// Get the featured article
$featured_article = dumabyt_get_category_articles($category_slug, 1);
// Get additional articles
$more_articles = dumabyt_get_category_articles($category_slug, 3, 1, false);
?>

<!-- REKONSTRUKCE - Modern wide card with orange theme -->
<div class="relative group">
    <?php 
    // Display category badge
    dumabyt_category_badge($category_name, $category_url, $color_class);
    
    // Display featured article or placeholder
    if ($featured_article->have_posts()) :
        $featured_article->the_post();
        $post = get_post();
        $height_class = 'h-64';
        include(get_template_directory() . '/template-parts/homepage/components/featured-card.php');
        wp_reset_postdata();
    else:
    ?>
        <div class="relative h-64 rounded-xl overflow-hidden card-shadow bg-gradient-to-br from-orange-500 to-orange-700">
            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent z-0"></div>
            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Rekonstrukce" class="w-full h-full object-cover">
            <div class="absolute bottom-0 left-0 right-0 p-5 z-10">
                <h3 class="text-xl font-bold text-white mb-2">Jak na úspěšnou rekonstrukci podkroví</h3>
                <p class="text-sm text-orange-50 mb-3">Využijte nevyužitý půdní prostor pro nové komfortní bydlení.</p>
                <span class="inline-flex items-center text-orange-200 text-sm font-medium">
                    Číst více
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                </span>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- REKONSTRUKCE Article list with three small cards -->
<div class="md:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-4">
    <?php
    if ($more_articles->have_posts()) :
        while ($more_articles->have_posts()) : $more_articles->the_post();
    ?>
        <div class="group bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
            <a href="<?php the_permalink(); ?>" class="block p-3">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0 w-24 h-24 bg-gray-100 rounded overflow-hidden">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('thumbnail', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-500')); ?>
                        <?php else: ?>
                            <div class="w-full h-full bg-orange-100 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                                </svg>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="text-base font-bold text-gray-800 group-hover:text-orange-700 transition-colors line-clamp-2 mb-1"><?php the_title(); ?></h4>
                        <p class="text-sm text-gray-600 line-clamp-2"><?php echo wp_trim_words(get_the_excerpt(), 10); ?></p>
                    </div>
                </div>
            </a>
        </div>
    <?php 
        endwhile;
        wp_reset_postdata();
    else:
        // Display placeholder if no articles
        for ($i = 0; $i < 3; $i++):
    ?>
        <div class="group bg-white rounded-lg shadow-sm p-3">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0 w-24 h-24 bg-gray-100 rounded"></div>
                <div class="flex-1 min-w-0">
                    <div class="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div class="h-4 bg-gray-100 rounded w-full mb-1"></div>
                    <div class="h-4 bg-gray-100 rounded w-2/3"></div>
                </div>
            </div>
        </div>
    <?php 
        endfor;
    endif; 
    ?>
    
    <div class="md:col-span-3 mt-1 text-right">
        <a href="<?php echo esc_url(get_category_link(get_cat_ID('rekonstrukce'))); ?>" class="text-orange-600 text-sm font-medium inline-flex items-center hover:text-orange-800">
            Všechny články o rekonstrukcích
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </a>
    </div>
</div>
