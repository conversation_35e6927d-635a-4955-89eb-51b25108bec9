<?php
/**
 * Template part for displaying the combined DŮM and INTERIÉR section on homepage
 *
 * @package dumabyt
 */

// DŮM section
$dum_slug = 'dum';
$dum_name = 'DŮM';
$dum_url = esc_url(get_category_link(get_cat_ID($dum_slug)));
$dum_color = dumabyt_get_category_color($dum_slug);

// INTERIÉR section
$interier_slug = 'interier';
$interier_name = 'INTERIÉR';
$interier_url = esc_url(get_category_link(get_cat_ID($interier_slug)));
$interier_color = dumabyt_get_category_color($interier_slug);

// Get featured articles
$dum_featured = dumabyt_get_category_articles($dum_slug, 1);
$interier_featured = dumabyt_get_category_articles($interier_slug, 1);

// Get additional articles
$dum_more = dumabyt_get_category_articles($dum_slug, 3, 1, false);
$interier_more = dumabyt_get_category_articles($interier_slug, 3, 1, false);
?>

<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- DŮM section - Left side -->
    <div class="relative">
        <!-- DŮM header -->
        <div class="flex items-center justify-between mb-4">
            <a href="<?php echo esc_url($dum_url); ?>" class="<?php echo esc_attr($dum_color); ?> text-white font-bold uppercase py-1.5 px-4 rounded-full text-xs tracking-wider badge-shadow inline-flex items-center">
                <span><?php echo esc_html($dum_name); ?></span>
            </a>
        </div>
        
        <!-- DŮM featured article -->
        <div class="relative group rounded-xl overflow-hidden card-elevation mb-4">
            <?php
            if ($dum_featured->have_posts()) :
                $dum_featured->the_post();
            ?>
                <a href="<?php the_permalink(); ?>" class="block">
                    <div class="relative h-64 overflow-hidden">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('medium_large', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-500')); ?>
                        <?php else: ?>
                            <div class="h-64 bg-amber-100 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-amber-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                            </div>
                        <?php endif; ?>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                        <div class="absolute bottom-0 left-0 right-0 p-4">
                            <h3 class="text-lg font-bold text-white mb-1"><?php the_title(); ?></h3>
                            <span class="inline-flex items-center text-amber-200 text-xs font-medium">
                                Číst více
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </span>
                        </div>
                    </div>
                </a>
            <?php
                wp_reset_postdata();
            else:
            ?>
                <div class="relative h-48 bg-gradient-to-br from-amber-500 to-amber-700">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 p-4">
                        <h3 class="text-lg font-bold text-white mb-1">Moderní rodinný dům se zahradou</h3>
                        <span class="inline-flex items-center text-amber-200 text-xs font-medium">
                            Číst více
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- DŮM additional articles -->
        <div class="bg-white rounded-xl overflow-hidden card-elevation p-4">
            <div class="space-y-3">
                <?php
                if ($dum_more->have_posts()) :
                    while ($dum_more->have_posts()) : $dum_more->the_post();
                ?>
                    <div class="group">
                        <a href="<?php the_permalink(); ?>" class="flex items-start space-x-3 group-hover:bg-amber-50 rounded-lg transition-all duration-300 p-2">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded overflow-hidden">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('thumbnail', array('class' => 'w-full h-full object-cover')); ?>
                                <?php else: ?>
                                    <div class="w-full h-full bg-amber-100 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-bold text-gray-800 group-hover:text-amber-700 transition-colors line-clamp-2"><?php the_title(); ?></h4>
                            </div>
                        </a>
                    </div>
                <?php 
                    endwhile;
                    wp_reset_postdata();
                else:
                    // Display placeholder if no articles
                    for ($i = 0; $i < 3; $i++):
                ?>
                    <div class="group bg-white rounded-lg shadow-sm p-3">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded"></div>
                            <div class="flex-1 min-w-0">
                                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                <div class="h-3 bg-gray-100 rounded w-1/2"></div>
                            </div>
                        </div>
                    </div>
                <?php 
                    endfor;
                endif; 
                ?>
            </div>
            
            <!-- Link to all DŮM articles -->
            <div class="mt-3 text-right">
                <a href="<?php echo esc_url($dum_url); ?>" class="text-amber-600 text-sm font-medium inline-flex items-center hover:text-amber-800">
                    Všechny články o bydlení
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
    
    <!-- INTERIÉR section - Right side -->
    <div class="relative">
        <!-- INTERIÉR header -->
        <div class="flex items-center justify-between mb-4">
            <a href="<?php echo esc_url($interier_url); ?>" class="<?php echo esc_attr($interier_color); ?> text-white font-bold uppercase py-1.5 px-4 rounded-full text-xs tracking-wider badge-shadow inline-flex items-center">
                <span><?php echo esc_html($interier_name); ?></span>
            </a>
        </div>
        
        <!-- INTERIÉR featured article -->
        <div class="relative group rounded-xl overflow-hidden card-elevation mb-4">
            <?php
            if ($interier_featured->have_posts()) :
                $interier_featured->the_post();
            ?>
                <a href="<?php the_permalink(); ?>" class="block">
                    <div class="relative h-64 overflow-hidden">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('medium_large', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-500')); ?>
                        <?php else: ?>
                            <div class="h-64 bg-cyan-100 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-cyan-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                </svg>
                            </div>
                        <?php endif; ?>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                        <div class="absolute bottom-0 left-0 right-0 p-4">
                            <h3 class="text-lg font-bold text-white mb-1"><?php the_title(); ?></h3>
                            <span class="inline-flex items-center text-cyan-200 text-xs font-medium">
                                Číst více
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </span>
                        </div>
                    </div>
                </a>
            <?php
                wp_reset_postdata();
            else:
            ?>
                <div class="relative h-48 bg-gradient-to-br from-cyan-500 to-cyan-700">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 p-4">
                        <h3 class="text-lg font-bold text-white mb-1">Jak vybrat správný nábytek pro malé prostory</h3>
                        <span class="inline-flex items-center text-cyan-200 text-xs font-medium">
                            Číst více
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- INTERIÉR additional articles -->
        <div class="bg-white rounded-xl overflow-hidden card-elevation p-4">
            <div class="space-y-3">
                <?php
                if ($interier_more->have_posts()) :
                    while ($interier_more->have_posts()) : $interier_more->the_post();
                ?>
                    <div class="group">
                        <a href="<?php the_permalink(); ?>" class="flex items-start space-x-3 group-hover:bg-amber-50 rounded-lg transition-all duration-300 p-2">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded overflow-hidden">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('thumbnail', array('class' => 'w-full h-full object-cover')); ?>
                                <?php else: ?>
                                    <div class="w-full h-full bg-cyan-100 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-cyan-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-bold text-gray-800 group-hover:text-cyan-700 transition-colors line-clamp-2"><?php the_title(); ?></h4>
                            </div>
                        </a>
                    </div>
                <?php 
                    endwhile;
                    wp_reset_postdata();
                else:
                    // Display placeholder if no articles
                    for ($i = 0; $i < 3; $i++):
                ?>
                    <div class="group bg-white rounded-lg shadow-sm p-3">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded"></div>
                            <div class="flex-1 min-w-0">
                                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                <div class="h-3 bg-gray-100 rounded w-1/2"></div>
                            </div>
                        </div>
                    </div>
                <?php 
                    endfor;
                endif; 
                ?>
            </div>
            
            <!-- Link to all INTERIÉR articles -->
            <div class="mt-4 text-right">
                <a href="<?php echo esc_url($interier_url); ?>" class="text-cyan-600 text-sm font-medium inline-flex items-center hover:text-cyan-800">
                    Všechny články o interiéru
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
</div>
