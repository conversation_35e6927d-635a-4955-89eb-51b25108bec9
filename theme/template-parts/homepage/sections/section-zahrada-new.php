<?php
/**
 * Template part for displaying the ZAHRADA section on homepage with category UX
 *
 * @package dumabyt
 */

// Get category information
$category_slug = 'zahrada';
$category_name = 'ZAHRADA';
$category = get_category_by_slug($category_slug);
$category_url = esc_url(get_category_link($category->term_id));
$color_class = dumabyt_get_category_color($category_slug);
$gradient_class = dumabyt_get_category_gradient($category_slug);
$text_color = str_replace('bg-', 'text-', $color_class);
$hover_color = str_replace('bg-', 'hover:bg-', $color_class);
$border_color = str_replace('bg-', 'border-', $color_class);
$text_hover_color = str_replace('bg-', 'hover:text-', $color_class);

// Get posts
$posts_per_page = 3;
$category_posts = dumabyt_get_category_articles($category_slug, $posts_per_page);
?>


<!-- Grid of Posts -->
<?php if ($category_posts->have_posts()) : ?>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <?php 
        while ($category_posts->have_posts()) : 
            $category_posts->the_post();
            get_template_part('template-parts/content/content', 'category-card', array(
                'category_name' => $category_name,
                'category_url' => $category_url,
                'color_class' => $color_class,
                'text_color' => $text_color,
                'text_hover_color' => $text_hover_color
            ));
        endwhile;
        wp_reset_postdata();
        ?>
    </div>
<?php else : ?>
    <div class="bg-white rounded-xl p-6 text-center mb-6">
        <p class="text-gray-600">V této kategorii zatím nejsou žádné články.</p>
    </div>
<?php endif; ?>

<!-- View All Link -->
<div class="mb-8 text-right">
    <a href="<?php echo $category_url; ?>" class="inline-flex items-center text-sm font-medium <?php echo $text_color; ?> hover:<?php echo $text_color; ?>/80 transition-colors">
        Všechny články v kategorii <?php echo $category_name; ?>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
        </svg>
    </a>
</div>
