<?php
/**
 * Template part for displaying the DŮM section on homepage
 *
 * @package _uw-theme
 */

$category_slug = 'dum';
$category_name = 'DŮM';
$category_url = esc_url(get_category_link(get_cat_ID($category_slug)));
$color_class = dumabyt_get_category_color($category_slug);

// Get the featured article
$articles = dumabyt_get_category_articles($category_slug, 1);
?>

<div class="md:col-span-2 md:row-span-2 relative group">
    <?php 
    // Display category badge
    dumabyt_category_badge($category_name, $category_url, $color_class);
    
    // Display featured article or placeholder
    if ($articles->have_posts()) :
        $articles->the_post();
        $post = get_post();
        $height_class = 'h-full';
        include(get_template_directory() . '/template-parts/homepage/components/featured-card.php');
        wp_reset_postdata();
    else:
    ?>
        <div class="relative h-full rounded-xl overflow-hidden card-shadow bg-gradient-to-br from-amber-500 to-amber-700">
            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent z-0"></div>
            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Dům" class="w-full h-full object-cover">
            <div class="absolute bottom-0 left-0 right-0 p-6 z-10">
                <h3 class="text-xl md:text-2xl font-bold text-white mb-2">Moderní rodinný dům se zahradou</h3>
                <p class="text-sm text-amber-50 mb-3">Prohlédněte si inspirativní projekty moderních rodinných domů.</p>
                <span class="inline-flex items-center text-amber-200 text-sm font-medium">
                    Číst více
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                </span>
            </div>
        </div>
    <?php endif; ?>
</div>
