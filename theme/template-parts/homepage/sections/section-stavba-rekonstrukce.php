<?php
/**
 * Template part for displaying the combined STAVBA and REKONSTRUKCE section on homepage
 *
 * @package dumabyt
 */

// STAVBA section
$stavba_slug = 'stavba';
$stavba_name = 'STAVBA';
$stavba_url = esc_url(get_category_link(get_cat_ID($stavba_slug)));
$stavba_color = dumabyt_get_category_color($stavba_slug);

// REKONSTRUKCE section
$rekonstrukce_slug = 'rekonstrukce';
$rekonstrukce_name = 'REKONSTRUKCE';
$rekonstrukce_url = esc_url(get_category_link(get_cat_ID($rekonstrukce_slug)));
$rekonstrukce_color = dumabyt_get_category_color($rekonstrukce_slug);

// Get featured articles
$stavba_featured = dumabyt_get_category_articles($stavba_slug, 1);
$rekonstrukce_featured = dumabyt_get_category_articles($rekonstrukce_slug, 1);

// Get additional articles
$stavba_more = dumabyt_get_category_articles($stavba_slug, 3, 1, false);
$rekonstrukce_more = dumabyt_get_category_articles($rekonstrukce_slug, 3, 1, false);
?>

<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- STAVBA section - Left side -->
    <div class="relative">
        <!-- STAVBA header -->
        <div class="flex items-center justify-between mb-4">
            <a href="<?php echo esc_url($stavba_url); ?>" class="<?php echo esc_attr($stavba_color); ?> text-white font-bold uppercase py-1.5 px-4 rounded-full text-xs tracking-wider badge-shadow inline-flex items-center">
                <span><?php echo esc_html($stavba_name); ?></span>
            </a>
        </div>
        
        <!-- STAVBA featured article -->
        <div class="relative group rounded-xl overflow-hidden card-elevation mb-4">
            <?php
            if ($stavba_featured->have_posts()) :
                $stavba_featured->the_post();
            ?>
                <a href="<?php the_permalink(); ?>" class="block">
                    <div class="relative h-64 overflow-hidden">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('medium_large', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-500')); ?>
                        <?php else: ?>
                            <div class="h-64 bg-emerald-100 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-emerald-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                            </div>
                        <?php endif; ?>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                        <div class="absolute bottom-0 left-0 right-0 p-4">
                            <h3 class="text-lg font-bold text-white mb-1"><?php the_title(); ?></h3>
                            <span class="inline-flex items-center text-emerald-200 text-xs font-medium">
                                Číst více
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </span>
                        </div>
                    </div>
                </a>
            <?php
                wp_reset_postdata();
            else:
            ?>
                <div class="relative h-64 bg-gradient-to-br from-emerald-500 to-emerald-700">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 p-4">
                        <h3 class="text-lg font-bold text-white mb-1">Moderní stavební technologie</h3>
                        <span class="inline-flex items-center text-emerald-200 text-xs font-medium">
                            Číst více
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- STAVBA additional articles -->
        <div class="bg-white rounded-xl overflow-hidden card-elevation p-4">
            <div class="space-y-3">
                <?php
                if ($stavba_more->have_posts()) :
                    while ($stavba_more->have_posts()) : $stavba_more->the_post();
                ?>
                    <div class="group">
                        <a href="<?php the_permalink(); ?>" class="flex items-start space-x-3 group-hover:bg-amber-50 rounded-lg transition-all duration-300 p-2">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded overflow-hidden">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('thumbnail', array('class' => 'w-full h-full object-cover')); ?>
                                <?php else: ?>
                                    <div class="w-full h-full bg-emerald-100 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-emerald-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-bold text-gray-800 group-hover:text-emerald-700 transition-colors line-clamp-2"><?php the_title(); ?></h4>
                            </div>
                        </a>
                    </div>
                <?php 
                    endwhile;
                    wp_reset_postdata();
                else:
                    // Display placeholder if no articles
                    for ($i = 0; $i < 3; $i++):
                ?>
                    <div class="group bg-white rounded-lg shadow-sm p-3">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded"></div>
                            <div class="flex-1 min-w-0">
                                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                <div class="h-3 bg-gray-100 rounded w-1/2"></div>
                            </div>
                        </div>
                    </div>
                <?php 
                    endfor;
                endif; 
                ?>
            </div>
            
            <!-- Link to all STAVBA articles -->
            <div class="mt-3 text-right">
                <a href="<?php echo esc_url($stavba_url); ?>" class="text-emerald-600 text-sm font-medium inline-flex items-center hover:text-emerald-800">
                    Všechny články o stavbě
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
    
    <!-- REKONSTRUKCE section - Right side -->
    <div class="relative">
        <!-- REKONSTRUKCE header -->
        <div class="flex items-center justify-between mb-4">
            <a href="<?php echo esc_url($rekonstrukce_url); ?>" class="<?php echo esc_attr($rekonstrukce_color); ?> text-white font-bold uppercase py-1.5 px-4 rounded-full text-xs tracking-wider badge-shadow inline-flex items-center">
                <span><?php echo esc_html($rekonstrukce_name); ?></span>
            </a>
        </div>
        
        <!-- REKONSTRUKCE featured article -->
        <div class="relative group rounded-xl overflow-hidden card-elevation mb-4">
            <?php
            if ($rekonstrukce_featured->have_posts()) :
                $rekonstrukce_featured->the_post();
            ?>
                <a href="<?php the_permalink(); ?>" class="block">
                    <div class="relative h-64 overflow-hidden">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('medium_large', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-500')); ?>
                        <?php else: ?>
                            <div class="h-64 bg-orange-100 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-orange-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                                </svg>
                            </div>
                        <?php endif; ?>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                        <div class="absolute bottom-0 left-0 right-0 p-4">
                            <h3 class="text-lg font-bold text-white mb-1"><?php the_title(); ?></h3>
                            <span class="inline-flex items-center text-orange-200 text-xs font-medium">
                                Číst více
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </span>
                        </div>
                    </div>
                </a>
            <?php
                wp_reset_postdata();
            else:
            ?>
                <div class="relative h-64 bg-gradient-to-br from-orange-500 to-orange-700">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 p-4">
                        <h3 class="text-lg font-bold text-white mb-1">Jak na úspěšnou rekonstrukci</h3>
                        <span class="inline-flex items-center text-orange-200 text-xs font-medium">
                            Číst více
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- REKONSTRUKCE additional articles -->
        <div class="bg-white rounded-xl overflow-hidden card-elevation p-4">
            <div class="space-y-3">
                <?php
                if ($rekonstrukce_more->have_posts()) :
                    while ($rekonstrukce_more->have_posts()) : $rekonstrukce_more->the_post();
                ?>
                    <div class="group">
                        <a href="<?php the_permalink(); ?>" class="flex items-start space-x-3 group-hover:bg-amber-50 rounded-lg transition-all duration-300 p-2">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded overflow-hidden">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('thumbnail', array('class' => 'w-full h-full object-cover')); ?>
                                <?php else: ?>
                                    <div class="w-full h-full bg-orange-100 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-bold text-gray-800 group-hover:text-orange-700 transition-colors line-clamp-2"><?php the_title(); ?></h4>
                            </div>
                        </a>
                    </div>
                <?php 
                    endwhile;
                    wp_reset_postdata();
                else:
                    // Display placeholder if no articles
                    for ($i = 0; $i < 3; $i++):
                ?>
                    <div class="group bg-white rounded-lg shadow-sm p-3">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded"></div>
                            <div class="flex-1 min-w-0">
                                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                <div class="h-3 bg-gray-100 rounded w-1/2"></div>
                            </div>
                        </div>
                    </div>
                <?php 
                    endfor;
                endif; 
                ?>
            </div>
            
            <!-- Link to all REKONSTRUKCE articles -->
            <div class="mt-3 text-right">
                <a href="<?php echo esc_url($rekonstrukce_url); ?>" class="text-orange-600 text-sm font-medium inline-flex items-center hover:text-orange-800">
                    Všechny články o rekonstrukci
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
</div>
