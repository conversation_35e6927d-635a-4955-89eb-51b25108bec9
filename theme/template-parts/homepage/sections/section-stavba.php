<?php
/**
 * Template part for displaying the STAVBA section on homepage
 *
 * @package _uw-theme
 */

$category_slug = 'stavba';
$category_name = 'STAVBA';
$category_url = esc_url(get_category_link(get_cat_ID($category_slug)));
$color_class = dumabyt_get_category_color($category_slug);

// Get the featured article
$featured_article = dumabyt_get_category_articles($category_slug, 1);
// Get additional articles
$more_articles = dumabyt_get_category_articles($category_slug, 3, 1);
?>

<!-- STAVBA - Modern card with emerald theme -->
<div class="md:col-span-3 relative group">
    <?php 
    // Display category badge
    dumabyt_category_badge($category_name, $category_url, $color_class);
    
    // Display featured article or placeholder
    if ($featured_article->have_posts()) :
        $featured_article->the_post();
        $post = get_post();
        $height_class = 'h-64';
        include(get_template_directory() . '/template-parts/homepage/components/featured-card.php');
        wp_reset_postdata();
    else:
    ?>
        <div class="relative h-64 rounded-xl overflow-hidden card-shadow bg-gradient-to-br from-emerald-500 to-emerald-700">
            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent z-0"></div>
            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Stavba" class="w-full h-full object-cover">
            <div class="absolute bottom-0 left-0 right-0 p-5 z-10">
                <h3 class="text-xl font-bold text-white mb-2">Trendy v moderních stavebních technologiích</h3>
                <p class="text-sm text-emerald-50 mb-3">Inovativní technologie pro zdravé a úsporné bydlení.</p>
                <span class="inline-flex items-center text-emerald-200 text-sm font-medium">
                    Číst více
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                </span>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- STAVBA Article list with three small cards -->
<div class="md:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-4">
    <?php
    if ($more_articles->have_posts()) :
        while ($more_articles->have_posts()) : $more_articles->the_post();
    ?>
        <div class="bg-white rounded-lg overflow-hidden card-elevation group h-full">
            <a href="<?php the_permalink(); ?>" class="block h-full flex flex-col">
                <div class="relative h-28 overflow-hidden">
                    <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('thumbnail', array('class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-500')); ?>
                    <?php else: ?>
                        <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="<?php the_title_attribute(); ?>" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500">
                    <?php endif; ?>
                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="p-3 flex-grow">
                    <h4 class="text-sm font-bold text-gray-800 group-hover:text-emerald-700 transition-colors line-clamp-2"><?php the_title(); ?></h4>
                </div>
            </a>
        </div>
    <?php 
        endwhile;
        wp_reset_postdata();
    else:
        // Display placeholder if no articles
        for ($i = 0; $i < 3; $i++):
    ?>
        <div class="bg-white rounded-lg overflow-hidden card-elevation">
            <div class="h-28 bg-gray-100"></div>
            <div class="p-3">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div class="h-3 bg-gray-100 rounded w-1/2"></div>
            </div>
        </div>
    <?php 
        endfor;
    endif; 
    ?>
    
    <div class="md:col-span-3 mt-1 text-right">
        <a href="<?php echo esc_url(get_category_link(get_cat_ID('stavba'))); ?>" class="text-emerald-700 text-sm font-medium inline-flex items-center hover:text-emerald-900">
            Všechny články o stavbě
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </a>
    </div>
</div>
