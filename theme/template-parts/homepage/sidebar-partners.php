<?php
/**
 * Template part for displaying partners in the sidebar
 *
 * @package dumabyt
 */

// Optional parameters:
// $partners_count - Number of partners to display (default: 3)
// $offset - Offset for partners query (default: 0)
// $partners_position - Position identifier for styling (default: 'main-sidebar')

// Set default position if not provided
$partners_position = isset($partners_position) ? $partners_position : 'main-sidebar';

// Set appropriate classes based on position
$container_classes = 'space-y-6';
if ($partners_position === 'main-sidebar') {
    $container_classes .= ' lg:col-span-1';
} elseif ($partners_position === 'content-sidebar') {
    $container_classes .= ' lg:col-span-1';
}
?>

<div class="<?php echo esc_attr($container_classes); ?>">
    <?php if ($partners_position === 'main-sidebar' || $partners_position === 'content-sidebar') : ?>
        <div class="bg-white rounded-lg card-elevation overflow-hidden p-4">
            <h3 class="font-bold mb-3 text-gray-700 text-center text-sm uppercase tracking-wider">Partneři</h3>
        </div>
    <?php endif; ?>

    <?php
    // Get partners for sidebar
    $partners_count = isset($partners_count) ? absint($partners_count) : 3;
    $offset = isset($offset) ? absint($offset) : 0;
    $partners_query = dumabyt_get_partners($partners_count, $offset);

    // Initialize counter for placeholders
    $partner_count = 0;

    // Display partners
    if ($partners_query->have_posts()) :
        while ($partners_query->have_posts()) : $partners_query->the_post();
            $partner_count++;
            $partner = get_post();
            $partner_position = $partners_position; // Pass position to banner component
            include(get_template_directory() . '/template-parts/homepage/components/partner-banner.php');
        endwhile;
        wp_reset_postdata();
    endif;

    // Display placeholders if fewer than partners_count
    if ($partner_count < $partners_count) :
        for ($i = $partner_count; $i < $partners_count; $i++) :
            $partner_position = $partners_position; // Pass position to banner component
            include(get_template_directory() . '/template-parts/homepage/components/partner-banner.php');
        endfor;
    endif;
    ?>
</div>
