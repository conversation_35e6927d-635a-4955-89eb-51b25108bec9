<?php
/**
 * Template part for displaying a featured card with image and overlay text
 *
 * @package _uw-theme
 */

// Required parameters:
// $post - The post object
// $category_slug - The category slug for color theming
// $height_class - CSS class for controlling the height of the card

if (!isset($post) || !isset($category_slug) || !isset($height_class)) {
    return;
}

$gradient_class = dumabyt_get_category_gradient($category_slug);
$permalink = get_permalink($post);
$title = get_the_title($post);
$excerpt = wp_trim_words(get_the_excerpt($post), 20);
$color_class = str_replace('from-', 'text-', explode(' ', $gradient_class)[0]);
$color_class_light = str_replace('500', '200', $color_class);
?>

<div class="relative group rounded-xl overflow-hidden card-shadow bg-gradient-to-br <?php echo esc_attr($gradient_class); ?>">
    <a href="<?php echo esc_url($permalink); ?>" class="block relative <?php echo esc_attr($height_class); ?> transform group-hover:scale-105 transition-transform duration-700 ease-in-out">
        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent z-0"></div>
        <?php if (has_post_thumbnail($post)) : ?>
            <?php echo get_the_post_thumbnail($post, 'large', array('class' => 'w-full h-full object-cover')); ?>
        <?php else: ?>
            <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="<?php echo esc_attr($title); ?>" class="w-full h-full object-cover">
        <?php endif; ?>
        
        <div class="absolute bottom-0 left-0 right-0 p-5 z-10 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
            <h3 class="text-xl font-bold text-white mb-2"><?php echo esc_html($title); ?></h3>
            <p class="text-sm text-white/80 line-clamp-2 mb-3"><?php echo esc_html($excerpt); ?></p>
            <span class="inline-flex items-center <?php echo esc_attr($color_class_light); ?> text-sm font-medium">
                Číst více
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
            </span>
        </div>
    </a>
</div>
