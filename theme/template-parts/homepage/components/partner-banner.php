<?php
/**
 * Template part for displaying a partner banner or placeholder
 *
 * @package dumabyt
 */

// Optional parameters:
// $partner - The partner post object
// $partner_position - Position identifier for styling (default: 'main-sidebar')

// Set default position if not provided
$partner_position = isset($partner_position) ? $partner_position : 'main-sidebar';

// Set appropriate classes and dimensions based on position
$container_classes = 'bg-white rounded-lg card-elevation overflow-hidden';
$banner_size = 'Banner 300x250 px';
$banner_height = 'py-10';

if ($partner_position === 'featured') {
    $container_classes .= ' h-full';
    $banner_size = 'Banner 600x300 px';
    $banner_height = 'py-16';
} elseif ($partner_position === 'content-sidebar') {
    $container_classes .= ' mb-6';
}
?>

<div class="<?php echo esc_attr($container_classes); ?>">
    <?php if (isset($partner) && is_a($partner, 'WP_Post')) :
        $partner_url = get_post_meta($partner->ID, '_partner_url', true);
        if (empty($partner_url)) {
            $partner_url = '#';
        }

        // Get partner name for alt text
        $partner_name = get_the_title($partner);
    ?>
        <a href="<?php echo esc_url($partner_url); ?>" class="block h-full" <?php if ($partner_url !== '#') echo 'target="_blank" rel="noopener"'; ?>>
            <?php
            if (has_post_thumbnail($partner)) {
                if ($partner_position === 'featured') {
                    echo get_the_post_thumbnail($partner, 'large', array('class' => 'w-full h-full object-cover'));
                } else {
                    echo get_the_post_thumbnail($partner, 'medium', array('class' => 'w-full h-auto'));
                }
            } else {
                echo '<img src="' . get_template_directory_uri() . '/screenshot.png" alt="' . esc_attr($partner_name) . '" class="w-full h-auto">';
            }
            ?>
            <div class="p-2 bg-gray-50 text-xs text-gray-400 text-center border-t border-gray-100">
                <?php echo esc_html($partner_name); ?>
            </div>
        </a>
    <?php else: ?>
        <div class="p-4 text-center h-full flex flex-col">
            <h3 class="text-gray-500 font-medium mb-2 text-sm">Místo pro vašeho partnera</h3>
            <div class="bg-gray-100 <?php echo $banner_height; ?> px-4 rounded flex-grow flex flex-col items-center justify-center">
                <span class="text-gray-400 text-2xl block mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1H2a1 1 0 01-1-1v-3a1 1 0 011-1h1a2 2 0 100-4H2a1 1 0 01-1-1V7a1 1 0 011-1h3a1 1 0 001-1V4a2 2 0 114 0v1a1 1 0 001 1h1a2 2 0 100-4h-1a1 1 0 00-1-1V1a1 1 0 00-1-1" />
                    </svg>
                </span>
                <p class="text-gray-400 text-sm"><?php echo esc_html($banner_size); ?></p>
            </div>
            <div class="mt-2 text-xs text-gray-400">
                <a href="#" class="hover:underline">Informace o reklamě</a>
            </div>
        </div>
    <?php endif; ?>
</div>
