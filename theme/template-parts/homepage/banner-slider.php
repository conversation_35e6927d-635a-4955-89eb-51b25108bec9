<?php
/**
 * Template part for displaying the main banner slideshow
 *
 * @package _uw-theme
 */

// Optional parameters:
// $posts_count - Number of posts to display in the slider (default: 5)
?>

<div class="lg:col-span-3">
    <div class="slideshow-container relative overflow-hidden rounded-xl card-shadow h-[400px] sm:h-[500px] md:h-[600px]">
        <?php
        // Get featured posts for slideshow
        $posts_count = isset($posts_count) ? absint($posts_count) : 5;
        $featured_posts = new WP_Query(array(
            'posts_per_page' => $posts_count,
            'meta_key' => '_thumbnail_id',
            'post_type' => 'post',
            'post_status' => 'publish'
        ));
        
        if ($featured_posts->have_posts()) :
            $slide_index = 0;
            while ($featured_posts->have_posts()) : $featured_posts->the_post();
                $slide_index++;
        ?>
            <div class="slide absolute inset-0 transition-all duration-500 ease-in-out <?php echo $slide_index === 1 ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full'; ?>">
                <?php if (has_post_thumbnail()) : ?>
                <a href="<?php the_permalink(); ?>" class="block h-full">
                    <?php the_post_thumbnail('full', array('class' => 'w-full h-full object-cover')); ?>
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent/20 flex items-end">
                        <div class="p-4 md:p-6 text-white">
                            <h2 class="text-xl md:text-2xl lg:text-3xl font-bold font-trajan"><?php the_title(); ?></h2>
                            <p class="mt-2 text-sm md:text-base"><?php echo wp_trim_words(get_the_excerpt(), 35); ?></p>
                        </div>
                    </div>
                </a>
                <?php endif; ?>
            </div>
        <?php
            endwhile;
            wp_reset_postdata();
        else :
            // Fallback if no featured posts
        ?>
            <div class="slide absolute inset-0 opacity-100">
                <img src="<?php echo get_template_directory_uri(); ?>/screenshot.png" alt="Dům a Byt" class="w-full h-full object-cover">
                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                    <div class="p-6 text-white">
                        <h2 class="text-3xl font-bold">DŮM & BYT</h2>
                        <p class="mt-2">Váš průvodce světem bydlení a stavby</p>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Slider navigation dots -->
        <div class="absolute bottom-3 left-0 right-0 flex justify-center space-x-2">
            <?php for ($i = 0; $i < min($featured_posts->post_count, $posts_count); $i++) : ?>
                <button class="slider-dot w-2.5 h-2.5 rounded-full bg-white/30 hover:bg-white/80 transition-colors <?php echo $i === 0 ? 'bg-white/80' : ''; ?>" data-slide="<?php echo $i; ?>"></button>
            <?php endfor; ?>
        </div>
        
        <!-- Slider arrows -->
        <button class="slider-prev absolute left-3 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center rounded-full bg-black/20 hover:bg-black/40 text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
        </button>
        <button class="slider-next absolute right-3 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center rounded-full bg-black/20 hover:bg-black/40 text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
    </div>
</div>
