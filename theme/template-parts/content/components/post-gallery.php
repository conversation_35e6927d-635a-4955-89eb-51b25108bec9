<?php
/**
 * Template part for displaying post gallery in single.php
 *
 * @package dumabyt
 */

// Get attached images to the post
$images = get_attached_media('image', get_the_ID());

// Check if we have a sabre_gallery metadata with image IDs
$sabre_gallery = get_post_meta(get_the_ID(), 'sabre_gallery', true);
if (!empty($sabre_gallery) && is_string($sabre_gallery)) {
    // Decode JSON data if it's a string, with error handling
    try {
        $sabre_gallery = json_decode($sabre_gallery, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $sabre_gallery = null;
        }
    } catch (Exception $e) {
        $sabre_gallery = null;
    }
}

// If we have sabre_gallery metadata with image IDs, use those images
if (!empty($sabre_gallery) && !empty($sabre_gallery['image_ids']) && is_array($sabre_gallery['image_ids']) && count($sabre_gallery['image_ids']) > 0) {
    $gallery_images = array();
    foreach ($sabre_gallery['image_ids'] as $image_id) {
        $attachment = get_post($image_id);
        if ($attachment && $attachment->post_type === 'attachment') {
            $gallery_images[] = $attachment;
        }
    }
    
    // Use migrated gallery images if found
    if (!empty($gallery_images)) {
        $images = $gallery_images;
    }
}

// Check if post content contains gallery shortcode and parse IDs
if (empty($gallery_images)) {
    $post = get_post(get_the_ID());
    if ($post && !empty($post->post_content)) {
        // Look for gallery shortcode with IDs
        if (preg_match('/\[gallery.*?ids="([^"]+)".*?\]/i', $post->post_content, $matches)) {
            $gallery_ids = explode(',', $matches[1]);
            $gallery_images = array();
            
            foreach ($gallery_ids as $image_id) {
                $attachment = get_post(trim($image_id));
                if ($attachment && $attachment->post_type === 'attachment') {
                    $gallery_images[] = $attachment;
                }
            }
            
            // Use shortcode gallery images if found
            if (!empty($gallery_images)) {
                $images = $gallery_images;
            }
        }
    }
}

// Set default value for category color (used for SVG icon)
$category_color = 'text-amber-600';

// Get main category to determine color scheme
$categories = get_the_category();
if ($categories) {
    $category = $categories[0]; // Get the first category
    
    // Set color based on category slug - matching our category scheme
    if ($category->slug === 'interier') {
        $category_color = 'text-cyan-600';
    } elseif ($category->slug === 'stavba') {
        $category_color = 'text-emerald-600';
    } elseif ($category->slug === 'rekonstrukce') {
        $category_color = 'text-orange-600';
    } elseif ($category->slug === 'zahrada') {
        $category_color = 'text-teal-600';
    } elseif ($category->slug === 'blog') {
        $category_color = 'text-purple-600';
    }
}

// Display gallery if there are images (even just one)
if (count($images) > 0) : 
?>
<div class="my-8 not-prose px-6">
    <h3 class="text-xl font-bold mb-4 text-gray-800 font-trajan inline-flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 <?php echo $category_color; ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        Galerie
    </h3>
    <div class="slideshow-container relative overflow-hidden rounded-lg shadow-sm">
        <div class="flex space-x-3 px-2 py-3 overflow-x-auto scrollbar-hide">
        <?php foreach ($images as $image) : 
            $thumb_url = wp_get_attachment_image_src($image->ID, 'thumbnail', false);
            $full_url = wp_get_attachment_image_src($image->ID, 'full', false);
            $caption = wp_get_attachment_caption($image->ID);
            $alt_text = get_post_meta($image->ID, '_wp_attachment_image_alt', true);
            $alt_text = !empty($alt_text) ? $alt_text : 'Obrázek z galerie';
            if ($thumb_url && $full_url) :
        ?>
            <div class="gallery-item flex-shrink-0" style="width: 150px;">
                <a href="<?php echo esc_url($full_url[0]); ?>" class="block rounded-lg overflow-hidden hover:opacity-90 transition-all duration-300 shadow-sm hover:shadow-md group" data-lightbox="gallery">
                    <div class="relative aspect-square">
                        <img src="<?php echo esc_url($thumb_url[0]); ?>" alt="<?php echo esc_attr($alt_text); ?>" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500">
                        <div class="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                            </svg>
                        </div>
                    </div>
                </a>
                <?php if (!empty($caption)) : ?>
                    <div class="mt-2 px-1">
                        <p class="text-xs text-gray-600 leading-tight"><?php echo esc_html($caption); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        <?php 
            endif;
        endforeach; 
        ?>
        </div>
        <button class="slider-prev absolute left-0 top-1/2 transform -translate-y-1/2 bg-black/60 text-white rounded-r-md p-2 hover:bg-black/80 focus:outline-none z-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
        </button>
        <button class="slider-next absolute right-0 top-1/2 transform -translate-y-1/2 bg-black/60 text-white rounded-l-md p-2 hover:bg-black/80 focus:outline-none z-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
    </div>
</div>
<?php endif; ?>
