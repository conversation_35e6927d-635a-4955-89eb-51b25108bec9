<?php
/**
 * Template part for displaying technical specifications in single.php
 *
 * @package dumabyt
 */

// Get technical specs using post meta
$zastavena_plocha = get_post_meta(get_the_ID(), 'zastavena_plocha', true);
$uzitna_plocha = get_post_meta(get_the_ID(), 'uzitna_plocha', true);
$pocet_mistnosti = get_post_meta(get_the_ID(), 'pocet_mistnosti', true);

// Set default value for category color and background
$category_color = 'text-amber-600 hover:text-amber-700';
$category_bg = 'bg-amber-100';

// Get main category
$categories = get_the_category();
if ($categories) {
    $category = $categories[0]; // Get the first category
    
    // Set color based on category slug - matching our category scheme
    if ($category->slug === 'interier') {
        $category_color = 'text-cyan-600 hover:text-cyan-700';
        $category_bg = 'bg-cyan-100';
    } elseif ($category->slug === 'stavba') {
        $category_color = 'text-emerald-600 hover:text-emerald-700';
        $category_bg = 'bg-emerald-100';
    } elseif ($category->slug === 'rekonstrukce') {
        $category_color = 'text-orange-600 hover:text-orange-700';
        $category_bg = 'bg-orange-100';
    } elseif ($category->slug === 'zahrada') {
        $category_color = 'text-teal-600 hover:text-teal-700';
        $category_bg = 'bg-teal-100';
    } elseif ($category->slug === 'blog') {
        $category_color = 'text-purple-600 hover:text-purple-700';
        $category_bg = 'bg-purple-100';
    }
}

// Only display specs section if we have any specs data
$tech_specs = ($zastavena_plocha || $uzitna_plocha || $pocet_mistnosti);

if ($tech_specs) : 
?>
<div class="my-8 <?php echo $category_bg; ?> p-6 rounded-lg border border-gray-100 not-prose px-6">
    <h3 class="text-xl font-bold mb-4 text-gray-800 font-trajan inline-flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 <?php echo $category_color; ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
        </svg>
        Technické parametry
    </h3>
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <?php if ($zastavena_plocha) : ?>
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <h4 class="text-sm uppercase <?php echo $category_color; ?> font-semibold mb-1">Zastavěná plocha</h4>
            <p class="text-xl font-bold text-gray-800"><?php echo esc_html($zastavena_plocha); ?> m<sup>2</sup></p>
        </div>
        <?php endif; ?>
        
        <?php if ($uzitna_plocha) : ?>
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <h4 class="text-sm uppercase <?php echo $category_color; ?> font-semibold mb-1">Užitná plocha</h4>
            <p class="text-xl font-bold text-gray-800"><?php echo esc_html($uzitna_plocha); ?> m<sup>2</sup></p>
        </div>
        <?php endif; ?>
        
        <?php if ($pocet_mistnosti) : ?>
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <h4 class="text-sm uppercase <?php echo $category_color; ?> font-semibold mb-1">Počet místností</h4>
            <p class="text-xl font-bold text-gray-800"><?php echo esc_html($pocet_mistnosti); ?></p>
        </div>
        <?php endif; ?>
        
        <?php 
        // Display any additional technical specs fields if they exist
        $dodatecne_parametry = get_post_meta(get_the_ID(), 'dodatecne_parametry', true);
        if (!empty($dodatecne_parametry) && is_array($dodatecne_parametry)) {
            foreach ($dodatecne_parametry as $parametr) {
                if (!empty($parametr['nazev']) && !empty($parametr['hodnota'])) {
                    echo '<div class="bg-white p-4 rounded-lg shadow-sm">';
                    echo '<h4 class="text-sm uppercase ' . $category_color . ' font-semibold mb-1">' . esc_html($parametr['nazev']) . '</h4>';
                    echo '<p class="text-xl font-bold text-gray-800">' . esc_html($parametr['hodnota']) . '</p>';
                    echo '</div>';
                }
            }
        }
        ?>
    </div>
</div>
<?php endif; ?>
