<?php
/**
 * Template part for displaying post header in single.php
 *
 * @package dumabyt
 */

// Set default values
$category_badge = 'bg-amber-600';
$category_color = 'text-amber-600 hover:text-amber-700';

// Get main category
$categories = get_the_category();
if ($categories) {
    $category = $categories[0]; // Get the first category
    
    // Set color based on category slug - matching our category scheme from homepage
    if ($category->slug === 'interier') {
        $category_badge = 'bg-cyan-600';
        $category_color = 'text-cyan-600 hover:text-cyan-700';
    } elseif ($category->slug === 'stavba') {
        $category_badge = 'bg-emerald-600';
        $category_color = 'text-emerald-600 hover:text-emerald-700';
    } elseif ($category->slug === 'rekonstrukce') {
        $category_badge = 'bg-orange-600';
        $category_color = 'text-orange-600 hover:text-orange-700';
    } elseif ($category->slug === 'zahrada') {
        $category_badge = 'bg-teal-600';
        $category_color = 'text-teal-600 hover:text-teal-700';
    } elseif ($category->slug === 'blog') {
        $category_badge = 'bg-purple-600';
        $category_color = 'text-purple-600 hover:text-purple-700';
    }
}
?>

<header class="relative overflow-hidden">
    <?php if (_uw_theme_can_show_post_thumbnail()) : ?>
    <div class="post-thumbnail relative">
        <div class="overflow-hidden max-h-[600px]">
            <?php the_post_thumbnail('full', array('class' => 'w-full h-auto object-cover transition-transform duration-1000 hover:scale-105')); ?>
        </div>
        
        <!-- Enhanced overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent"></div>
        
        <!-- Category badge -->
        <?php if ($categories) : ?>
        <div class="absolute top-6 left-6 z-10">
            <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="inline-block <?php echo $category_badge; ?> text-white font-bold uppercase py-1.5 px-4 rounded-full text-xs tracking-wider shadow-md transform transition-transform hover:translate-y-px">
                <?php echo esc_html($category->name); ?>
            </a>
        </div>
        <?php endif; ?>
        
        <!-- Title overlayed on image -->
        <div class="absolute bottom-0 left-0 p-6 text-left text-white">
            <?php the_title('<h1 class="entry-title text-left text-2xl md:text-3xl lg:text-4xl font-bold mb-4 leading-tight font-trajan text-white shadow-text">', '</h1>'); ?>
            
            <!-- Lead paragraph/excerpt moved to post-content -->
            
            <div class="entry-meta flex flex-wrap items-center text-sm gap-4 pt-2">
                <!-- Author information removed -->
                
                <!-- Date -->
                <div class="flex items-center text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <time datetime="<?php echo esc_attr(get_the_date('c')); ?>"><?php echo esc_html(get_the_date()); ?></time>
                </div>
                
                <!-- Reading time estimate -->
                <div class="flex items-center text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <?php 
                    // Calculate reading time
                    $content = get_post_field('post_content', get_the_ID());
                    $word_count = str_word_count(strip_tags($content));
                    $reading_time = ceil($word_count / 200); // Assuming 200 words per minute
                    echo esc_html($reading_time . ' min. čtení');
                    ?>
                </div>
            </div>
        </div>
    </div>
    <?php else : ?>
    <!-- Fallback header without image - with gradient background matching category -->
    <div class="p-6 bg-gradient-to-br from-gray-800 to-gray-900">
        <!-- Category badge -->
        <?php if ($categories) : ?>
        <div class="mb-6">
            <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="inline-block <?php echo $category_badge; ?> text-white font-bold uppercase py-1.5 px-4 rounded-full text-xs tracking-wider shadow-md">
                <?php echo esc_html($category->name); ?>
            </a>
        </div>
        <?php endif; ?>
        
        <?php the_title('<h1 class="entry-title text-2xl md:text-3xl lg:text-4xl font-bold mb-4 text-white leading-tight font-trajan">', '</h1>'); ?>
        
        <!-- Lead paragraph/excerpt moved to post-content -->
        
        <div class="entry-meta flex flex-wrap items-center text-sm gap-4 text-white pt-2">
            <!-- Author information removed -->
            
            <!-- Date -->
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <time datetime="<?php echo esc_attr(get_the_date('c')); ?>"><?php echo esc_html(get_the_date()); ?></time>
            </div>
            
            <!-- Reading time estimate -->
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <?php 
                // Calculate reading time
                $content = get_post_field('post_content', get_the_ID());
                $word_count = str_word_count(strip_tags($content));
                $reading_time = ceil($word_count / 200); // Assuming 200 words per minute
                echo esc_html($reading_time . ' min. čtení');
                ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</header><!-- .entry-header -->
