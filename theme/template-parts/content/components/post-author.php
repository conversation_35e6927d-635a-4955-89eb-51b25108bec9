<?php
/**
 * Template part for displaying post author information in single.php
 *
 * @package dumabyt
 */
?>

<div class="author-bio px-6 py-6 mb-6 border-t border-gray-100">
    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        <div class="flex-shrink-0">
            <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>" class="block">
                <?php
                $avatar_size = 80;
                echo get_avatar(get_the_author_meta('ID'), $avatar_size, '', get_the_author(), array('class' => 'rounded-lg w-20 h-20 shadow-sm'));
                ?>
            </a>
        </div>
        <div>
            <span class="text-sm text-gray-500 uppercase font-medium">Autor</span>
            <h3 class="text-lg font-bold text-gray-800">
                <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>" class="hover:text-amber-600 transition-colors">
                    <?php echo esc_html(get_the_author()); ?>
                </a>
            </h3>
            <?php if (get_the_author_meta('description')) : ?>
                <p class="text-sm text-gray-600 mt-1"><?php echo esc_html(get_the_author_meta('description')); ?></p>
            <?php endif; ?>
        </div>
    </div>
</div>
