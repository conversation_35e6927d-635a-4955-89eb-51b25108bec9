<?php
/**
 * Template part for displaying post tags in single.php
 *
 * @package dumabyt
 */

// Get post tags
$tags = get_the_tags();

// Set default value for category color
$category_color = 'text-amber-600';

// Get main category to determine color scheme
$categories = get_the_category();
if ($categories) {
    $category = $categories[0]; // Get the first category
    
    // Set color based on category slug
    if ($category->slug === 'interier') {
        $category_color = 'text-cyan-600';
    } elseif ($category->slug === 'stavba') {
        $category_color = 'text-emerald-600';
    } elseif ($category->slug === 'rekonstrukce') {
        $category_color = 'text-orange-600';
    } elseif ($category->slug === 'zahrada') {
        $category_color = 'text-teal-600';
    } elseif ($category->slug === 'blog') {
        $category_color = 'text-purple-600';
    }
}

// Only display tags section if we have tags
if ($tags) :
?>
<div class="mb-4">
    <h4 class="text-sm uppercase text-gray-500 font-medium mb-2"><PERSON><PERSON><PERSON><PERSON>ky</h4>
    <div class="flex flex-wrap gap-2 mb-4">
        <?php foreach ($tags as $tag) : ?>
            <a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>" class="inline-flex items-center px-3 py-1.5 rounded-full bg-gray-100 text-sm text-gray-700 hover:bg-gray-200 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1 <?php echo $category_color; ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                <?php echo esc_html($tag->name); ?>
            </a>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>
