<?php
/**
 * Template part for displaying table of contents in single posts
 *
 * @package dumabyt
 */

// Get the current post content and calculate word count
$content = get_post_field('post_content', get_the_ID());
$word_count = str_word_count(strip_tags($content));

// Set default values
$category_color = 'text-amber-600 hover:text-amber-700';

// Get main category
$categories = get_the_category();
if ($categories) {
    $category = $categories[0]; // Get the first category
    
    // Set color based on category slug - matching our category scheme from homepage
    if ($category->slug === 'interier') {
        $category_color = 'text-cyan-600 hover:text-cyan-700';
    } elseif ($category->slug === 'stavba') {
        $category_color = 'text-emerald-600 hover:text-emerald-700';
    } elseif ($category->slug === 'rekonstrukce') {
        $category_color = 'text-orange-600 hover:text-orange-700';
    } elseif ($category->slug === 'zahrada') {
        $category_color = 'text-teal-600 hover:text-teal-700';
    } elseif ($category->slug === 'blog') {
        $category_color = 'text-purple-600 hover:text-purple-700';
    }
}

// Function to extract headings from content
if (!function_exists('extract_headings')) {
    function extract_headings($content) {
        $headings = array();
        
        // First try to find standard HTML headings
        $pattern = '/<h([2-3])[^>]*>(.*?)<\/h\1>/i';
        
        if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $level = intval($match[1]);
                $text = strip_tags($match[2]);
                $id = sanitize_title($text);
                
                $headings[] = array(
                    'level' => $level,
                    'text' => $text,
                    'id' => $id
                );
            }
        }
        
        // If no HTML headings found, try to detect migrated TOC format
        if (empty($headings) && strpos($content, "Obsah článku") !== false) {
            // Find text after "Obsah článku" that might be TOC items
            $lines = explode("\n", $content);
            $is_in_toc = false;
            $toc_items = [];
            
            foreach ($lines as $line) {
                $trimmed_line = trim($line);
                
                // Start collecting TOC items after "Obsah článku"
                if ($trimmed_line === "Obsah článku" || strpos($trimmed_line, "Obsah článku") !== false) {
                    $is_in_toc = true;
                    continue;
                }
                
                // If we're in the TOC section and line has content
                if ($is_in_toc && !empty($trimmed_line) && $trimmed_line !== '<p>' && $trimmed_line !== '</p>' && strlen(strip_tags($trimmed_line)) > 5) {
                    // Check if this looks like a TOC item
                    if (!preg_match('/<h[1-6]|<img|<table|<ul|<ol|<div|<section/i', $trimmed_line)) {
                        $clean_text = strip_tags($trimmed_line);
                        // Skip very short lines or obvious non-headings
                        if (strlen($clean_text) > 5 && !preg_match('/^[0-9\.]+$/', $clean_text)) {
                            // Add as a heading (assume all are h3 level)
                            $headings[] = array(
                                'level' => 3, // Consider all migrated items as H3
                                'text' => $clean_text,
                                'id' => sanitize_title($clean_text)
                            );
                        }
                    } else {
                        // If we hit structured content, we're likely out of the TOC area
                        $is_in_toc = false;
                    }
                }
                
                // If we find an H2/H3 tag, we're definitely out of the TOC area
                if ($is_in_toc && preg_match('/<h[2-3]/i', $trimmed_line)) {
                    $is_in_toc = false;
                }
            }
        }
        
        return $headings;
    }
}

// Extract headings, even from migrated content
$headings = extract_headings($content);

// Show TOC if we have headings OR if the article is long
if (!empty($headings) || $word_count > 800):
?>
<div class="px-6 pt-6">
    <div class="toc p-4 bg-gray-50 rounded-lg border border-gray-100 mb-6">
        <h2 class="text-lg font-bold text-gray-800 mb-2">Obsah článku</h2>
        <ul class="space-y-1 text-sm toc-items-list">
            <?php foreach ($headings as $heading) : ?>
            <li class="<?php echo $heading['level'] == 3 ? 'ml-4' : ''; ?>">
                <a href="#<?php echo esc_attr($heading['id']); ?>" 
                   class="<?php echo $category_color; ?> hover:underline flex items-center toc-link"
                   data-toc-text="<?php echo esc_attr($heading['text']); ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                    <?php echo esc_html($heading['text']); ?>
                </a>
            </li>
            <?php endforeach; ?>
        </ul>
    </div>
</div>
<?php endif; ?>
