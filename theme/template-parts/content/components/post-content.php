<?php
/**
 * Template part for displaying post content in single.php
 *
 * @package dumabyt
 */

// Get the content and process it for headings
$content = get_the_content();

// Add IDs to headings for TOC linking
$pattern = '/<h([2-3])>(.*?)<\/h\1>/i';
$replacement = '<h$1 id="' . sanitize_title('$2') . '">$2</h$1>';
$content_with_ids = preg_replace($pattern, $replacement, $content);

// Process content further for plain text TOC (typical in migrated content)
// Look for "Obsah článku" followed by lines of questions or sections
$toc_identifier = "Obsah č<PERSON>ánku";
if (strpos($content_with_ids, $toc_identifier) !== false) {
    // Find text after "Obsah článku" that might be TOC items
    $lines = explode("\n", $content_with_ids);
    $is_in_toc = false;
    $toc_items = [];
    
    // First pass: collect TOC items
    foreach ($lines as $i => $line) {
        $trimmed_line = trim($line);
        
        // Check if this line is "Obsah článku"
        if ($trimmed_line === $toc_identifier || strpos($trimmed_line, $toc_identifier) !== false) {
            $is_in_toc = true;
            continue;
        }
        
        // If we're in the TOC section and line has content (not just whitespace or HTML tag)
        if ($is_in_toc && !empty($trimmed_line) && $trimmed_line !== '<p>' && $trimmed_line !== '</p>' && strlen(strip_tags($trimmed_line)) > 5) {
            // Check if this looks like a TOC item (not part of regular content)
            if (!preg_match('/<h[1-6]|<img|<table|<ul|<ol|<div|<section/i', $trimmed_line)) {
                $clean_text = strip_tags($trimmed_line);
                // Skip very short lines or obvious non-headings
                if (strlen($clean_text) > 5 && !preg_match('/^[0-9\.]+$/', $clean_text)) {
                    $toc_items[] = $clean_text;
                }
            } else {
                // If we hit structured content, we're likely out of the TOC area
                $is_in_toc = false;
            }
        }
        
        // If we find an H2/H3 tag, we're definitely out of the TOC area
        if ($is_in_toc && preg_match('/<h[2-3]/i', $trimmed_line)) {
            $is_in_toc = false;
        }
    }
    
    // Second pass: add IDs to content where TOC items are mentioned
    foreach ($toc_items as $item) {
        $item_clean = trim(strip_tags($item));
        if (strlen($item_clean) < 5) continue; // Skip very short items
        
        $item_id = sanitize_title($item_clean);
        
        // Search for exact phrase match and wrap it in a heading with ID
        $content_with_ids = preg_replace(
            '/(<p>|^|\n)(' . preg_quote($item_clean, '/') . ')(\?|\.|:|$|<\/p>)/m',
            '$1<h3 id="' . $item_id . '">$2$3</h3>',
            $content_with_ids,
            1 // Replace only the first occurrence
        );
    }
}

// Output the modified content
?>
<div class="entry-content px-6 py-8 article-content" style="max-width: none; width: 100%; color: #374151;">
    <?php echo apply_filters('the_content', $content_with_ids); ?>

    <?php
    wp_link_pages(
        array(
            'before' => '<div class="page-links my-6 p-4 bg-gray-50 rounded-lg text-sm border border-gray-100">' . __('Stránky:', '_uw-theme'),
            'after'  => '</div>',
        )
    );
    ?>
</div><!-- .entry-content -->

<script>
// Script to enhance TOC functionality for migrated content
document.addEventListener('DOMContentLoaded', function() {
    // Find TOC container if it exists
    const tocContainer = document.querySelector('.toc');
    if (!tocContainer) return;
    
    // Get all links in the TOC
    const tocLinks = tocContainer.querySelectorAll('a');
    
    // Process each link
    tocLinks.forEach(link => {
        const targetId = link.getAttribute('href').substring(1); // Remove # from href
        const targetElement = document.getElementById(targetId);
        
        // If target doesn't exist, try to find content that matches the link text
        if (!targetElement) {
            const linkText = link.textContent.trim();
            
            // Search all paragraphs for content matching the link text
            const paragraphs = document.querySelectorAll('.entry-content p');
            
            paragraphs.forEach(paragraph => {
                // If paragraph contains the exact text
                if (paragraph.textContent.includes(linkText)) {
                    // Create an ID for this paragraph
                    const newId = targetId || 'toc-' + linkText.toLowerCase().replace(/[^a-z0-9]+/g, '-');
                    paragraph.id = newId;
                    
                    // Update the link href
                    link.href = '#' + newId;
                }
            });
        }
    });
    
    // Add smooth scrolling for all TOC links
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                // Scroll smoothly to the element
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Highlight the element briefly
                targetElement.classList.add('highlight-target');
                setTimeout(() => {
                    targetElement.classList.remove('highlight-target');
                }, 2000);
            }
        });
    });
});
</script>

<style>
/* Animation for highlighting the target section */
@keyframes highlightFade {
    0%   { background-color: rgba(251, 191, 36, 0.2); }
    100% { background-color: transparent; }
}

.highlight-target {
    animation: highlightFade 2s ease-out;
}
</style>

<script>
// Script to ensure article content is full width, images are centered, and text color is correct
document.addEventListener('DOMContentLoaded', function() {
    // Target article content
    const articleContent = document.querySelector('.entry-content');
    if (articleContent) {
        articleContent.style.maxWidth = 'none';
        articleContent.style.width = '100%';
        articleContent.style.color = '#374151';
        
        // Also target any content containers inside
        const contentContainers = articleContent.querySelectorAll('p, div, ul, ol, blockquote, figure');
        contentContainers.forEach(container => {
            container.style.maxWidth = 'none';
            container.style.width = '100%';
            container.style.color = '#374151';
        });
        
        // Explicitly set text color for all text elements
        const textElements = articleContent.querySelectorAll('p, span, h1, h2, h3, h4, h5, h6, li, blockquote');
        textElements.forEach(element => {
            element.style.color = '#374151';
        });
        
        // Remove prose class if it exists
        if (articleContent.classList.contains('prose')) {
            articleContent.classList.remove('prose');
        }
        
        // Apply to wp-block elements which may have max-width constraints
        const wpBlocks = articleContent.querySelectorAll('.wp-block, .wp-block-group');
        wpBlocks.forEach(block => {
            block.style.maxWidth = 'none';
            block.style.width = '100%';
            block.style.color = '#374151';
        });
        
        // Center all images in the content
        const images = articleContent.querySelectorAll('img, figure, .wp-block-image');
        images.forEach(img => {
            img.style.display = 'block';
            img.style.marginLeft = 'auto';
            img.style.marginRight = 'auto';
            img.style.maxWidth = '800px';
            
            // If image is inside a figure, center the caption too
            const parentFigure = img.closest('figure');
            if (parentFigure) {
                const figcaption = parentFigure.querySelector('figcaption');
                if (figcaption) {
                    figcaption.style.textAlign = 'center';
                }
            }
        });
    }
});
</script>
