<?php
/**
 * Template part for displaying posts in a card layout for category archives
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package dumabyt
 */

// Get passed variables
$category_name = isset($args['category_name']) ? $args['category_name'] : '';
$category_url = isset($args['category_url']) ? $args['category_url'] : '';
$color_class = isset($args['color_class']) ? $args['color_class'] : 'bg-blue-600';
$text_color = isset($args['text_color']) ? $args['text_color'] : 'text-blue-600';
$text_hover_color = isset($args['text_hover_color']) ? $args['text_hover_color'] : 'hover:text-blue-700';
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('bg-white rounded-xl overflow-hidden card-elevation h-full flex flex-col'); ?>>
    <!-- Featured Image -->
    <div class="relative h-48 overflow-hidden">
        <?php if (has_post_thumbnail()) : ?>
            <a href="<?php the_permalink(); ?>" class="block h-full">
                <?php the_post_thumbnail('medium_large', array('class' => 'w-full h-full object-cover transition-transform duration-300 hover:scale-105')); ?>
            </a>
        <?php else : ?>
            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
        <?php endif; ?>
        
        <!-- Category badge -->
        <a href="<?php echo esc_url($category_url); ?>" class="absolute top-3 left-3 z-10">
            <span class="<?php echo esc_attr($color_class); ?> text-white font-bold uppercase py-1 px-3 rounded-full text-xs tracking-wider badge-shadow"><?php echo esc_html($category_name); ?></span>
        </a>
    </div>
    
    <!-- Content -->
    <div class="p-4 flex-grow flex flex-col">
        <h2 class="text-lg font-bold mb-2 font-trajan line-clamp-2">
            <a href="<?php the_permalink(); ?>" class="text-gray-800 hover:<?php echo $text_color; ?> transition-colors"><?php the_title(); ?></a>
        </h2>
        
        <div class="text-gray-600 text-sm mb-4 line-clamp-3 flex-grow">
            <?php 
            // Display excerpt or generate one from content
            if (has_excerpt()) {
                the_excerpt();
            } else {
                echo wp_trim_words(get_the_content(), 20, '...');
            }
            ?>
        </div>
        
        <div class="mt-auto">
            <div class="flex items-center text-xs text-gray-500 mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <time datetime="<?php echo esc_attr(get_the_date('c')); ?>"><?php echo esc_html(get_the_date()); ?></time>
            </div>
            
            <a href="<?php the_permalink(); ?>" class="inline-flex items-center text-sm <?php echo $text_color; ?> <?php echo $text_hover_color; ?> font-medium">
                Číst více
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
            </a>
        </div>
    </div>
</article>
