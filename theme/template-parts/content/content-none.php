<?php
/**
 * Template part for displaying a message when posts are not found
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package dumabyt
 */

?>

<section class="bg-white rounded-xl p-8 text-center">
    <?php if (is_search()) : ?>
        <h2 class="text-xl font-bold mb-4 text-gray-800"><PERSON><PERSON><PERSON><PERSON> v<PERSON></h2>
        <p class="text-gray-600 mb-6">Pro hledaný výraz "<?php echo esc_html(get_search_query()); ?>" nebyly nalezeny žádné výsledky.</p>

        <div class="max-w-md mx-auto">
            <h3 class="text-lg font-medium mb-3 text-gray-700">Zkuste prosím:</h3>
            <ul class="text-left list-disc pl-6 mb-6 text-gray-600">
                <li>Zkontrolovat pravopis zadaných slov</li>
                <li><PERSON><PERSON><PERSON><PERSON><PERSON> ji<PERSON> kl<PERSON><PERSON> slova</li>
                <li><PERSON><PERSON>ž<PERSON>t obecnější výrazy</li>
                <li>Snížit počet slov v dotazu</li>
            </ul>

            <div class="mt-6 max-w-xl">
                <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                    <div class="relative">
                        <input type="search" class="search-field w-full py-3 pl-4 pr-12 rounded-full bg-gray-50 border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 focus:outline-none transition-all duration-300 text-gray-800" placeholder="<?php echo esc_attr_x('Hledat znovu...', 'placeholder', '_uw-theme'); ?>" value="<?php echo esc_attr(get_search_query()); ?>" name="s" />
                        <button type="submit" class="search-submit absolute right-3 top-1/2 transform -translate-y-1/2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <span class="sr-only"><?php echo _x('Search', 'submit button', '_uw-theme'); ?></span>
                        </button>
                    </div>
                </form>
            </div>

            <a href="<?php echo esc_url(home_url('/')); ?>" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium mt-6">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Zpět na úvodní stránku
            </a>
        </div>
    <?php elseif (is_home() && current_user_can('publish_posts')) : ?>
        <h2 class="text-xl font-bold mb-4 text-gray-800">Žádné příspěvky</h2>
        <p class="text-gray-600 mb-6">
            <?php esc_html_e('Váš web je nastaven tak, aby zobrazoval nejnovější příspěvky na domovské stránce, ale zatím jste nezveřejnili žádné příspěvky.', '_uw-theme'); ?>
        </p>
        <a href="<?php echo esc_url(admin_url('edit.php')); ?>" class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            <?php esc_html_e('Přidat nebo publikovat příspěvky', '_uw-theme'); ?>
        </a>
    <?php else : ?>
        <h2 class="text-xl font-bold mb-4 text-gray-800">Nic nenalezeno</h2>
        <p class="text-gray-600 mb-6">
            <?php esc_html_e('Vašemu požadavku neodpovídá žádný obsah.', '_uw-theme'); ?>
        </p>

        <div class="mt-6 max-w-xl mx-auto">
            <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                <div class="relative">
                    <input type="search" class="search-field w-full py-3 pl-4 pr-12 rounded-full bg-gray-50 border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 focus:outline-none transition-all duration-300 text-gray-800" placeholder="<?php echo esc_attr_x('Zkuste vyhledat...', 'placeholder', '_uw-theme'); ?>" value="" name="s" />
                    <button type="submit" class="search-submit absolute right-3 top-1/2 transform -translate-y-1/2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <span class="sr-only"><?php echo _x('Search', 'submit button', '_uw-theme'); ?></span>
                    </button>
                </div>
            </form>
        </div>

        <a href="<?php echo esc_url(home_url('/')); ?>" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium mt-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Zpět na úvodní stránku
        </a>
    <?php endif; ?>
</section>
