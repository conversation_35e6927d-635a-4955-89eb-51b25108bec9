<?php
/**
 * Template part for displaying pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package dumabyt
 */

?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
    <?php if (has_post_thumbnail()) : ?>
        <div class="relative">
            <?php the_post_thumbnail('full', array('class' => 'w-full h-auto')); ?>
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent/20 flex items-end">
                <div class="p-6 text-white w-full">
                    <?php
                    if (!is_front_page()) {
                        the_title('<h1 class="text-3xl md:text-4xl font-bold font-trajan">', '</h1>');
                    } else {
                        the_title('<h2 class="text-3xl md:text-4xl font-bold font-trajan">', '</h2>');
                    }
                    ?>
                </div>
            </div>
        </div>
    <?php else : ?>
        <header class="entry-header p-6 border-b border-gray-100">
            <?php
            if (!is_front_page()) {
                the_title('<h1 class="text-3xl font-bold text-gray-800 font-trajan">', '</h1>');
            } else {
                the_title('<h2 class="text-3xl font-bold text-gray-800 font-trajan">', '</h2>');
            }
            ?>
        </header><!-- .entry-header -->
    <?php endif; ?>

    <div class="entry-content px-6 py-8 prose prose-lg max-w-none">
        <?php
        the_content();

        wp_link_pages(
            array(
                'before' => '<div class="page-links my-6 p-4 bg-gray-50 rounded-lg text-sm border border-gray-100">' . __('Stránky:', '_uw-theme'),
                'after'  => '</div>',
            )
        );
        ?>
    </div><!-- .entry-content -->

    <?php if (get_edit_post_link()) : ?>
        <footer class="entry-footer px-6 pb-6 border-t border-gray-100 pt-4 text-sm text-gray-500">
            <?php
            edit_post_link(
                sprintf(
                    wp_kses(
                        /* translators: %s: Name of current post. Only visible to screen readers. */
                        __('Upravit <span class="sr-only">%s</span>', '_uw-theme'),
                        array(
                            'span' => array(
                                'class' => array(),
                            ),
                        )
                    ),
                    get_the_title()
                ),
                '<span class="inline-flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /></svg>',
                '</span>'
            );
            ?>
        </footer><!-- .entry-footer -->
    <?php endif; ?>

</article><!-- #post-<?php the_ID(); ?> -->
