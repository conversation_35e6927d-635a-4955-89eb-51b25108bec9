<?php
/**
 * Template part for displaying featured post in category archives
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package dumabyt
 */

// Get passed variables
$category_name = isset($args['category_name']) ? $args['category_name'] : '';
$category_url = isset($args['category_url']) ? $args['category_url'] : '';
$color_class = isset($args['color_class']) ? $args['color_class'] : 'bg-blue-600';
$text_color = isset($args['text_color']) ? $args['text_color'] : 'text-blue-600';
$text_hover_color = isset($args['text_hover_color']) ? $args['text_hover_color'] : 'hover:text-blue-700';
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('bg-white rounded-xl overflow-hidden card-elevation'); ?>>
    <div class="grid grid-cols-1 md:grid-cols-2">
        <!-- Featured Image -->
        <div class="relative h-64 md:h-auto overflow-hidden">
            <?php if (has_post_thumbnail()) : ?>
                <a href="<?php the_permalink(); ?>" class="block h-full">
                    <?php the_post_thumbnail('large', array('class' => 'w-full h-full object-cover transition-transform duration-300 hover:scale-105')); ?>
                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent/20 flex items-end md:hidden">
                        <div class="p-4 text-white">
                            <h2 class="text-xl font-bold"><?php the_title(); ?></h2>
                        </div>
                    </div>
                </a>
            <?php else : ?>
                <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
            <?php endif; ?>
            
            <!-- Category badge -->
            <a href="<?php echo esc_url($category_url); ?>" class="absolute top-4 left-4 z-10">
                <span class="<?php echo esc_attr($color_class); ?> text-white font-bold uppercase py-1.5 px-4 rounded-full text-xs tracking-wider badge-shadow"><?php echo esc_html($category_name); ?></span>
            </a>
        </div>
        
        <!-- Content -->
        <div class="p-6 flex flex-col justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-3 font-trajan">
                    <a href="<?php the_permalink(); ?>" class="text-gray-800 hover:<?php echo $text_color; ?> transition-colors"><?php the_title(); ?></a>
                </h2>
                
                <div class="text-gray-600 mb-4">
                    <?php 
                    // Display excerpt or generate one from content
                    if (has_excerpt()) {
                        the_excerpt();
                    } else {
                        echo wp_trim_words(get_the_content(), 30, '...');
                    }
                    ?>
                </div>
            </div>
            
            <div class="mt-4">
                <div class="flex items-center text-sm text-gray-500 mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <time datetime="<?php echo esc_attr(get_the_date('c')); ?>"><?php echo esc_html(get_the_date()); ?></time>
                </div>
                
                <a href="<?php the_permalink(); ?>" class="inline-flex items-center <?php echo $text_color; ?> <?php echo $text_hover_color; ?> font-medium">
                    Číst více
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
</article>
