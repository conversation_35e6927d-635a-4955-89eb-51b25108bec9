<?php
/**
 * Template part for displaying single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package dumabyt
 */

// Variables shared across components
$categories = get_the_category();
$category_color = 'text-amber-600 hover:text-amber-700';
$category_bg = 'bg-amber-100';
$category_badge = 'bg-amber-600';

if ($categories) {
    $category = $categories[0]; // Get the first category
    // Set color based on category slug - matching our category scheme from homepage
    if ($category->slug === 'interier') {
        $category_color = 'text-cyan-600 hover:text-cyan-700';
        $category_bg = 'bg-cyan-100';
        $category_badge = 'bg-cyan-600';
    } elseif ($category->slug === 'stavba') {
        $category_color = 'text-emerald-600 hover:text-emerald-700';
        $category_bg = 'bg-emerald-100';
        $category_badge = 'bg-emerald-600';
    } elseif ($category->slug === 'rekonstrukce') {
        $category_color = 'text-orange-600 hover:text-orange-700';
        $category_bg = 'bg-orange-100';
        $category_badge = 'bg-orange-600';
    } elseif ($category->slug === 'zahrada') {
        $category_color = 'text-teal-600 hover:text-teal-700';
        $category_bg = 'bg-teal-100';
        $category_badge = 'bg-teal-600';
    } elseif ($category->slug === 'blog') {
        $category_color = 'text-purple-600 hover:text-purple-700';
        $category_bg = 'bg-purple-100';
        $category_badge = 'bg-purple-600';
    }
}
?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
    <!-- Post Header -->
    <?php get_template_part('template-parts/content/components/post-header'); ?>

    <!-- Lead paragraph/excerpt if available -->
    <?php if (has_excerpt()) : ?>
    <div class="px-6 pt-6 pb-2">
        <div class="text-lg font-medium text-gray-700 lead-paragraph">
            <?php the_excerpt(); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Gallery - moved up right after the excerpt -->
    <?php get_template_part('template-parts/content/components/post-gallery'); ?>

    <!-- Table of contents for long articles -->
    <?php get_template_part('template-parts/content/components/post-toc'); ?>

    <!-- Post Content -->
    <?php get_template_part('template-parts/content/components/post-content'); ?>

    <!-- Technical Specifications -->
    <?php get_template_part('template-parts/content/components/post-specs'); ?>

    <!-- Author info removed as requested -->

    <!-- Post Footer -->
    <footer class="entry-footer px-6 pb-6 border-t border-gray-100 pt-4">
        <!-- Tags -->
        <?php get_template_part('template-parts/content/components/post-tags'); ?>
        
        <!-- Social sharing buttons -->
        <?php get_template_part('template-parts/content/components/post-social'); ?>
    </footer><!-- .entry-footer -->
</article><!-- #post-${ID} -->
