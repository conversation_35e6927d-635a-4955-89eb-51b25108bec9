<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default. Please note that
 * this is the WordPress construct of pages: specifically, posts with a post
 * type of `page`.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package dumabyt
 */

get_header();
?>

<main id="primary" class="site-main bg-gray-50 py-8">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Main content - 3 columns wide -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-xl overflow-hidden card-elevation">
                    <?php
                    /* Start the Loop */
                    while (have_posts()) :
                        the_post();

                        get_template_part('template-parts/content/content', 'page');

                        // If comments are open, or we have at least one comment, load
                        // the comment template.
                        if (comments_open() || get_comments_number()) {
                            comments_template();
                        }

                    endwhile; // End of the loop.
                    ?>
                </div>
            </div>

            <!-- Sidebar - 1 column wide -->
            <?php get_template_part('template-parts/layout/sidebar', 'partneri'); ?>
        </div>
    </div>
</main>

<?php
get_footer();
