/**
 * TOC Enhancer - Advanced script for improving table of contents functionality
 * Especially helpful for migrated content from older systems
 */

// Wait for jQuery and make sure we're in the global scope
(function($) {
    // Initialize when document is ready (jQuery way to ensure DOM is loaded)
    $(document).ready(function() {
        enhanceTOC();
    });

    // Store all potential headings here for lookup
    const potentialHeadings = [];

    /**
     * Main function to enhance TOC functionality
     */
    function enhanceTOC() {
        // Find the TOC container
        const tocContainer = $('.toc');
        if (tocContainer.length === 0) {
            return; // No TOC found, exit
        }

        // First identify content headings/questions
        identifyContentHeadings();

        // Then update TOC links to point to the headings we found
        updateTOCLinks();
        
        // Add smooth scrolling and highlighting
        addScrollBehavior();
    }

    /**
     * Identify all potential headings in the content and assign IDs
     */
    function identifyContentHeadings() {
        // Find the main content area
        const $contentArea = $('.entry-content');
        if ($contentArea.length === 0) {
            return;
        }
        
        // First identify any existing headings (h2, h3)
        $contentArea.find('h2, h3').each(function() {
            const $heading = $(this);
            const text = $heading.text().trim();
            const id = $heading.attr('id') || sanitizeId(text);
            
            // If no ID, assign one
            if (!$heading.attr('id')) {
                $heading.attr('id', id);
            }
            
            // Add to our collection of headings
            potentialHeadings.push({
                element: $heading,
                text: text,
                id: $heading.attr('id'),
                level: parseInt($heading.prop('tagName').substring(1))
            });
        });
        
        // Then look for paragraphs that might be questions or section headers
        $contentArea.find('p').each(function() {
            const $paragraph = $(this);
            const text = $paragraph.text().trim();
            
            // Skip short texts
            if (text.length < 5) return;
            
            // Look for question format or title-like format
            if (text.endsWith('?') || 
                text.endsWith('...') ||
                text.endsWith('…') ||
                /^[A-Z].*[\.:]$/.test(text) || 
                (text.length < 100 && /^[A-Z]/.test(text))) {
                
                // Create an ID for this heading
                const id = 'heading-' + sanitizeId(text);
                
                // Assign ID to paragraph
                $paragraph.attr('id', id);
                $paragraph.addClass('auto-heading');
                
                // Add to our collection
                potentialHeadings.push({
                    element: $paragraph,
                    text: text,
                    id: id,
                    level: 3 // Treat as h3 level
                });
            }
        });
        
        // Now check for potential questions directly in the TOC
        const tocItems = [];
        $('.toc a').each(function() {
            const linkText = $(this).text().trim();
            tocItems.push(linkText);
        });
        
        // Look for paragraphs containing these questions
        if (tocItems.length > 0) {
            $contentArea.find('p').each(function() {
                const $paragraph = $(this);
                // Skip if already processed
                if ($paragraph.hasClass('auto-heading')) return;
                
                const paragraphText = $paragraph.text().trim();
                
                // Look for TOC items in paragraph text
                for (let i = 0; i < tocItems.length; i++) {
                    const tocItem = tocItems[i];
                    if (paragraphText.includes(tocItem)) {
                        // Found a paragraph containing the TOC question!
                        const id = 'toc-item-' + sanitizeId(tocItem);
                        $paragraph.attr('id', id);
                        $paragraph.addClass('toc-target');
                        
                        // Add to potential headings
                        potentialHeadings.push({
                            element: $paragraph,
                            text: tocItem,
                            id: id,
                            level: 3,
                            tocItem: true
                        });
                        break;
                    }
                }
            });
        }
    }

    /**
     * Update TOC links to point to the headings we found
     */
    function updateTOCLinks() {
        $('.toc a').each(function(index) {
            const $link = $(this);
            const linkText = $link.text().trim();
            
            // Try different matching strategies
            
            // 1. First try to find a direct match by text
            let bestMatch = null;
            
            for (let i = 0; i < potentialHeadings.length; i++) {
                const heading = potentialHeadings[i];
                
                // Exact match to heading text
                if (heading.text === linkText) {
                    bestMatch = heading;
                    break;
                }
                
                // Exact match to heading text without punctuation
                if (removePunctuation(heading.text) === removePunctuation(linkText)) {
                    bestMatch = heading;
                    break;
                }
                
                // Check if heading contains the link text
                if (heading.text.includes(linkText)) {
                    bestMatch = heading;
                    break;
                }
                
                // Check if link text contains the heading
                if (linkText.includes(heading.text)) {
                    bestMatch = heading;
                    break;
                }
            }
            
            // 2. If no direct match, look for "question and answer" pattern
            if (!bestMatch) {
                // For each link, assign a heading based on position
                // This is a fallback strategy for heavily migrated content
                if (index < potentialHeadings.length) {
                    bestMatch = potentialHeadings[index];
                }
            }
            
            // If we found a match, update the link
            if (bestMatch) {
                $link.attr('href', '#' + bestMatch.id);
                
                // Highlight the heading
                bestMatch.element.addClass('toc-target');
            } else {
                // Create a fallback ID based on the link text
                const fallbackId = 'toc-' + sanitizeId(linkText);
                $link.attr('href', '#' + fallbackId);
                
                // Find any paragraph containing this exact text and give it the ID
                $('.entry-content p').each(function() {
                    const $paragraph = $(this);
                    if ($paragraph.text().includes(linkText)) {
                        $paragraph.attr('id', fallbackId);
                        $paragraph.addClass('toc-target');
                        return false; // break each loop
                    }
                });
            }
        });
    }
    
    /**
     * Add smooth scrolling and highlighting to TOC links
     */
    function addScrollBehavior() {
        // Use jQuery for better compatibility
        $('.toc a').on('click', function(e) {
            e.preventDefault();
            const $link = $(this);
            
            const targetId = $link.attr('href').substring(1);
            const $targetElement = $('#' + targetId);
            
            if ($targetElement.length) {
                // Scroll to element with offset for header
                $('html, body').animate({
                    scrollTop: $targetElement.offset().top - 50
                }, 800, function() {
                    // After scrolling, flash the element
                    $targetElement.addClass('highlight-target');
                    setTimeout(function() {
                        $targetElement.removeClass('highlight-target');
                    }, 2000);
                });
            }
        });
    }
    
    /**
     * Helper function to sanitize text into an ID
     */
    function sanitizeId(text) {
        return text.toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove punctuation
            .replace(/\s+/g, '-')     // Replace spaces with hyphens
            .replace(/--+/g, '-')     // Replace multiple hyphens with single hyphen
            .substring(0, 40);        // Limit length
    }
    
    /**
     * Helper function to remove punctuation for better matching
     */
    function removePunctuation(text) {
        return text.replace(/[^\w\s]/g, '').toLowerCase().trim();
    }

})(jQuery); // End of jQuery IIFE
