/**
 * Enhanced menu functionality for DŮM & BYT
 *
 * Features:
 * - Improved submenu handling
 * - Mobile menu with animations
 * - Accessibility improvements
 */

/* global jQuery, document, window, setTimeout */

jQuery(document).ready(function($) {
    // Variables
    const mobileMenuToggle = $('#mobile-menu-toggle')[0];
    const mobileMenu = $('#mobile-menu')[0];
    const siteHeader = $('#masthead')[0];
    const desktopMenuItems = $('#primary-menu .menu-item-has-children');

    // Mobile menu toggle handled by script.js to avoid conflicts

    // This section is now handled in the mobile menu toggle code

    // Desktop submenu handling
    desktopMenuItems.each(function() {
        const item = $(this);
        // Add indicator for items with submenu
        const itemLink = item.find('> a');
        const indicator = $('<span class="submenu-indicator"><svg xmlns="http://www.w3.org/2000/svg" width="10" height="6" viewBox="0 0 10 6" fill="none"><path d="M1 1L5 5L9 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg></span>');
        itemLink.append(indicator);

        // Handle mouse interactions
        item.on('mouseenter', function(e) {
            e.stopPropagation(); // Prevent event bubbling
            // First close all submenus before opening this one
            closeAllSubmenus(desktopMenuItems);
            openSubmenu(item);
        });

        // Handle focus events for keyboard navigation
        itemLink.on('focus', function() {
            closeAllSubmenus(desktopMenuItems);
            openSubmenu(item);
        });

        // Create a submenu container for better positioning
        const submenu = item.find('.sub-menu');
        if (submenu.length) {
            const submenuWrapper = $('<div class="submenu-wrapper"></div>');
            item.append(submenuWrapper);
            submenuWrapper.append(submenu);

            // Add event listener to keep submenu open when mouse is over it
            submenuWrapper.on('mouseenter', function(e) {
                e.stopPropagation(); // Prevent event bubbling
                // Keep the submenu open, but make sure all others are closed
                openSubmenu(item);
            });

            submenuWrapper.on('mouseleave', function() {
                closeSubmenu(item);
            });
        }

        // Close submenu when mouse leaves the menu item
        item.on('mouseleave', function() {
            // Use setTimeout to allow moving to the submenu
            setTimeout(function() {
                // Only close if the mouse isn't over the submenu or the item
                if (!item.is(':hover') && !item.find('.submenu-wrapper').is(':hover')) {
                    closeSubmenu(item);
                }
            }, 50);
        });
    });

    // Mobile submenu handling moved to script.js to avoid conflicts

    // Helper functions
    function openSubmenu(item) {
        item.addClass('submenu-active');
        const submenuWrapper = item.find('.submenu-wrapper');
        if (submenuWrapper.length) {
            submenuWrapper.addClass('active');
        }
    }

    function closeSubmenu(item) {
        item.removeClass('submenu-active');
        const submenuWrapper = item.find('.submenu-wrapper');
        if (submenuWrapper.length) {
            submenuWrapper.removeClass('active');
        }
    }

    function closeAllSubmenus(items) {
        items.each(function() {
            closeSubmenu($(this));
        });
    }

    // Handle keyboard navigation
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close all submenus when Escape is pressed
            closeAllSubmenus(desktopMenuItems);

            // Close mobile menu if open
            if ($(mobileMenu).hasClass('active')) {
                $(mobileMenuToggle).trigger('click');
            }
        }
    });

    // Set header height as CSS variable for smooth transitions
    function setHeaderHeight() {
        if (siteHeader) {
            const headerHeight = $(siteHeader).outerHeight();
            document.documentElement.style.setProperty('--header-height', headerHeight + 'px');
            return headerHeight;
        }
        return 0;
    }
    
    // Initialize header height
    setHeaderHeight();
    
    // Update on resize
    $(window).on('resize', function() {
        setHeaderHeight();
    });
    
    // Variable for smooth scroll handling
    let ticking = false;
    
    // Improved scroll handling with requestAnimationFrame for smoother performance
    $(window).on('scroll', function() {
        const scrollTop = $(window).scrollTop();
        
        if (!ticking) {
            window.requestAnimationFrame(function() {
                handleScroll(scrollTop);
                ticking = false;
            });
            ticking = true;
        }
    });
    
    // Set navigation height as CSS variable for smooth transitions
    function setNavHeight() {
        const mainNav = $('#site-navigation');
        if (mainNav.length) {
            const navHeight = mainNav.outerHeight();
            document.documentElement.style.setProperty('--nav-height', navHeight + 'px');
            return navHeight;
        }
        return 0;
    }
    
    // Initialize navigation height
    setNavHeight();
    
    function handleScroll(scrollTop) {
        // We need both the header height and nav height
        setHeaderHeight();
        setNavHeight();
        
        const scrollThreshold = 100; // Increased threshold to avoid premature activation
        
        // Add sticky class when scrolled past threshold
        if (scrollTop > scrollThreshold) {
            if (!$('body').hasClass('has-sticky-header')) {
                $('body').addClass('has-sticky-header');
            }
            
            if (!$(siteHeader).hasClass('sticky-header')) {
                $(siteHeader).addClass('sticky-header');
            }
        } else {
            // Remove sticky when back at the top
            $(siteHeader).removeClass('sticky-header');
            $('body').removeClass('has-sticky-header');
        }
    }
    
    // Check initial scroll position on page load
    $(window).on('load', function() {
        // Small delay to ensure all resources are loaded
        setTimeout(function() {
            const scrollTop = $(window).scrollTop();
            handleScroll(scrollTop);
        }, 10);
    });
    
    // Also check immediately
    setTimeout(function() {
        const scrollTop = $(window).scrollTop();
        handleScroll(scrollTop);
    }, 10);
});
