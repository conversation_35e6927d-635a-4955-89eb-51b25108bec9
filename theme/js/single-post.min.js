(() => {
  // javascript/single-post.js
  document.addEventListener("DOMContentLoaded", function() {
    const progressBar = document.getElementById("reading-progress");
    if (progressBar) {
      window.addEventListener("scroll", function() {
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight - windowHeight;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollPercent = scrollTop / documentHeight * 100;
        progressBar.style.width = scrollPercent + "%";
        if (scrollTop > 50) {
          progressBar.parentElement.style.opacity = "1";
        } else {
          progressBar.parentElement.style.opacity = "0";
        }
      });
    }
    const tocLinks = document.querySelectorAll('.toc a[href^="#"]');
    tocLinks.forEach((link) => {
      link.addEventListener("click", function(e) {
        e.preventDefault();
        const targetId = this.getAttribute("href").substring(1);
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          window.scrollTo({
            top: targetElement.offsetTop - 20,
            behavior: "smooth"
          });
          targetElement.classList.add("highlight-target");
          setTimeout(() => {
            targetElement.classList.remove("highlight-target");
          }, 2e3);
        }
      });
    });
    const galleryElements = document.querySelectorAll(".gallery-item a");
    if (galleryElements.length > 0 && typeof window.SimpleLightbox !== "undefined") {
      new SimpleLightbox(".gallery-item a", {
        captionPosition: "bottom",
        captionsData: "alt",
        enableKeyboard: true,
        showCounter: true,
        loop: true,
        animationSpeed: 300,
        swipeClose: true,
        navText: ["&larr;", "&rarr;"],
        closeText: "&times;",
        widthRatio: 0.9,
        heightRatio: 0.9,
        alertError: false,
        alertErrorMessage: ""
      });
    }
    const galleryContainer = document.querySelector(".slideshow-container");
    if (galleryContainer) {
      const slidesWrapper = galleryContainer.querySelector(".flex");
      const prevButton = galleryContainer.querySelector(".slider-prev");
      const nextButton = galleryContainer.querySelector(".slider-next");
      if (slidesWrapper && prevButton && nextButton) {
        let handleSwipe2 = function() {
          if (touchEndX < touchStartX) {
            slidesWrapper.scrollLeft += scrollAmount;
          }
          if (touchEndX > touchStartX) {
            slidesWrapper.scrollLeft -= scrollAmount;
          }
        };
        var handleSwipe = handleSwipe2;
        const scrollAmount = 162;
        prevButton.classList.add("opacity-100");
        nextButton.classList.add("opacity-100");
        nextButton.addEventListener("click", () => {
          slidesWrapper.scrollLeft += scrollAmount * 2;
        });
        prevButton.addEventListener("click", () => {
          slidesWrapper.scrollLeft -= scrollAmount * 2;
        });
        let touchStartX = 0;
        let touchEndX = 0;
        slidesWrapper.addEventListener("touchstart", (e) => {
          touchStartX = e.changedTouches[0].screenX;
        });
        slidesWrapper.addEventListener("touchend", (e) => {
          touchEndX = e.changedTouches[0].screenX;
          handleSwipe2();
        });
      }
    }
  });
})();
