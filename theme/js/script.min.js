(() => {
  // javascript/script.js
  document.addEventListener("DOMContentLoaded", function() {
    const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
    const mobileMenu = document.getElementById("mobile-menu");
    if (mobileMenuToggle && mobileMenu) {
      mobileMenuToggle.addEventListener("click", function(e) {
        e.preventDefault();
        e.stopPropagation();
        const isExpanded = mobileMenuToggle.getAttribute("aria-expanded") === "true";
        if (!isExpanded) {
          mobileMenuToggle.setAttribute("aria-expanded", "true");
          mobileMenuToggle.classList.add("is-active");
          mobileMenu.classList.add("visible");
        } else {
          mobileMenuToggle.setAttribute("aria-expanded", "false");
          mobileMenuToggle.classList.remove("is-active");
          mobileMenu.classList.remove("visible");
        }
      });
    }
    const menuItemsWithChildren = document.querySelectorAll("#mobile-menu .menu-item-has-children");
    menuItemsWithChildren.forEach(function(item) {
      const toggleButton = document.createElement("button");
      toggleButton.className = "mobile-submenu-toggle";
      toggleButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>';
      toggleButton.setAttribute("aria-expanded", "false");
      const itemLink = item.querySelector(":scope > a");
      if (itemLink) {
        itemLink.after(toggleButton);
      }
      toggleButton.addEventListener("click", function(e) {
        e.preventDefault();
        e.stopPropagation();
        const submenu2 = item.querySelector(".sub-menu");
        if (submenu2) {
          const isExpanded = toggleButton.getAttribute("aria-expanded") === "true";
          if (!isExpanded) {
            const scrollHeight = submenu2.scrollHeight;
            submenu2.style.maxHeight = scrollHeight + "px";
            toggleButton.setAttribute("aria-expanded", "true");
            toggleButton.classList.add("is-active");
            item.classList.add("has-active-submenu");
          } else {
            submenu2.style.maxHeight = "0";
            toggleButton.setAttribute("aria-expanded", "false");
            toggleButton.classList.remove("is-active");
            item.classList.remove("has-active-submenu");
          }
        }
      });
      const submenu = item.querySelector(".sub-menu");
      if (submenu) {
        submenu.style.maxHeight = "0";
      }
    });
    const mainNavigation = document.getElementById("site-navigation");
    const body = document.body;
    let navOffset = 0;
    let navHeight = 0;
    if (mainNavigation) {
      let toggleStickyNav2 = function() {
        if (window.pageYOffset > navOffset) {
          mainNavigation.classList.add("sticky-nav");
          body.classList.add("has-sticky-nav");
        } else {
          mainNavigation.classList.remove("sticky-nav");
          body.classList.remove("has-sticky-nav");
        }
      };
      var toggleStickyNav = toggleStickyNav2;
      navOffset = mainNavigation.offsetTop;
      navHeight = mainNavigation.offsetHeight;
      document.documentElement.style.setProperty("--nav-height", navHeight + "px");
      toggleStickyNav2();
      window.addEventListener("scroll", toggleStickyNav2);
      window.addEventListener("resize", function() {
        body.classList.remove("has-sticky-nav");
        mainNavigation.classList.remove("sticky-nav");
        setTimeout(function() {
          navOffset = mainNavigation.offsetTop;
          navHeight = mainNavigation.offsetHeight;
          document.documentElement.style.setProperty("--nav-height", navHeight + "px");
          toggleStickyNav2();
        }, 100);
      });
    }
  });
})();
