(() => {
  // node_modules/@_tw/typography/block-editor-classes.js
  var targetClasses = {
    "edit-post-visual-editor__post-title-wrapper": ["entry-header"],
    "wp-block-post-title": ["entry-title"],
    "wp-block-post-content": ["entry-content", ...tailwindTypographyClasses]
  };
  wp.domReady(() => {
    addTypographyClasses();
  });
  function getCurrentPostTypeClass() {
    let currentClass = null;
    for (const classToCheck of document.body.classList) {
      if (classToCheck.startsWith("post-type-")) {
        currentClass = classToCheck;
        break;
      }
    }
    return currentClass;
  }
  function addTypographyClasses() {
    const editorLoadedInterval = setInterval(function() {
      if (Object.keys(targetClasses).every(
        (className) => document.getElementsByClassName(className).length
      )) {
        if (getCurrentPostTypeClass()) {
          Object.values(targetClasses).forEach(
            (className) => className.push(getCurrentPostTypeClass())
          );
        }
        Object.entries(targetClasses).forEach(([targetClass, classes]) => {
          document.getElementsByClassName(targetClass)[0].classList.add(...classes);
        });
        Object.keys(targetClasses).forEach((className) => {
          mutationObserver.observe(document.querySelector("." + className), {
            attributes: true,
            attributeFilter: ["class"]
          });
        });
        clearInterval(editorLoadedInterval);
      } else if (document.getElementsByName("editor-canvas").length) {
        clearInterval(editorLoadedInterval);
      }
    }, 40);
  }
  var mutationObserver = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      const classList = mutation.target.classList;
      Object.entries(targetClasses).forEach(([targetClass, classes]) => {
        if (classList.contains(targetClass)) {
          if (!classes.every((className) => classList.contains(className))) {
            classList.add(...classes);
          }
        }
      });
    });
  });

  // javascript/block-editor.js
  wp.domReady(() => {
    wp.blocks.registerBlockStyle("core/paragraph", {
      name: "lead",
      label: "Lead"
    });
  });
})();
