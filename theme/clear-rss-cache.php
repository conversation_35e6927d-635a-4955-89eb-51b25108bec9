<?php
/**
 * Simple script to clear the RSS cache
 * 
 * This script is meant to be accessed directly to clear the cached RSS feed data.
 */

// Load WordPress core
require_once(__DIR__ . '/../../../wp-load.php');

// Clear the RSS cache
if (function_exists('dumabyt_clear_partner_articles_cache')) {
    dumabyt_clear_partner_articles_cache();
    echo "Partner articles cache has been cleared successfully.";
} else {
    echo "Error: Function dumabyt_clear_partner_articles_cache() not found.";
}

// Link back to debug page
echo '<p><a href="rss-debug-cihel.html">Back to RSS Debug Page</a></p>';
