# Clearing WordPress Transients via WP-CLI

WP-CLI provides a powerful way to manage WordPress transients directly from the command line. Here are the commands you can use to manage the RSS feed transient:

## Check if the transient exists

```bash
wp transient get dumabyt_partner_articles
```

## Delete the specific transient

```bash
wp transient delete dumabyt_partner_articles
```

## Delete all transients (use with caution)

```bash
wp transient delete --all
```

## Verify it's deleted

```bash
wp transient get dumabyt_partner_articles
```
This should return "Warning: Transient named 'dumabyt_partner_articles' not found." if successfully deleted.

## Refresh Object Cache (if using)

```bash
wp cache flush
```

## Test the changes

After deleting the transient, visit your website to see the updated "ČASOPIS MŮJ DŮM" content in the partners section.
