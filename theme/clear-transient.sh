#!/bin/bash

# Clear RSS Feed Transient using WP-CLI
# Run this script from the WordPress root directory

# Show current directory
echo "Current directory: $(pwd)"
echo ""

# Display if the transient exists
echo "Checking if transient exists..."
wp transient get dumabyt_partner_articles
echo ""

# Delete the transient
echo "Deleting transient 'dumabyt_partner_articles'..."
wp transient delete dumabyt_partner_articles
echo ""

# Verify it's deleted
echo "Verifying deletion..."
wp transient get dumabyt_partner_articles
echo ""

# Flush object cache (if applicable)
echo "Flushing WordPress object cache..."
wp cache flush
echo ""

echo "=== COMPLETE ==="
echo "The transient has been cleared. Please visit your website to see the updated content."
echo "You should now see 'ČASOPIS MŮJ DŮM' content instead of 'STAVÍME Z CIHEL' in the partners section."
