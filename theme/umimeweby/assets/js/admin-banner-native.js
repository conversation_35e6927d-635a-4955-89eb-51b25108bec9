/**
 * Admin JavaScript pro nativní implementaci správy bannerů
 *
 * @package dumabyt
 */
/* global jQuery, document */
jQuery(document).ready(function($) {
    'use strict';

    // Definice rozměrů pro různé typy bannerů
    const bannerDimensions = {
        'square': {
            width: 300,
            height: 300,
            label: 'Square (300x300 px)'
        },
        'leaderboard': {
            width: 745,
            height: 100,
            label: 'Leaderboard (745x100 px)'
        }
    };

    // Inicializace všech funkcí pro administraci bannerů
    initBannerAdmin();

    /**
     * Inicializace všech funkcí pro administraci bannerů
     */
    function initBannerAdmin() {
        // Toggle pozic podle typu banneru
        togglePositionsByType();
        
        // Zobrazení informace o rozměrech
        showDimensionsInfo();
        
        // Event listenery
        setupEventListeners();
    }

    /**
     * Nastavení event listenerů
     */
    function setupEventListeners() {
        // Změna typu banneru
        $('#banner_type').on('change', function() {
            togglePositionsByType();
            showDimensionsInfo();
        });
        
        // Sledování změn v nahraném obrázku
        $('#postimagediv').on('DOMSubtreeModified', function() {
            showDimensionsInfo();
        });
    }

    /**
     * Přepínání dostupných pozic podle typu banneru
     */
    function togglePositionsByType() {
        const selectedType = $('#banner_type').val();
        
        // Reset všech checkboxů (odstraníme disabled)
        $('input[name="banner_positions[]"]').closest('label').removeClass('disabled');
        $('input[name="banner_positions[]"]').prop('disabled', false);
        
        if (selectedType === 'square') {
            // Zakážeme homepage_bottom pro square
            $('input[name="banner_positions[]"][value="homepage_bottom"]')
                .prop('disabled', true)
                .prop('checked', false)
                .closest('label')
                .addClass('disabled');
        } else if (selectedType === 'leaderboard') {
            // Zakážeme homepage_sidebar pro leaderboard
            $('input[name="banner_positions[]"][value="homepage_sidebar"]')
                .prop('disabled', true)
                .prop('checked', false)
                .closest('label')
                .addClass('disabled');
        }
    }

    /**
     * Zobrazení informace o doporučených rozměrech obrázku
     */
    function showDimensionsInfo() {
        const selectedType = $('#banner_type').val();
        if (!selectedType || !bannerDimensions[selectedType]) {
            return;
        }
        
        const expectedDimensions = bannerDimensions[selectedType];
        const $imageContainer = $('#postimagediv .inside');
        
        // Nejprve odstraníme existující zprávy
        $('.banner-dimension-notice').remove();
        
        // Přidáme pouze informaci o doporučených rozměrech
        $imageContainer.append(
            $('<div class="banner-dimension-notice" style="margin-top: 10px; font-style: italic;">').html(
                'Doporučené rozměry pro tento typ banneru: <strong>' +
                expectedDimensions.width + ' x ' + expectedDimensions.height + ' px</strong>'
            )
        );
    }
});
