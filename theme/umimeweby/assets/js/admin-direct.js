/**
 * Admin JavaScript pro přímou implementaci bannerů
 *
 * @package dumabyt
 */
/* global jQuery, document, Image */
jQuery(document).ready(function($) {
    'use strict';

    // Definice rozměrů pro různé typy bannerů
    const bannerDimensions = {
        'square': {
            width: 300,
            height: 300,
            label: 'Square (300x300 px)'
        },
        'leaderboard': {
            width: 745,
            height: 100,
            label: 'Leaderboard (745x100 px)'
        }
    };

    // Funkce pro přidání upozornění
    function addDynamicNotice() {
        $('.wp-header-end').after(
            $('<div class="notice notice-info is-dismissible dynamic-notice"><p><strong>Banner Manager:</strong> Nastavte prosím všechny potřebné parametry banneru níže.</p></div>')
        );
    }

    // Kontrola, zda jsme na stránce editace banneru
    if ($('#banner_type').length) {
        addDynamicNotice();
        
        // Aktualizace pozic podle typu banneru
        $('#banner_type').on('change', function() {
            const type = $(this).val();
            
            // Zobrazení/skrytí pozic podle typu
            if (type === 'square') {
                $('input[value="homepage_bottom"]').closest('label').addClass('disabled')
                    .find('input').prop('disabled', true).prop('checked', false);
                $('input[value="homepage_sidebar"]').closest('label').removeClass('disabled')
                    .find('input').prop('disabled', false);
            } else if (type === 'leaderboard') {
                $('input[value="homepage_sidebar"]').closest('label').addClass('disabled')
                    .find('input').prop('disabled', true).prop('checked', false);
                $('input[value="homepage_bottom"]').closest('label').removeClass('disabled')
                    .find('input').prop('disabled', false);
            } else {
                $('input[name="banner_positions[]"]').closest('label').removeClass('disabled')
                    .find('input').prop('disabled', false);
            }

            // Validace obrázku
            validateFeaturedImage();
        });

        // Inicializace validace při načtení stránky
        $('#banner_type').trigger('change');
        
        // Přidání informací o rozměrech obrázku
        $('#postimagediv .inside').append(
            $('<p style="color: #cc0000; font-weight: bold;">').text('Důležité: Nahrajte obrázek ve správných rozměrech podle typu banneru!')
        );
    }

    // Funkce pro validaci obrázku pro metaboxy
    function validateFeaturedImage() {
        const selectedType = $('#banner_type').val();
        if (!selectedType || !bannerDimensions[selectedType]) {
            return;
        }
        
        const expectedDimensions = bannerDimensions[selectedType];
        
        // Odstraní existující zprávy
        $('.dimensions-notice').remove();
        
        // Přidá zprávu o požadovaných rozměrech
        $('#postimagediv .inside').append(
            $('<div class="dimensions-notice notice notice-warning inline" style="margin: 5px 0;"><p>').html(
                'Doporučené rozměry: <strong>' + expectedDimensions.width + ' × ' + expectedDimensions.height + ' px</strong>'
            )
        );
        
        // Kontrola obrázku
        const $image = $('#set-post-thumbnail img');
        if ($image.length) {
            const img = new Image();
            img.onload = function() {
                const imgWidth = this.width;
                const imgHeight = this.height;
                
                if (imgWidth !== expectedDimensions.width || imgHeight !== expectedDimensions.height) {
                    $('#postimagediv .inside').append(
                        $('<div class="dimensions-notice notice notice-error inline" style="margin: 5px 0;"><p>').html(
                            '<strong>Nesprávné rozměry!</strong> Aktuální: ' + imgWidth + ' × ' + imgHeight + ' px'
                        )
                    );
                } else {
                    $('#postimagediv .inside').append(
                        $('<div class="dimensions-notice notice notice-success inline" style="margin: 5px 0;"><p>').html(
                            '<strong>✓ Správné rozměry</strong> ' + imgWidth + ' × ' + imgHeight + ' px'
                        )
                    );
                }
            };
            img.src = $image.attr('src');
        }
    }
    
    // Sledování změn náhledového obrázku
    $('#postimagediv').on('DOMSubtreeModified', function() {
        if ($('#set-post-thumbnail img').length) {
            validateFeaturedImage();
        }
    });
});
