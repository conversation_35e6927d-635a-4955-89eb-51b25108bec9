/**
 * Admin JavaScript pro správu bannerů
 *
 * @package dumabyt
 */
/* global jQuery, document */
jQuery(document).ready(function($) {
    'use strict';

    // Definice rozměrů pro různé typy bannerů
    const bannerDimensions = {
        'square': {
            width: 300,
            height: 300,
            label: 'Square (300x300 px)'
        },
        'leaderboard': {
            width: 745,
            height: 100,
            label: 'Leaderboard (745x100 px)'
        }
    };

    const bannerTypeField = $('#banner_type');
    
    if (bannerTypeField.length) {
        // Přidání třídy k nadřazenému elementu pro styly
        bannerTypeField.closest('.rwmb-field').addClass('banner-type-field');
        
        // Počáteční nastavení podle vybraného typu
        initBannerTypeField();
        
        // Event listener pro změnu typu banneru
        bannerTypeField.on('change', function() {
            updateBannerTypeClasses();
            updateBannerPositionOptions();
        });

        // Event listener pro nahrání obrázku
        $('#wpbody').on('DOMNodeInserted', '.rwmb-image-item', function() {
            showDimensionsInfo();
        });
    }

    // Inicializace zobrazení rozměrů při načtení stránky
    showDimensionsInfo();

    /**
     * Inicializace pole typu banneru
     */
    function initBannerTypeField() {
        updateBannerTypeClasses();
        updateBannerPositionOptions();
    }

    /**
     * Aktualizace tříd podle vybraného typu banneru
     */
    function updateBannerTypeClasses() {
        const bannerType = $('#banner_type').val();
        
        // Odstranění předchozích tříd
        $('.rwmb-field._thumbnail_id_field')
            .removeClass('banner-type-square')
            .removeClass('banner-type-leaderboard');
        
        // Přidání nové třídy podle aktuálního typu
        if (bannerType) {
            $('.rwmb-field._thumbnail_id_field').addClass('banner-type-' + bannerType);
        }
    }

    /**
     * Aktualizace dostupných pozic podle typu banneru
     */
    function updateBannerPositionOptions() {
        const bannerType = $('#banner_type').val();
        const positionField = $('#banner_positions');
        
        if (!positionField.length || !bannerType) {
            return;
        }
        
        // Získání všech checkboxů
        const checkboxes = positionField.closest('.rwmb-field').find('input[type="checkbox"]');
        
        // Reset všech checkboxů
        checkboxes.prop('disabled', false).closest('label').show();
        
        // Nastavení podle typu
        if (bannerType === 'square') {
            // Square bannery - zakáže homepage_bottom
            checkboxes.filter('[value="homepage_bottom"]')
                .prop('disabled', true)
                .prop('checked', false)
                .closest('label')
                .addClass('disabled')
                .attr('title', 'Pouze pro typ Leaderboard');
        } else if (bannerType === 'leaderboard') {
            // Leaderboard bannery - zakáže homepage_sidebar
            checkboxes.filter('[value="homepage_sidebar"]')
                .prop('disabled', true)
                .prop('checked', false)
                .closest('label')
                .addClass('disabled')
                .attr('title', 'Pouze pro typ Square');
        }
    }

    /**
     * Zobrazení informace o doporučených rozměrech
     */
    function showDimensionsInfo() {
        const bannerType = $('#banner_type').val();
        if (!bannerType) {
            return;
        }
        
        const imageItems = $('.rwmb-image-item');
        if (!imageItems.length) {
            return;
        }
        
        const dimensions = bannerDimensions[bannerType];
        if (!dimensions) {
            return;
        }

        // Získání všech obrázků
        imageItems.each(function() {
            const imageItem = $(this);
            
            // Odstranění všech předchozích informací
            imageItem.find('.banner-warning, .banner-dimensions-info').remove();
            
            // Pouze přidání informace o požadovaných rozměrech
            $('<span class="banner-dimensions-info">Doporučené rozměry: ' + 
              dimensions.width + 'x' + dimensions.height + 'px</span>').appendTo(imageItem);
        });
    }
});
