/**
 * Styly pro bannery UmimeWeby
 *
 * @package dumabyt
 */

/* Základní styly pro bannery */
.uw-banner {
    display: block;
    margin: 0.75rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    overflow: hidden;
    background: #fff;
}

.uw-banner-link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
}

.uw-banner-image {
    display: block;
    width: 100%;
    height: auto;
}

/* Square Banner (300x300 px) */
.uw-banner-square {
    width: 300px !important;
    height: 300px !important;
    max-width: 100% !important;
    aspect-ratio: 1 / 1 !important;
}

.uw-banner-square .uw-banner-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

/* Leaderboard Banner (745x100 px) */
.uw-banner-leaderboard {
    width: 100%;
    max-width: 745px;
    height: 100px;
    margin-left: auto;
    margin-right: auto;
    /* Odstránenie podkladu */
    background: transparent;
    box-shadow: none;
    border: none;
    border-radius: 0;
    /* Posunúť bližšie k headeru */
    margin-top: 0.1rem;
    margin-bottom: 0.1rem;
}

.uw-banner-leaderboard .uw-banner-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Placeholder styly */
.uw-banner-placeholder {
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #666;
}

.uw-placeholder-content {
    padding: 1rem;
}

.uw-placeholder-title {
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
    font-weight: 500;
}

.uw-placeholder-size {
    margin: 0.25rem 0 0 0;
    font-size: 0.875rem;
    opacity: 0.7;
}

/* Responzivní úpravy */
@media (max-width: 767px) {
    .uw-banner-square {
        margin-left: auto;
        margin-right: auto;
    }
    
    .uw-banner-leaderboard {
        height: auto;
        min-height: 80px;
    }
}

/* Bannery v článcích */
.single-post .uw-banner, 
.single-page .uw-banner {
    margin: 2rem auto;
}

/* Force square aspect ratio with highest specificity */
div.uw-banner.uw-banner-square,
.uw-banner-square.uw-banner-placeholder,
.lg\:col-span-1 .uw-banner-square,
body .uw-banner-square {
    width: 300px !important;
    height: 300px !important;
    aspect-ratio: 1 / 1 !important;
    display: block !important;
    min-height: 300px !important;
    max-height: 300px !important;
}

div.uw-banner.uw-banner-square .uw-banner-image,
.uw-banner-square.uw-banner-placeholder .uw-banner-image,
.lg\:col-span-1 .uw-banner-square .uw-banner-image,
body .uw-banner-square .uw-banner-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    display: block !important;
    min-height: 300px !important;
    max-height: 300px !important;
}

/* Override any responsive styles */
@media (max-width: 767px) {
    div.uw-banner.uw-banner-square,
    .uw-banner-square.uw-banner-placeholder,
    .lg\:col-span-1 .uw-banner-square,
    body .uw-banner-square {
        width: 280px !important;
        height: 280px !important;
        min-height: 280px !important;
        max-height: 280px !important;
    }
    
    div.uw-banner.uw-banner-square .uw-banner-image,
    .uw-banner-square.uw-banner-placeholder .uw-banner-image,
    .lg\:col-span-1 .uw-banner-square .uw-banner-image,
    body .uw-banner-square .uw-banner-image {
        min-height: 280px !important;
        max-height: 280px !important;
    }
}
