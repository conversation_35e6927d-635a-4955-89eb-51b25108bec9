<?php
/**
 * <PERSON>lavní třída pro inicializaci rozšíření UmimeWeby
 *
 * @package dumabyt
 */

if (!class_exists('UmimewebyCore')) {

    /**
     * Hlavní třída UmimewebyCcore
     */
    class UmimewebyCore {
        /**
         * Instance této třídy
         *
         * @var UmimewebyCore
         */
        private static $instance = null;
        
        /**
         * Získání instance třídy (Singleton pattern)
         *
         * @return UmimewebyCore Instance třídy
         */
        public static function get_instance() {
            if (null === self::$instance) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * Konstruktor třídy.
         */
        private function __construct() {
            // Kontrola existence potřebných funkcí
            if (!function_exists('dumabyt_add_debug_notice')) {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-error"><p>CHYBA: Funkce dumabyt_add_debug_notice není definována!</p></div>';
                });
                return;
            }
            
            // Načtení všech souborů
            $this->load_dependencies();
            
            // Přímá inicializace tříd, nečekáme na hook
            $this->init();
            
            // Pro jistotu necháme i hook verzi
            add_action('init', array($this, 'init'), 0);
            
            // Inicializace
        }
        
        /**
         * Načtení závislostí.
         */
        public function load_dependencies() {
            // Kontrola existence adresáře
            $base_dir = get_template_directory() . '/umimeweby/banners/';
            if (!file_exists($base_dir)) {
                if (WP_DEBUG && is_admin()) {
                    dumabyt_add_debug_notice('UmimewebyCore: Adresář banners neexistuje: ' . $base_dir, 'error');
                }
                return;
            }
            
            // Načtení všech tříd
            $files = array(
                $base_dir . 'banner-config.php',        // Centralizovaná konfigurace
                $base_dir . 'class-banner-post-type.php',
                $base_dir . 'class-banner-manager.php',
                $base_dir . 'class-banner-renderer.php',
                $base_dir . 'class-banner-admin.php',   // Vylepšení administrace
                $base_dir . 'class-banner-metabox.php', // MetaBox plugin integrace
                $base_dir . 'class-native-metabox.php', // Nativní WP metaboxy
            );
            
            foreach ($files as $file) {
                if (file_exists($file)) {
                    require_once $file;
                } else if (WP_DEBUG && is_admin()) {
                    dumabyt_add_debug_notice('UmimewebyCore: Soubor neexistuje: ' . $file, 'error');
                }
            }
        }
        
        /**
         * Inicializace všech komponent.
         */
        public function init() {
            // Inicializace komponent
            BannerPostType::get_instance();
            BannerManager::get_instance();
            BannerRenderer::get_instance();
            
            // Vylepšení administrace a metaboxy
            if (is_admin()) {
                BannerAdmin::get_instance();
                
                // Inicializace metaboxů - pro formuláře bannerů
                if (class_exists('BannerMetabox')) {
                    BannerMetabox::get_instance();
                }
                if (class_exists('BannerNativeMetabox')) {
                    BannerNativeMetabox::get_instance();
                }
            }
        }
    }
}
