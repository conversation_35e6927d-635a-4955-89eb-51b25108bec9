<?php
/**
 * Třída pro vlastní implementaci meta boxů pro bannery bez závislosti na Meta Box pluginu
 *
 * @package dumabyt
 */

if (!class_exists('BannerNativeMetabox')) {

    /**
     * Třída pro vlastní implementaci meta boxů pro bannery
     */
    class BannerNativeMetabox {
        /**
         * Instance této třídy
         *
         * @var BannerNativeMetabox
         */
        private static $instance = null;
        
        /**
         * Získání instance třídy (Singleton pattern)
         *
         * @return BannerNativeMetabox Instance třídy
         */
        public static function get_instance() {
            if (null === self::$instance) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * Konstruktor třídy.
         */
        private function __construct() {
            // Registrace meta boxů
            add_action('add_meta_boxes', array($this, 'register_meta_boxes'));
            
            // Uložení dat
            add_action('save_post_uw_banner', array($this, 'save_banner_meta'));
            add_action('save_post', array($this, 'save_article_banner'));
            
            // JS a CSS
            add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
            
            // Registrace meta polí
            add_action('init', array($this, 'register_meta_fields'));
        }
        
        /**
         * Registrace meta polí
         */
        public function register_meta_fields() {
            register_post_meta('uw_banner', 'banner_type', array(
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'auth_callback' => function() { 
                    return current_user_can('edit_posts'); 
                }
            ));
            
            register_post_meta('uw_banner', 'banner_url', array(
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'auth_callback' => function() { 
                    return current_user_can('edit_posts'); 
                }
            ));
            
            register_post_meta('uw_banner', 'banner_positions', array(
                'show_in_rest' => true,
                'single' => false,
                'type' => 'string',
                'auth_callback' => function() { 
                    return current_user_can('edit_posts'); 
                }
            ));
            
            register_post_meta('uw_banner', 'banner_date_from', array(
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'auth_callback' => function() { 
                    return current_user_can('edit_posts'); 
                }
            ));
            
            register_post_meta('uw_banner', 'banner_date_to', array(
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'auth_callback' => function() { 
                    return current_user_can('edit_posts'); 
                }
            ));
            
            register_post_meta('uw_banner', 'banner_tracking', array(
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'auth_callback' => function() { 
                    return current_user_can('edit_posts'); 
                }
            ));
            
            // Pro články
            register_post_meta('post', 'article_banner_id', array(
                'show_in_rest' => true,
                'single' => true,
                'type' => 'number',
                'auth_callback' => function() { 
                    return current_user_can('edit_posts'); 
                }
            ));
            
            register_post_meta('page', 'article_banner_id', array(
                'show_in_rest' => true,
                'single' => true,
                'type' => 'number',
                'auth_callback' => function() { 
                    return current_user_can('edit_posts'); 
                }
            ));
        }
        
        /**
         * Načtení skriptů a stylů pro admin
         */
        public function admin_scripts($hook) {
            $screen = get_current_screen();
            if ('uw_banner' === $screen->post_type) {
                wp_enqueue_style(
                    'uw-banner-admin', 
                    get_template_directory_uri() . '/umimeweby/assets/css/admin-banner.css',
                    array(),
                    '1.0.0'
                );
                
                wp_enqueue_script(
                    'uw-banner-admin-native', 
                    get_template_directory_uri() . '/umimeweby/assets/js/admin-banner-native.js',
                    array('jquery'),
                    '1.0.0',
                    true
                );
            }
        }
        
        /**
         * Registrace meta boxů
         */
        public function register_meta_boxes() {
            add_meta_box(
                'banner_settings',
                __('Nastavení banneru', 'dumabyt'),
                array($this, 'banner_settings_callback'),
                'uw_banner',
                'normal',
                'high'
            );
            
            add_meta_box(
                'article_banner',
                __('Bannery v článku', 'dumabyt'),
                array($this, 'article_banner_callback'),
                array('post', 'page'),
                'side',
                'default'
            );
        }
        
        /**
         * Callback pro nastavení banneru
         */
        public function banner_settings_callback($post) {
            // Nonce pro bezpečnost
            wp_nonce_field('save_banner_settings', 'banner_settings_nonce');
            
            // Načtení aktuálních hodnot
            $banner_type = get_post_meta($post->ID, 'banner_type', true);
            $banner_url = get_post_meta($post->ID, 'banner_url', true);
            $banner_positions = get_post_meta($post->ID, 'banner_positions', false);
            $banner_date_from = get_post_meta($post->ID, 'banner_date_from', true);
            $banner_date_to = get_post_meta($post->ID, 'banner_date_to', true);
            $banner_tracking = get_post_meta($post->ID, 'banner_tracking', true);
            // Diagnostické informace byly odstraněny pro čistější rozhraní
            
            // Výchozí hodnoty
            if (empty($banner_positions)) {
                $banner_positions = array();
            }
            
            // Dostupné pozice
            $available_positions = BannerConfig::get_positions();
            
            ?>
            <style>
                .banner-field {
                    margin-bottom: 15px;
                }
                .banner-field label {
                    display: block;
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                .banner-field input[type="text"],
                .banner-field input[type="url"],
                .banner-field input[type="date"],
                .banner-field select {
                    width: 100%;
                    max-width: 500px;
                }
                .banner-field textarea {
                    width: 100%;
                    height: 80px;
                }
                .banner-field .description {
                    color: #666;
                    font-style: italic;
                    margin-top: 3px;
                }
                .banner-positions label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: normal;
                }
                .banner-section {
                    margin-bottom: 20px;
                    background: #f9f9f9;
                    padding: 10px;
                    border-left: 3px solid #007cba;
                }
            </style>
            
            <h1>Nastavení banneru</h1>
            <p class="description">Zde můžete nastavit všechny parametry banneru včetně typu, pozic a dalších.</p>
            
            <!-- Typ banneru -->
            <div class="banner-section">
                <div class="banner-field">
                    <label for="banner_type"><?php _e('Typ banneru', 'dumabyt'); ?></label>
                    <select name="banner_type" id="banner_type">
                        <option value=""><?php _e('-- Vyberte typ --', 'dumabyt'); ?></option>
                        <option value="square" <?php selected($banner_type, 'square'); ?>><?php _e('Square (300x300 px)', 'dumabyt'); ?></option>
                        <option value="leaderboard" <?php selected($banner_type, 'leaderboard'); ?>><?php _e('Leaderboard (745x100 px)', 'dumabyt'); ?></option>
                    </select>
                    <p class="description"><?php _e('Vyberte typ banneru, který určí jeho rozměry.', 'dumabyt'); ?></p>
                </div>
            </div>
            
            <!-- URL odkazu -->
            <div class="banner-section">
                <div class="banner-field">
                    <label for="banner_url"><?php _e('URL odkazu', 'dumabyt'); ?></label>
                    <input type="url" id="banner_url" name="banner_url" value="<?php echo esc_url($banner_url); ?>" placeholder="https://" />
                    <p class="description"><?php _e('Zadejte URL, na kterou bude banner odkazovat.', 'dumabyt'); ?></p>
                </div>
            </div>
            
            <!-- Pozice -->
            <div class="banner-section">
                <div class="banner-field">
                    <label><?php _e('Pozice', 'dumabyt'); ?></label>
                    <div class="banner-positions">
                        <?php foreach ($available_positions as $position_key => $position_label) : ?>
                            <label>
                                <input type="checkbox" name="banner_positions[]" value="<?php echo esc_attr($position_key); ?>" <?php checked(in_array($position_key, $banner_positions)); ?> />
                                <?php echo esc_html($position_label); ?>
                            </label>
                        <?php endforeach; ?>
                    </div>
                    <p class="description"><?php _e('Vyberte pozice, kde se má banner zobrazovat.', 'dumabyt'); ?></p>
                    <p class="description" style="color: #d63638; font-weight: bold;">
                        <?php _e('⚠️ Důležité: Každý banner by měl být zobrazen pouze na jedné konkrétní pozici.', 'dumabyt'); ?><br>
                        <?php _e('Nevybírejte kombinaci více pozic pro stejný banner.', 'dumabyt'); ?>
                    </p>
                </div>
            </div>
            
            <!-- Časové období -->
            <div class="banner-section">
                <div class="banner-field">
                    <label for="banner_date_from"><?php _e('Aktivní od', 'dumabyt'); ?></label>
                    <input type="date" id="banner_date_from" name="banner_date_from" value="<?php echo esc_attr($banner_date_from); ?>" />
                    <p class="description"><?php _e('Datum, od kterého se má banner zobrazovat. Ponechte prázdné pro okamžité zobrazení.', 'dumabyt'); ?></p>
                </div>
                
                <div class="banner-field">
                    <label for="banner_date_to"><?php _e('Aktivní do', 'dumabyt'); ?></label>
                    <input type="date" id="banner_date_to" name="banner_date_to" value="<?php echo esc_attr($banner_date_to); ?>" />
                    <p class="description"><?php _e('Datum, do kterého se má banner zobrazovat. Ponechte prázdné pro neomezené zobrazení.', 'dumabyt'); ?></p>
                </div>
            </div>
            
            <!-- Tracking kód byl odstraněn -->
            <?php
        }
        
        /**
         * Callback pro výběr banneru v článku
         */
        public function article_banner_callback($post) {
            // Nonce pro bezpečnost
            wp_nonce_field('save_article_banner', 'article_banner_nonce');
            
            // Načtení aktuální hodnoty
            $article_banner_id = get_post_meta($post->ID, 'article_banner_id', true);
            
            // Načtení všech dostupných bannerů pro články
            $banners_query = new WP_Query(array(
                'post_type' => 'uw_banner',
                'posts_per_page' => -1,
                'meta_query' => array(
                    array(
                        'key' => 'banner_positions',
                        'value' => 'article',
                        'compare' => 'LIKE'
                    )
                )
            ));
            
            if ($banners_query->have_posts()) :
                ?>
                <p>
                    <label for="article_banner_id"><?php _e('Vyberte banner', 'dumabyt'); ?></label>
                    <select name="article_banner_id" id="article_banner_id" style="width: 100%;">
                        <option value=""><?php _e('-- Žádný banner --', 'dumabyt'); ?></option>
                        <?php while ($banners_query->have_posts()) : $banners_query->the_post(); ?>
                            <option value="<?php echo get_the_ID(); ?>" <?php selected($article_banner_id, get_the_ID()); ?>>
                                <?php the_title(); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </p>
                <p class="description"><?php _e('Vyberte banner, který se zobrazí v tomto článku.', 'dumabyt'); ?></p>
                <?php
                wp_reset_postdata();
            else :
                ?>
                <p><?php _e('Nejsou k dispozici žádné bannery pro články. Vytvořte nejprve banner a přiřaďte mu pozici "Články".', 'dumabyt'); ?></p>
                <?php
            endif;
        }
        
        /**
         * Uložení metadat banneru
         */
        public function save_banner_meta($post_id) {
            // Kontrola, zda jde o autosave
            if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
                return;
            }
            
            // Kontrola nonce
            if (!isset($_POST['banner_settings_nonce']) || !wp_verify_nonce($_POST['banner_settings_nonce'], 'save_banner_settings')) {
                return;
            }
            
            // Kontrola oprávnění
            if (!current_user_can('edit_post', $post_id)) {
                return;
            }
            
            // Uložení hodnot
            if (isset($_POST['banner_type'])) {
                update_post_meta($post_id, 'banner_type', sanitize_text_field($_POST['banner_type']));
            }
            
            if (isset($_POST['banner_url'])) {
                update_post_meta($post_id, 'banner_url', esc_url_raw($_POST['banner_url']));
            }
            
            // Nejprve smažeme všechny pozice
            delete_post_meta($post_id, 'banner_positions');
            
            // Pak přidáme nové
            if (isset($_POST['banner_positions']) && is_array($_POST['banner_positions'])) {
                foreach ($_POST['banner_positions'] as $position) {
                    add_post_meta($post_id, 'banner_positions', sanitize_text_field($position));
                }
            }
            
            if (isset($_POST['banner_date_from'])) {
                update_post_meta($post_id, 'banner_date_from', sanitize_text_field($_POST['banner_date_from']));
            }
            
            if (isset($_POST['banner_date_to'])) {
                update_post_meta($post_id, 'banner_date_to', sanitize_text_field($_POST['banner_date_to']));
            }
            
            if (isset($_POST['banner_tracking'])) {
                update_post_meta($post_id, 'banner_tracking', $_POST['banner_tracking']);
            }
        }
        
        /**
         * Uložení ID banneru pro článek
         */
        public function save_article_banner($post_id) {
            // Kontrola, zda jde o autosave
            if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
                return;
            }
            
            // Kontrola nonce
            if (!isset($_POST['article_banner_nonce']) || !wp_verify_nonce($_POST['article_banner_nonce'], 'save_article_banner')) {
                return;
            }
            
            // Kontrola oprávnění
            if (!current_user_can('edit_post', $post_id)) {
                return;
            }
            
            // Uložení hodnoty
            if (isset($_POST['article_banner_id'])) {
                update_post_meta($post_id, 'article_banner_id', intval($_POST['article_banner_id']));
            }
        }
    }
}
