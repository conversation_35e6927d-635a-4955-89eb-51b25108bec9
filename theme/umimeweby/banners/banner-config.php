<?php
/**
 * Centralizované definice pro banner systém
 *
 * @package dumabyt
 * @since 1.0.0
 */

if (!class_exists('BannerConfig')) {

    /**
     * Třída pro centralizaci konfigurace bannerů
     */
    class BannerConfig {
        /**
         * Dostupné pozice bannerů
         *
         * @return array Asociativní pole pozic [kód => popis]
         */
        public static function get_positions() {
            return array(
                'carousel_sidebar' => __('Homepage - vedle carouselu (Square - max 2)', 'dumabyt'),
                'homepage_sidebar' => __('Homepage - postranní panel (Square)', 'dumabyt'),
                'homepage_bottom'  => __('Cel<PERSON> web - horní č<PERSON>t (Leaderboard)', 'dumabyt'),
                'article'          => __('Články', 'dumabyt')
            );
        }
        
        /**
         * <PERSON>ískání pozic kompatibilních s daným typem banneru
         *
         * @param string $banner_type Typ banneru (square, leaderboard)
         * @return array Pozice kompatibilní s typem banneru
         */
        public static function get_positions_for_type($banner_type) {
            $all_positions = self::get_positions();
            
            if ($banner_type === 'square') {
                return array_filter($all_positions, function($key) {
                    return in_array($key, array('carousel_sidebar', 'homepage_sidebar', 'article'));
                }, ARRAY_FILTER_USE_KEY);
            } elseif ($banner_type === 'leaderboard') {
                return array_filter($all_positions, function($key) {
                    return in_array($key, array('homepage_bottom'));
                }, ARRAY_FILTER_USE_KEY);
            }
            
            return $all_positions;
        }
        
        /**
         * Maximální počet bannerů pro danou pozici
         *
         * @param string $position Kód pozice
         * @return int Maximální počet bannerů nebo null pro neomezený počet
         */
        public static function get_max_banners_for_position($position) {
            $limits = array(
                'carousel_sidebar' => 2,
                'homepage_sidebar' => null, // neomezeno
                'homepage_bottom'  => 1,
                'article'          => null  // neomezeno
            );
            
            return isset($limits[$position]) ? $limits[$position] : null;
        }
    }
}
