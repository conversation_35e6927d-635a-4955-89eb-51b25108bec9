<?php
/**
 * Třída pro registraci vlastního post typu Banner
 *
 * @package dumabyt
 */

if (!class_exists('BannerPostType')) {

    /**
     * Třída pro správu vlastního post typu Banner
     */
    class BannerPostType {
        /**
         * Instance této třídy
         *
         * @var BannerPostType
         */
        private static $instance = null;
        
        /**
         * Získání instance třídy (Singleton pattern)
         *
         * @return BannerPostType Instance třídy
         */
        public static function get_instance() {
            if (null === self::$instance) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * Konstruktor třídy.
         */
        private function __construct() {
            // Spuštění funkce přímo při inicializaci objektu, nejen na hooku 'init'
            $this->register_post_type();
            
            // Ponecháme také hooked verzi, abychom nastavili správné podm<PERSON>ky při opětovné registraci
            add_action('init', array($this, 'register_post_type'));
            
            // Debug
            if (WP_DEBUG && is_admin()) {
                add_action('admin_notices', array($this, 'debug_post_type'));
            }
        }
        
        /**
         * Diagnostika post typu
         */
        public function debug_post_type() {
            // Diagnostika odstraněna
        }
        
        /**
         * Registrace vlastního post typu Banner
         */
        public function register_post_type() {
            $labels = array(
                'name'                  => _x('Reklamní bannery', 'Post type general name', 'dumabyt'),
                'singular_name'         => _x('Reklamní banner', 'Post type singular name', 'dumabyt'),
                'menu_name'             => _x('🖼️ Bannery', 'Admin Menu text', 'dumabyt'),
                'name_admin_bar'        => _x('Banner', 'Add New on Toolbar', 'dumabyt'),
                'add_new'               => __('Přidat nový', 'dumabyt'),
                'add_new_item'          => __('Přidat nový banner', 'dumabyt'),
                'new_item'              => __('Nový banner', 'dumabyt'),
                'edit_item'             => __('Upravit banner', 'dumabyt'),
                'view_item'             => __('Zobrazit banner', 'dumabyt'),
                'all_items'             => __('Všechny bannery', 'dumabyt'),
                'search_items'          => __('Hledat bannery', 'dumabyt'),
                'not_found'             => __('Žádné bannery nenalezeny', 'dumabyt'),
                'not_found_in_trash'    => __('Žádné bannery nenalezeny v koši', 'dumabyt'),
                'featured_image'        => __('Obrázek banneru (povinný)', 'dumabyt'),
                'set_featured_image'    => __('Nastavit obrázek banneru (klikněte sem)', 'dumabyt'),
                'remove_featured_image' => __('Odstranit obrázek banneru', 'dumabyt'),
                'use_featured_image'    => __('Použít jako obrázek banneru', 'dumabyt'),
                'attributes'            => __('Vlastnosti banneru', 'dumabyt'),
                'filter_items_list'     => __('Filtrovat seznam bannerů', 'dumabyt'),
                'items_list'            => __('Seznam bannerů', 'dumabyt'),
                'item_published'        => __('Banner publikován', 'dumabyt'),
                'item_updated'          => __('Banner aktualizován', 'dumabyt'),
            );
            
            $args = array(
                'labels'             => $labels,
                'public'             => false,
                'publicly_queryable' => false,
                'show_ui'            => true,
                'show_in_menu'       => true,
                'query_var'          => false,
                'rewrite'            => false,
                'capability_type'    => 'post',
                'has_archive'        => false,
                'hierarchical'       => false,
                'menu_position'      => 20,
                'menu_icon'          => 'dashicons-images-alt2',
                'supports'           => array('title', 'thumbnail'),
                'show_in_rest'       => true,
                // Nyní používáme metaboxy z tříd BannerMetabox a NativeMetabox
            );
            
            register_post_type('uw_banner', $args);
        }
        
    }
}
