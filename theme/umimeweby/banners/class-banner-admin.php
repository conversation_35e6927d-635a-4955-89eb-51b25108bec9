<?php
/**
 * Třída pro vylepšení administračního rozhraní pro bannery
 *
 * @package dumabyt
 */

if (!class_exists('BannerAdmin')) {

    /**
     * Třída pro vylepšení administračního rozhraní
     */
    class BannerAdmin {
        /**
         * Instance této třídy
         *
         * @var BannerAdmin
         */
        private static $instance = null;
        
        /**
         * Získání instance třídy (Singleton pattern)
         *
         * @return BannerAdmin Instance třídy
         */
        public static function get_instance() {
            if (null === self::$instance) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * Konstruktor třídy.
         */
        private function __construct() {
            // Admin rozhraní vylepšení
            add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
            add_action('admin_head', array($this, 'add_admin_inline_styles'));
            add_action('admin_menu', array($this, 'modify_admin_menu'));
            add_action('admin_notices', array($this, 'display_admin_notices'));
            
            // Sloupce pro přehled bannerů
            add_filter('manage_uw_banner_posts_columns', array($this, 'banner_columns'));
            add_action('manage_uw_banner_posts_custom_column', array($this, 'banner_column_content'), 10, 2);
            add_filter('manage_edit-uw_banner_sortable_columns', array($this, 'banner_sortable_columns'));
            
            // Filtry pro seznam bannerů
            add_action('restrict_manage_posts', array($this, 'add_banner_filters'));
            add_filter('parse_query', array($this, 'filter_banner_query'));
        }
        
        /**
         * Načtení skriptů a stylů pro administraci
         */
        public function enqueue_admin_assets($hook) {
            $screen = get_current_screen();
            
            if ('uw_banner' === $screen->post_type) {
                // Načtení dashicons
                wp_enqueue_style('dashicons');
                
                // Přidání speciálních stylů pro administraci
                wp_enqueue_style(
                    'uw-banner-admin-enhanced',
                    get_template_directory_uri() . '/umimeweby/assets/css/admin-enhanced.css',
                    array(),
                    '1.0.1' // Update version to force cache refresh
                );
                
                // Načtení JS pro správu bannerů včetně JS pro carousel
                wp_enqueue_script(
                    'uw-banner-admin-js',
                    get_template_directory_uri() . '/umimeweby/assets/js/admin-banner.js',
                    array('jquery'),
                    '1.0.1', // Update version to force cache refresh
                    true
                );
                
                // Načtení JS pro nativní implementaci
                wp_enqueue_script(
                    'uw-banner-admin-native-js',
                    get_template_directory_uri() . '/umimeweby/assets/js/admin-banner-native.js',
                    array('jquery'),
                    '1.0.1', // Update version to force cache refresh
                    true
                );
            }
        }
        
        /**
         * Přidání inline stylů do hlavičky administrace
         */
        public function add_admin_inline_styles() {
            $screen = get_current_screen();
            
            if ('uw_banner' === $screen->post_type) {
                echo '<style>
                    .post-type-uw_banner #titlediv .inside {
                        display: block !important;
                    }
                    
                    .post-type-uw_banner #titlediv .inside p {
                        margin-top: 8px;
                        font-style: italic;
                        color: #666;
                    }
                    
                    .post-type-uw_banner #postimagediv .inside p {
                        margin-top: 8px;
                        font-style: italic;
                        color: #ff0000;
                        font-weight: bold;
                    }
                    
                    .post-type-uw_banner .postbox-header {
                        background-color: #f0f6fc;
                    }
                    
                    .post-type-uw_banner .postbox-header h2 {
                        font-weight: bold;
                    }
                </style>';
            }
        }
        
        /**
         * Úprava admin menu pro lepší přehlednost
         */
        public function modify_admin_menu() {
            global $menu;
            
            // Zvýraznění položky menu pro bannery
            foreach ($menu as $key => $item) {
                if (isset($item[2]) && $item[2] === 'edit.php?post_type=uw_banner') {
                    $menu[$key][0] = $menu[$key][0] . ' <span class="update-plugins count-1"><span class="plugin-count">✓</span></span>';
                }
            }
        }
        
        /**
         * Zobrazení upozornění v administraci
         */
        public function display_admin_notices() {
            $screen = get_current_screen();
            
            if ('uw_banner' === $screen->post_type && 'add' === $screen->action) {
                echo '<div class="notice notice-info is-dismissible">
                    <p><strong>Tip:</strong> Nezapomeňte nastavit typ banneru a pozice zobrazení.</p>
                    <p>Pro <strong>Square banner (300x300 px)</strong> můžete vybrat zobrazení:</p>
                    <ul style="list-style-type: disc; margin-left: 20px;">
                        <li><strong>vedle carouselu</strong> (max 2 bannery)</li>
                        <li><strong>v postranním panelu</strong> (sidebar)</li>
                        <li><strong>v článcích</strong></li>
                    </ul>
                    <p>Pro <strong>Leaderboard banner (745x100 px)</strong> můžete vybrat zobrazení ve spodní části homepage.</p>
                </div>';
            }
        }
        
        /**
         * Definice sloupců pro výpis bannerů v administraci
         */
        public function banner_columns($columns) {
            $new_columns = array();
            
            // Přeskupení sloupců pro lepší přehlednost
            $new_columns['cb'] = $columns['cb'];
            $new_columns['title'] = $columns['title'];
            $new_columns['banner_preview'] = __('Náhled', 'dumabyt');
            $new_columns['banner_type'] = __('Typ', 'dumabyt');
            $new_columns['banner_position'] = __('Pozice', 'dumabyt');
            $new_columns['banner_dates'] = __('Období zobrazení', 'dumabyt');
            $new_columns['date'] = $columns['date'];
            
            return $new_columns;
        }
        
        /**
         * Obsah vlastních sloupců
         */
        public function banner_column_content($column, $post_id) {
            switch ($column) {
                case 'banner_preview':
                    if (has_post_thumbnail($post_id)) {
                        echo '<div style="max-width: 100px; max-height: 60px; overflow: hidden;">';
                        echo get_the_post_thumbnail($post_id, 'thumbnail', array('style' => 'max-width: 100%; height: auto;'));
                        echo '</div>';
                    } else {
                        echo '<div style="color: #999; font-style: italic;">' . __('Bez obrázku', 'dumabyt') . '</div>';
                    }
                    break;
                    
                case 'banner_type':
                    $type = get_post_meta($post_id, 'banner_type', true);
                    
                    if ('square' === $type) {
                        echo '<span style="color: #0073aa;"><span class="dashicons dashicons-format-image"></span> Square</span>';
                        echo '<div style="font-size: 11px; color: #666;">300×300 px</div>';
                    } elseif ('leaderboard' === $type) {
                        echo '<span style="color: #46b450;"><span class="dashicons dashicons-align-wide"></span> Leaderboard</span>';
                        echo '<div style="font-size: 11px; color: #666;">745×100 px</div>';
                    } else {
                        echo '<span style="color: #999; font-style: italic;">' . __('Neurčeno', 'dumabyt') . '</span>';
                    }
                    break;
                    
                case 'banner_position':
                    $positions = get_post_meta($post_id, 'banner_positions', false);
                    
                    if (!empty($positions)) {
                        $labels = array(
                            'carousel_sidebar' => '<span style="color: #9900cc;"><span class="dashicons dashicons-slides"></span> Vedle carouselu</span>',
                            'homepage_sidebar' => '<span style="color: #0073aa;"><span class="dashicons dashicons-align-right"></span> Sidebar</span>',
                            'homepage_bottom' => '<span style="color: #46b450;"><span class="dashicons dashicons-align-center"></span> Homepage bottom</span>',
                            'article' => '<span style="color: #FF4500;"><span class="dashicons dashicons-media-document"></span> Články</span>'
                        );
                        
                        foreach ($positions as $pos) {
                            if (isset($labels[$pos])) {
                                echo $labels[$pos] . '<br>';
                            }
                        }
                    } else {
                        echo '<span style="color: #999; font-style: italic;">' . __('Nenastaveno', 'dumabyt') . '</span>';
                    }
                    break;
                    
                case 'banner_dates':
                    $date_from = get_post_meta($post_id, 'banner_date_from', true);
                    $date_to = get_post_meta($post_id, 'banner_date_to', true);
                    
                    if (empty($date_from) && empty($date_to)) {
                        echo '<span style="color: #46b450;"><span class="dashicons dashicons-yes"></span> ' . __('Stále aktivní', 'dumabyt') . '</span>';
                    } else {
                        if (!empty($date_from)) {
                            echo '<div><span style="font-weight: bold;">Od:</span> ' . date_i18n(get_option('date_format'), strtotime($date_from)) . '</div>';
                        }
                        
                        if (!empty($date_to)) {
                            $today = date('Y-m-d');
                            $is_expired = $date_to < $today;
                            
                            echo '<div><span style="font-weight: bold;">Do:</span> ';
                            if ($is_expired) {
                                echo '<span style="color: #dc3232;">';
                            }
                            echo date_i18n(get_option('date_format'), strtotime($date_to));
                            if ($is_expired) {
                                echo ' <span class="dashicons dashicons-no-alt"></span></span>';
                            }
                            echo '</div>';
                        }
                    }
                    break;
            }
        }
        
        /**
         * Nastavení řaditelných sloupců
         */
        public function banner_sortable_columns($columns) {
            $columns['banner_type'] = 'banner_type';
            return $columns;
        }
        
        /**
         * Přidání filtrů nad seznam bannerů
         */
        public function add_banner_filters($post_type) {
            if ('uw_banner' !== $post_type) {
                return;
            }
            
            // Filtr podle typu banneru
            $selected = isset($_GET['banner_type']) ? $_GET['banner_type'] : '';
            
            echo '<select name="banner_type">
                <option value="">' . __('Všechny typy', 'dumabyt') . '</option>
                <option value="square"' . selected($selected, 'square', false) . '>' . __('Square (300×300 px)', 'dumabyt') . '</option>
                <option value="leaderboard"' . selected($selected, 'leaderboard', false) . '>' . __('Leaderboard (745×100 px)', 'dumabyt') . '</option>
            </select>';
            
            // Filtr podle pozice
            $selected_pos = isset($_GET['banner_position']) ? $_GET['banner_position'] : '';
            
            echo '<select name="banner_position">
                <option value="">' . __('Všechny pozice', 'dumabyt') . '</option>
                <option value="carousel_sidebar"' . selected($selected_pos, 'carousel_sidebar', false) . '>' . __('Vedle carouselu', 'dumabyt') . '</option>
                <option value="homepage_sidebar"' . selected($selected_pos, 'homepage_sidebar', false) . '>' . __('Homepage sidebar', 'dumabyt') . '</option>
                <option value="homepage_bottom"' . selected($selected_pos, 'homepage_bottom', false) . '>' . __('Homepage bottom', 'dumabyt') . '</option>
                <option value="article"' . selected($selected_pos, 'article', false) . '>' . __('Články', 'dumabyt') . '</option>
            </select>';
        }
        
        /**
         * Filtrace dotazu podle vybraných filtrů
         */
        public function filter_banner_query($query) {
            global $pagenow;
            
            if (is_admin() && 'edit.php' === $pagenow && isset($_GET['post_type']) && 'uw_banner' === $_GET['post_type']) {
                $meta_query = array();
                
                // Filtr podle typu
                if (isset($_GET['banner_type']) && !empty($_GET['banner_type'])) {
                    $meta_query[] = array(
                        'key'     => 'banner_type',
                        'value'   => $_GET['banner_type'],
                        'compare' => '='
                    );
                }
                
                // Filtr podle pozice
                if (isset($_GET['banner_position']) && !empty($_GET['banner_position'])) {
                    $meta_query[] = array(
                        'key'     => 'banner_positions',
                        'value'   => $_GET['banner_position'],
                        'compare' => 'LIKE'
                    );
                }
                
                // Přidání meta_query do hlavního dotazu
                if (!empty($meta_query)) {
                    $query->set('meta_query', $meta_query);
                }
            }
            
            return $query;
        }
    }
}
