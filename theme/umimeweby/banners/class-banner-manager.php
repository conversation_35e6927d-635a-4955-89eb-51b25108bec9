<?php
/**
 * Třída pro správu bannerů a jejich pozic
 *
 * @package dumabyt
 */

if (!class_exists('BannerManager')) {

    /**
     * Třída pro správu bannerů
     */
    class BannerManager {
        /**
         * Instance této třídy
         *
         * @var BannerManager
         */
        private static $instance = null;
        
        /**
         * Získání instance třídy (Singleton pattern)
         *
         * @return BannerManager Instance třídy
         */
        public static function get_instance() {
            if (null === self::$instance) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * Konstruktor třídy.
         */
        private function __construct() {
            // Inicializace hooks
            add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
        }
        
        /**
         * Načtení admin skriptů pro validaci bannerů
         */
        public function admin_scripts($hook) {
            global $post_type;
            
            if ('uw_banner' !== $post_type) {
                return;
            }
            
            // Skripty již byly načteny v t<PERSON><PERSON><PERSON><PERSON> BannerMetabox
        }
        
        /**
         * Získání bannerů podle pozice a dalš<PERSON>ch kritérií
         *
         * @param string $position Pozice banneru (homepage_sidebar, homepage_bottom, article)
         * @param int    $count    Počet bannerů k získání (výchozí: 1)
         * @param array  $args     Další argumenty pro WP_Query
         * @return array           Pole objektů bannerů (WP_Post)
         */
        public function get_banners($position, $count = 1, $args = array()) {
            $today = date('Y-m-d');
            
            // Vylepšení pro získání pouze bannerů určených pro danou pozici
            // Toto řeší problém, kdy se bannery z homepage zobrazují i v článcích
            $position_query = array(
                'key'     => 'banner_positions',
                'value'   => $position,
                'compare' => 'LIKE',
            );
            
            // Pokud žádáme o carousel bannery, prioritizujeme je a omezíme na max 2
            if ($position === 'carousel_sidebar') {
                $position_query = array(
                    'relation' => 'AND',
                    array(
                        'key'     => 'banner_positions',
                        'value'   => 'carousel_sidebar',
                        'compare' => 'LIKE',
                    ),
                    // Vyloučíme bannery, které jsou označeny i pro jiné pozice
                    array(
                        'key'     => 'banner_positions',
                        'value'   => 'article',
                        'compare' => 'NOT LIKE',
                    ),
                );
                
                // Omezíme počet na maximálně 2 pro carousel
                $count = min(2, absint($count));
            }
            // Pokud žádáme o homepage bannery, vyloučíme ty, které jsou označeny i pro články
            else if ($position === 'homepage_sidebar') {
                $position_query = array(
                    'relation' => 'AND',
                    array(
                        'key'     => 'banner_positions',
                        'value'   => 'homepage_sidebar',
                        'compare' => 'LIKE',
                    ),
                    array(
                        'key'     => 'banner_positions',
                        'value'   => 'article',
                        'compare' => 'NOT LIKE',
                    ),
                    array(
                        'key'     => 'banner_positions',
                        'value'   => 'carousel_sidebar',
                        'compare' => 'NOT LIKE',
                    ),
                );
            } else if ($position === 'homepage_bottom') {
                $position_query = array(
                    'relation' => 'AND',
                    array(
                        'key'     => 'banner_positions',
                        'value'   => 'homepage_bottom',
                        'compare' => 'LIKE',
                    ),
                    array(
                        'key'     => 'banner_positions',
                        'value'   => 'article',
                        'compare' => 'NOT LIKE',
                    ),
                    array(
                        'key'     => 'banner_positions',
                        'value'   => 'carousel_sidebar',
                        'compare' => 'NOT LIKE',
                    ),
                );
            }
            
            $query_args = array(
                'post_type'      => 'uw_banner',
                'posts_per_page' => absint($count),
                'orderby'        => 'rand', // Náhodné pořadí pro rovnoměrnou rotaci
                'meta_query'     => array(
                    'relation' => 'AND',
                    // Banner musí mít požadovanou pozici a případná další omezení
                    $position_query,
                    // Omezení podle data od (pokud je vyplněno)
                    array(
                        'relation' => 'OR',
                        array(
                            'key'     => 'banner_date_from',
                            'value'   => $today,
                            'compare' => '<=',
                            'type'    => 'DATE',
                        ),
                        array(
                            'key'     => 'banner_date_from',
                            'value'   => '',
                            'compare' => '=',
                        ),
                        array(
                            'key'     => 'banner_date_from',
                            'compare' => 'NOT EXISTS',
                        ),
                    ),
                    // Omezení podle data do (pokud je vyplněno)
                    array(
                        'relation' => 'OR',
                        array(
                            'key'     => 'banner_date_to',
                            'value'   => $today,
                            'compare' => '>=',
                            'type'    => 'DATE',
                        ),
                        array(
                            'key'     => 'banner_date_to',
                            'value'   => '',
                            'compare' => '=',
                        ),
                        array(
                            'key'     => 'banner_date_to',
                            'compare' => 'NOT EXISTS',
                        ),
                    ),
                ),
            );
            
            // Spojení vlastních argumentů s výchozími
            $query_args = array_merge($query_args, $args);
            
            // Provedení dotazu
            $banner_query = new WP_Query($query_args);
            
            return $banner_query->posts;
        }
        
        /**
         * Získání banneru podle ID s kontrolou platnosti
         *
         * @param int $banner_id ID banneru
         * @return WP_Post|null  Banner objekt nebo null
         */
        public function get_banner_by_id($banner_id) {
            if (empty($banner_id)) {
                return null;
            }
            
            $today = date('Y-m-d');
            
            $query_args = array(
                'post_type'      => 'uw_banner',
                'posts_per_page' => 1,
                'p'              => $banner_id,
                'meta_query'     => array(
                    'relation' => 'AND',
                    // Omezení podle data od (pokud je vyplněno)
                    array(
                        'relation' => 'OR',
                        array(
                            'key'     => 'banner_date_from',
                            'value'   => $today,
                            'compare' => '<=',
                            'type'    => 'DATE',
                        ),
                        array(
                            'key'     => 'banner_date_from',
                            'value'   => '',
                            'compare' => '=',
                        ),
                        array(
                            'key'     => 'banner_date_from',
                            'compare' => 'NOT EXISTS',
                        ),
                    ),
                    // Omezení podle data do (pokud je vyplněno)
                    array(
                        'relation' => 'OR',
                        array(
                            'key'     => 'banner_date_to',
                            'value'   => $today,
                            'compare' => '>=',
                            'type'    => 'DATE',
                        ),
                        array(
                            'key'     => 'banner_date_to',
                            'value'   => '',
                            'compare' => '=',
                        ),
                        array(
                            'key'     => 'banner_date_to',
                            'compare' => 'NOT EXISTS',
                        ),
                    ),
                ),
            );
            
            $banner_query = new WP_Query($query_args);
            
            if ($banner_query->have_posts()) {
                return $banner_query->posts[0];
            }
            
            return null;
        }
        
        /**
         * Získání URL banneru
         *
         * @param int $banner_id ID banneru
         * @return string        URL odkazu banneru
         */
        public function get_banner_url($banner_id) {
            $url = get_post_meta($banner_id, 'banner_url', true);
            return !empty($url) ? esc_url($url) : '#';
        }
        
        /**
         * Získání typu banneru
         *
         * @param int $banner_id ID banneru
         * @return string        Typ banneru (square, leaderboard)
         */
        public function get_banner_type($banner_id) {
            $type = get_post_meta($banner_id, 'banner_type', true);
            return !empty($type) ? $type : 'square';
        }
        
        /**
         * Tracking kód byl odstraněn
         */
    }
}
