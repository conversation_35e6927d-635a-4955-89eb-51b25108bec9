<?php
/**
 * Třída pro integraci s pluginem MetaBox pro správu metadat bannerů
 *
 * @package dumabyt
 */

if (!class_exists('BannerMetabox')) {

    /**
     * Třída pro správu metadat bannerů pomocí pluginu MetaBox
     */
    class BannerMetabox {
        /**
         * Instance této třídy
         *
         * @var BannerMetabox
         */
        private static $instance = null;
        
        /**
         * Získání instance třídy (Singleton pattern)
         *
         * @return BannerMetabox Instance třídy
         */
        public static function get_instance() {
            if (null === self::$instance) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * Konstruktor třídy.
         */
        private function __construct() {
            // Přidání priority 20, aby byla jistota, že se hook spustí později
            add_filter('rwmb_meta_boxes', array($this, 'register_meta_boxes'), 20);
            add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
            add_action('admin_init', array($this, 'debug_metabox_registration'));
            
            // Přidání akce pro vymazání cache při aktivaci/deaktivaci pluginů
            add_action('activated_plugin', array($this, 'clear_metabox_cache'));
            add_action('deactivated_plugin', array($this, 'clear_metabox_cache'));
            
            // Vymazání cache při inicializaci (pro jistotu)
            $this->clear_metabox_cache();
        }
        
        /**
         * Vymazání cache pro meta boxy
         */
        public function clear_metabox_cache() {
            if (function_exists('wp_cache_delete')) {
                wp_cache_delete('meta-boxes', 'meta-boxes');
            }
            
            // Poznámka: Admin notices byly odstraněny pro čistější rozhraní
        }
        
        /**
         * Diagnostika registrace metaboxů
         */
        public function debug_metabox_registration() {
            if (WP_DEBUG && is_admin()) {
                // Kontrola, zda funkce rwmb_meta existuje
                if (!function_exists('rwmb_meta')) {
                    dumabyt_add_debug_notice('MetaBox API není dostupné, meta boxy nebudou fungovat.', 'error');
                }
                // Poznámka: Úspěšná hláška byla odstraněna pro čistější rozhraní
            }
        }
        
        /**
         * Načtení admin JS a CSS pro validaci a chování meta boxů
         */
        public function admin_scripts($hook) {
            $screen = get_current_screen();
            if ('uw_banner' === $screen->post_type) {
                wp_enqueue_style(
                    'uw-banner-admin', 
                    get_template_directory_uri() . '/umimeweby/assets/css/admin-banner.css',
                    array(),
                    '1.0.0'
                );
                
                wp_enqueue_script(
                    'uw-banner-admin', 
                    get_template_directory_uri() . '/umimeweby/assets/js/admin-banner.js',
                    array('jquery'),
                    '1.0.0',
                    true
                );
            }
        }
        
        /**
         * Registrace meta boxů pro bannery
         * 
         * @param array $meta_boxes Existující meta boxy
         * @return array Aktualizované meta boxy
         */
        public function register_meta_boxes($meta_boxes) {
            // Meta box pro nastavení banneru
            $meta_boxes[] = array(
                'title'      => __('Nastavení banneru', 'dumabyt'),
                'id'         => 'banner_settings',
                'post_types' => array('uw_banner'),
                'context'    => 'normal',
                'priority'   => 'high',
                'fields'     => array(
                    array(
                        'name'     => __('Typ banneru', 'dumabyt'),
                        'id'       => 'banner_type',
                        'type'     => 'select',
                        'options'  => array(
                            'square'      => __('Square (300x300 px)', 'dumabyt'),
                            'leaderboard' => __('Leaderboard (745x100 px)', 'dumabyt')
                        ),
                        'required' => true,
                        'desc'     => __('Vyberte typ banneru, který určí jeho rozměry.', 'dumabyt'),
                    ),
                    array(
                        'name'     => __('URL odkazu', 'dumabyt'),
                        'id'       => 'banner_url',
                        'type'     => 'url',
                        'required' => true,
                        'desc'     => __('Zadejte URL, na kterou bude banner odkazovat.', 'dumabyt'),
                    ),
                    array(
                        'name'    => __('Pozice', 'dumabyt'),
                        'id'      => 'banner_positions',
                        'type'    => 'checkbox_list',
                        'options' => BannerConfig::get_positions(),
                        'inline' => false,
                        'select_all_none' => true,
                        'desc'    => __('Vyberte pozice, kde se má banner zobrazovat.', 'dumabyt'),
                        'after'   => '<div class="banner-warning" style="color: #d63638; margin-top: 10px;"><p><strong>⚠️ Důležité:</strong> ' . __('Každý banner by měl být zobrazen pouze na <strong>jedné konkrétní pozici</strong>.', 'dumabyt') . '</p></div>',
                    ),
                    array(
                        'name' => __('Aktivní od', 'dumabyt'),
                        'id'   => 'banner_date_from',
                        'type' => 'date',
                        'desc' => __('Datum, od kterého se má banner zobrazovat. Ponechte prázdné pro okamžité zobrazení.', 'dumabyt'),
                    ),
                    array(
                        'name' => __('Aktivní do', 'dumabyt'),
                        'id'   => 'banner_date_to',
                        'type' => 'date',
                        'desc' => __('Datum, do kterého se má banner zobrazovat. Ponechte prázdné pro neomezené zobrazení.', 'dumabyt'),
                    )
                )
            );
            
            // Meta box pro výběr banneru v článcích
            $meta_boxes[] = array(
                'title'      => __('Bannery v článku', 'dumabyt'),
                'id'         => 'article_banners',
                'post_types' => array('post', 'page'),
                'context'    => 'side',
                'priority'   => 'low',
                'fields'     => array(
                    array(
                        'name'        => __('Výběr banneru', 'dumabyt'),
                        'id'          => 'article_banner_id',
                        'type'        => 'post',
                        'post_type'   => 'uw_banner',
                        'field_type'  => 'select_advanced',
                        'placeholder' => __('Vyberte banner', 'dumabyt'),
                        'query_args'  => array(
                            'meta_query' => array(
                                array(
                                    'key'     => 'banner_positions',
                                    'value'   => 'article',
                                    'compare' => 'LIKE'
                                )
                            )
                        ),
                        'desc'        => __('Vyberte banner, který se zobrazí v tomto článku.', 'dumabyt'),
                    )
                )
            );
            
            return $meta_boxes;
        }
    }
}
