<?php
/**
 * Třída pro vykreslování bannerů na různých pozicích
 *
 * @package dumabyt
 */

if (!class_exists('BannerRenderer')) {

    /**
     * Třída pro vykreslování bannerů na různých pozicích
     */
    class BannerRenderer {
        /**
         * Instance této třídy
         *
         * @var BannerRenderer
         */
        private static $instance = null;
        
        /**
         * Reference na třídu BannerManager
         *
         * @var BannerManager
         */
        private $manager;
        
        /**
         * Získání instance třídy (Singleton pattern)
         *
         * @return BannerRenderer Instance třídy
         */
        public static function get_instance() {
            if (null === self::$instance) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * Konstruktor třídy.
         */
        private function __construct() {
            $this->manager = BannerManager::get_instance();
            
            // Přidání akcí pro vykreslování bannerů na různých pozicích
            add_action('homepage_sidebar_banners', array($this, 'render_homepage_sidebar_banners'));
            add_action('carousel_sidebar_banners', array($this, 'render_carousel_sidebar_banners')); // Nová akce pro bannery vedle carouselu
            add_action('article_sidebar_banners', array($this, 'render_article_sidebar_banners')); 
            add_action('homepage_bottom_banner', array($this, 'render_homepage_bottom_banner'));
            add_filter('the_content', array($this, 'insert_article_banners'));
            
            // Přidání stylů pro bannery
            add_action('wp_enqueue_scripts', array($this, 'enqueue_banner_styles'));
        }
        
        /**
         * Načtení stylů pro bannery
         */
        public function enqueue_banner_styles() {
            wp_enqueue_style(
                'uw-banner-styles',
                get_template_directory_uri() . '/umimeweby/assets/css/banner-styles.css',
                array(),
                '1.0.0'
            );
        }
        
        /**
         * Vykreslení bannerů v postranním panelu na homepage
         * 
         * @param int $count Počet bannerů k zobrazení
         */
        public function render_homepage_sidebar_banners($count = 3) {
            // Vylepšení - explicitně používáme zde pouze homepage bannery
            $banners = $this->manager->get_banners('homepage_sidebar', $count);
            
            if (empty($banners)) {
                $this->render_placeholder('square');
                return;
            }
            
            foreach ($banners as $banner) {
                // Ověříme, že typ banneru je square
                if ($this->manager->get_banner_type($banner->ID) === 'square') {
                    $this->render_banner($banner, 'square');
                } else {
                    // Pokud není správný typ, použijeme placeholder
                    $this->render_placeholder('square');
                }
            }
        }
        
        /**
         * Vykreslení bannerů vedle carouselu na homepage
         * 
         * @param int $count Počet bannerů k zobrazení (maximálně 2)
         */
        public function render_carousel_sidebar_banners($count = 2) {
            // Omezení na maximálně 2 bannery
            $count = min(2, absint($count));
            
            // Získání bannerů specificky pro pozici vedle carouselu
            $banners = $this->manager->get_banners('carousel_sidebar', $count);
            
            if (empty($banners)) {
                for ($i = 0; $i < $count; $i++) {
                    $this->render_placeholder('square');
                }
                return;
            }
            
            foreach ($banners as $banner) {
                // Ověříme, že typ banneru je square
                if ($this->manager->get_banner_type($banner->ID) === 'square') {
                    $this->render_banner($banner, 'square');
                } else {
                    // Pokud není správný typ, použijeme placeholder
                    $this->render_placeholder('square');
                }
            }
            
            // Pokud nemáme dostatek bannerů, doplníme placeholdery
            $remaining = $count - count($banners);
            for ($i = 0; $i < $remaining; $i++) {
                $this->render_placeholder('square');
            }
        }
        
        /**
         * Vykreslení bannerů v postranním panelu v článcích
         * 
         * @param int $count Počet bannerů k zobrazení
         */
        public function render_article_sidebar_banners($count = 3) {
            // Vytvořil jsem novou metodu pro bannery v článcích
            // Tato metoda používá bannery pouze s pozicí 'article'
            $banners = $this->manager->get_banners('article', $count);
            
            if (empty($banners)) {
                $this->render_placeholder('square');
                return;
            }
            
            foreach ($banners as $banner) {
                // Ověříme, že typ banneru je square
                if ($this->manager->get_banner_type($banner->ID) === 'square') {
                    $this->render_banner($banner, 'square');
                } else {
                    // Pokud není správný typ, použijeme placeholder
                    $this->render_placeholder('square');
                }
            }
        }
        
        /**
         * Vykreslení banneru ve spodní části homepage
         */
        public function render_homepage_bottom_banner() {
            $banners = $this->manager->get_banners('homepage_bottom', 1);
            
            if (empty($banners)) {
                $this->render_placeholder('leaderboard');
                return;
            }
            
            // Ověříme, že typ banneru je leaderboard
            if ($this->manager->get_banner_type($banners[0]->ID) === 'leaderboard') {
                $this->render_banner($banners[0], 'leaderboard');
            } else {
                // Pokud není správný typ, použijeme placeholder
                $this->render_placeholder('leaderboard');
            }
        }
        
        /**
         * Vkládání bannerů do obsahu článků
         * 
         * @param string $content Obsah článku
         * @return string Obsah s vloženými bannery
         */
        public function insert_article_banners($content) {
            // DŮLEŽITÉ: Square a Leaderboard bannery se nemají zobrazovat v obsahu článků
            // Tato funkce by neměla vkládat žádné bannery do obsahu článku
            
            // Vrátíme content beze změny - nezobrazujeme bannery v obsahu
            return $content;
            
            /* ZAKÁZÁNO - Bannery v obsahu článku již nejsou podporovány
            
            if (!is_single() && !is_page()) {
                return $content;
            }
            
            $post_id = get_the_ID();
            $banner_ids = get_post_meta($post_id, 'article_banner_ids', false);
            
            // Pro zpětnou kompatibilitu zkontrolujeme i staré pole
            $legacy_banner_id = get_post_meta($post_id, 'article_banner_id', true);
            if (!empty($legacy_banner_id) && !in_array($legacy_banner_id, $banner_ids)) {
                $banner_ids[] = $legacy_banner_id;
            }
            
            // Získáme pouze bannery určené výhradně pro články
            // Nebudeme brát bannery z homepage, i když jsou označeny pro články
            if (empty($banner_ids)) {
                // Vylepšený dotaz - vyžaduje bannery pouze s pozicí 'article' a žádnou jinou pozicí
                $article_only_banners = new WP_Query(array(
                    'post_type'      => 'uw_banner',
                    'posts_per_page' => 3,
                    'orderby'        => 'rand',
                    'meta_query'     => array(
                        'relation' => 'AND',
                        // Musí mít pozici 'article'
                        array(
                            'key'     => 'banner_positions',
                            'value'   => 'article',
                            'compare' => 'LIKE',
                        ),
                        // A NESMÍ mít pozici 'homepage_sidebar'
                        array(
                            'key'     => 'banner_positions',
                            'value'   => 'homepage_sidebar',
                            'compare' => 'NOT LIKE',
                        ),
                        // A NESMÍ mít pozici 'homepage_bottom'
                        array(
                            'key'     => 'banner_positions',
                            'value'   => 'homepage_bottom',
                            'compare' => 'NOT LIKE',
                        ),
                    ),
                ));
                
                if ($article_only_banners->have_posts()) {
                    foreach ($article_only_banners->posts as $banner) {
                        $banner_ids[] = $banner->ID;
                    }
                }
            }
            
            if (empty($banner_ids)) {
                return $content;
            }
            
            // Rozdělení článku na odstavce
            $paragraphs = explode('</p>', $content);
            $total_paragraphs = count($paragraphs);
            
            // Pozice pro vložení bannerů - po 3., uprostřed a před koncem
            $insert_positions = array();
            
            if ($total_paragraphs >= 4) {
                $insert_positions[] = 2; // Po 3. odstavci (index 2)
            }
            
            if ($total_paragraphs >= 8) {
                $insert_positions[] = intval($total_paragraphs / 2); // Uprostřed
            }
            
            if ($total_paragraphs >= 12) {
                $insert_positions[] = $total_paragraphs - 3; // 3 od konce
            }
            
            // Pokud nemáme žádné pozice, vložíme banner na konec
            if (empty($insert_positions)) {
                $modified_content = $content;
                
                // Vložíme první banner na konec
                if (!empty($banner_ids[0])) {
                    $banner = $this->manager->get_banner_by_id($banner_ids[0]);
                    if (!empty($banner)) {
                        $banner_html = $this->get_banner_html($banner);
                        $modified_content .= $banner_html;
                    }
                }
                
                return $modified_content;
            }
            
            // Vložení bannerů na vypočítané pozice
            $banner_index = 0;
            
            foreach ($insert_positions as $position) {
                if ($banner_index >= count($banner_ids)) {
                    break; // Nemáme další bannery k vložení
                }
                
                $banner = $this->manager->get_banner_by_id($banner_ids[$banner_index]);
                
                if (!empty($banner)) {
                    $banner_html = $this->get_banner_html($banner);
                    $paragraphs[$position] .= $banner_html;
                    $banner_index++;
                }
            }
            
            return implode('</p>', $paragraphs);
            */
        }
        
        /**
         * Vykreslení banneru podle objektu
         * 
         * @param WP_Post $banner Banner objekt
         * @param string  $type   Volitelný typ banneru (přepíše typ z meta)
         */
        public function render_banner($banner, $type = null) {
            echo $this->get_banner_html($banner, $type);
        }
        
        /**
         * Vykreslení placeholder banneru
         * 
         * @param string $type Typ banneru (square, leaderboard)
         */
        public function render_placeholder($type) {
            echo $this->get_placeholder_html($type);
        }
        
        /**
         * Získání HTML kódu banneru
         * 
         * @param WP_Post $banner Banner objekt
         * @param string  $type   Volitelný typ banneru (přepíše typ z meta)
         * @return string HTML kód banneru
         */
        private function get_banner_html($banner, $type = null) {
            if (empty($type)) {
                $type = $this->manager->get_banner_type($banner->ID);
            }
            
            $url = $this->manager->get_banner_url($banner->ID);
            // Tracking kód byl odstraněn
            
            ob_start();
            include get_template_directory() . '/umimeweby/templates/banner-' . $type . '.php';
            return ob_get_clean();
        }
        
        /**
         * Získání HTML kódu placeholderu
         * 
         * @param string $type Typ banneru (square, leaderboard)
         * @return string HTML kód placeholderu
         */
        private function get_placeholder_html($type) {
            ob_start();
            include get_template_directory() . '/umimeweby/templates/banner-' . $type . '-placeholder.php';
            return ob_get_clean();
        }
    }
}
