<?php
/**
 * The template for displaying search results pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#search-result
 *
 * @package dumabyt
 */

get_header();

// Get search query
$search_query = get_search_query();

// Set default color classes for search results
$color_class = 'bg-blue-600';
$gradient_class = 'from-blue-500 to-blue-700';
$text_color = 'text-blue-600';
$text_hover_color = 'hover:text-blue-700';

// Get featured post (most recent)
$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
$posts_per_page = 9; // 3x3 grid

// Calculate total for pagination
global $wp_query;
$total_posts = $wp_query->found_posts;
$total_pages = ceil($total_posts / $posts_per_page);
?>

<main id="primary" class="site-main bg-gray-50 py-8">
    <div class="container mx-auto px-4">
        <!-- Search Header -->
        <div class="bg-gradient-to-br <?php echo $gradient_class; ?> rounded-xl overflow-hidden mb-8 text-white p-8 relative">
            <div class="absolute inset-0 bg-black/20"></div>
            <div class="relative z-10">
                <h1 class="text-3xl md:text-4xl font-bold mb-4 font-trajan">
                    <?php
                    printf(
                        /* translators: %s: search query */
                        esc_html__('Výsledky vyhledávání: %s', '_uw-theme'),
                        '<span class="font-bold">' . esc_html($search_query) . '</span>'
                    );
                    ?>
                </h1>
                <div class="text-white/90 max-w-3xl mb-4">
                    <?php
                    printf(
                        /* translators: %d: number of results */
                        esc_html(_n('Nalezen %d výsledek', 'Nalezeno %d výsledků', $total_posts, '_uw-theme')),
                        number_format_i18n($total_posts)
                    );
                    ?>
                </div>

                <!-- Search form in header -->
                <div class="mt-6 max-w-xl">
                    <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                        <div class="relative">
                            <input type="search" class="search-field w-full py-3 pl-4 pr-12 rounded-full bg-white/90 border border-white/20 focus:border-white focus:ring-2 focus:ring-white/30 focus:outline-none transition-all duration-300 text-gray-800" placeholder="<?php echo esc_attr_x('Hledat znovu...', 'placeholder', '_uw-theme'); ?>" value="<?php echo esc_attr($search_query); ?>" name="s" />
                            <button type="submit" class="search-submit absolute right-3 top-1/2 transform -translate-y-1/2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <span class="sr-only"><?php echo _x('Search', 'submit button', '_uw-theme'); ?></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Main content - 3 columns wide -->
            <div class="lg:col-span-3">
                <?php if (have_posts()) : ?>
                    <!-- Grid of Posts -->
                    <div class="mb-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php
                            while (have_posts()) :
                                the_post();

                                // Get post category for color
                                $categories = get_the_category();
                                if ($categories) {
                                    $category = $categories[0]; // Get the first category
                                    $cat_slug = $category->slug;
                                    $cat_name = $category->name;
                                    $cat_url = get_category_link($category->term_id);

                                    // Get category color
                                    if (function_exists('dumabyt_get_category_color')) {
                                        $color_class = dumabyt_get_category_color($cat_slug);
                                        $text_color = str_replace('bg-', 'text-', $color_class);
                                        $text_hover_color = str_replace('bg-', 'hover:text-', $color_class);
                                    }
                                }

                                // Include the search result card template
                                get_template_part('template-parts/content/content', 'search-card', array(
                                    'category_name' => isset($cat_name) ? $cat_name : '',
                                    'category_url' => isset($cat_url) ? $cat_url : '',
                                    'color_class' => $color_class,
                                    'text_color' => $text_color,
                                    'text_hover_color' => $text_hover_color
                                ));
                            endwhile;
                            ?>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1) : ?>
                        <div class="mt-8 flex justify-center">
                            <div class="inline-flex rounded-md shadow-sm">
                                <?php
                                // Previous page
                                if ($paged > 1) :
                                    $prev_link = get_pagenum_link($paged - 1);
                                ?>
                                    <a href="<?php echo esc_url($prev_link); ?>" class="relative inline-flex items-center rounded-l-md px-3 py-2 text-gray-600 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">
                                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                        </svg>
                                        <span class="sr-only">Předchozí</span>
                                    </a>
                                <?php endif; ?>

                                <?php
                                // Page numbers
                                $start_page = max(1, min($paged - 2, $total_pages - 4));
                                $end_page = min($total_pages, max($paged + 2, 5));

                                for ($i = $start_page; $i <= $end_page; $i++) :
                                    $is_current = $paged == $i;
                                    $page_link = get_pagenum_link($i);
                                    $classes = $is_current
                                        ? "relative z-10 inline-flex items-center $color_class px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                                        : "relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0";
                                ?>
                                    <a href="<?php echo esc_url($page_link); ?>" class="<?php echo $classes; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endfor; ?>

                                <?php
                                // Next page
                                if ($paged < $total_pages) :
                                    $next_link = get_pagenum_link($paged + 1);
                                ?>
                                    <a href="<?php echo esc_url($next_link); ?>" class="relative inline-flex items-center rounded-r-md px-3 py-2 text-gray-600 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">
                                        <span class="sr-only">Další</span>
                                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php else : ?>
                    <!-- No results found -->
                    <div class="bg-white rounded-xl p-8 text-center">
                        <h2 class="text-xl font-bold mb-4 text-gray-800">Žádné výsledky</h2>
                        <p class="text-gray-600 mb-6">Pro hledaný výraz "<?php echo esc_html($search_query); ?>" nebyly nalezeny žádné výsledky.</p>

                        <div class="max-w-md mx-auto">
                            <h3 class="text-lg font-medium mb-3 text-gray-700">Zkuste prosím:</h3>
                            <ul class="text-left list-disc pl-6 mb-6 text-gray-600">
                                <li>Zkontrolovat pravopis zadaných slov</li>
                                <li>Použít jiná klíčová slova</li>
                                <li>Použít obecnější výrazy</li>
                                <li>Snížit počet slov v dotazu</li>
                            </ul>

                            <a href="<?php echo esc_url(home_url('/')); ?>" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                </svg>
                                Zpět na úvodní stránku
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar - 1 column wide -->
            <?php get_template_part('template-parts/layout/sidebar', 'partneri'); ?>
        </div>
    </div>
</main>

<?php
get_footer();
