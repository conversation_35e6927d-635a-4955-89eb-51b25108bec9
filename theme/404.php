<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @link https://codex.wordpress.org/Creating_an_Error_404_Page
 *
 * @package dumabyt
 */

get_header();
?>

<main id="primary" class="site-main bg-gray-50 py-8">
    <div class="container mx-auto px-4">
        <!-- 404 Header -->
        <div class="bg-gradient-to-br from-amber-500 to-amber-700 rounded-xl overflow-hidden mb-8 text-white p-8 relative">
            <div class="absolute inset-0 bg-black/20"></div>
            <div class="relative z-10">
                <h1 class="text-3xl md:text-4xl font-bold mb-4 font-trajan"><?php esc_html_e('Stránka nenalezena', '_uw-theme'); ?></h1>
                <div class="text-white/90 max-w-3xl mb-4">
                    <?php esc_html_e('Omlouváme se, ale požadovaná stránka nebyla nalezena. Možná byla odstraněna, přejmenována nebo nikdy neexistovala.', '_uw-theme'); ?>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Main content - 3 columns wide -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-xl p-8 text-center card-elevation">
                    <img src="<?php echo get_template_directory_uri(); ?>/images/404.svg" alt="404" class="w-48 h-48 mx-auto mb-6 opacity-75">

                    <h2 class="text-xl font-bold mb-4 text-gray-800">Co můžete zkusit?</h2>

                    <div class="max-w-md mx-auto">
                        <ul class="text-left list-disc pl-6 mb-6 text-gray-600">
                            <li>Zkontrolovat URL adresu, zda neobsahuje překlepy</li>
                            <li>Vrátit se na úvodní stránku</li>
                            <li>Použít vyhledávání pro nalezení požadovaného obsahu</li>
                        </ul>

                        <div class="mt-6 max-w-xl">
                            <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                                <div class="relative">
                                    <input type="search" class="search-field w-full py-3 pl-4 pr-12 rounded-full bg-gray-50 border border-gray-200 focus:border-amber-400 focus:ring-2 focus:ring-amber-100 focus:outline-none transition-all duration-300 text-gray-800" placeholder="<?php echo esc_attr_x('Vyhledat...', 'placeholder', '_uw-theme'); ?>" value="" name="s" />
                                    <button type="submit" class="search-submit absolute right-3 top-1/2 transform -translate-y-1/2">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                        <span class="sr-only"><?php echo _x('Search', 'submit button', '_uw-theme'); ?></span>
                                    </button>
                                </div>
                            </form>
                        </div>

                        <a href="<?php echo esc_url(home_url('/')); ?>" class="inline-flex items-center bg-amber-600 hover:bg-amber-700 text-white font-medium py-2 px-4 rounded-lg transition-colors mt-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            <?php esc_html_e('Zpět na úvodní stránku', '_uw-theme'); ?>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sidebar - 1 column wide -->
            <?php get_template_part('template-parts/layout/sidebar', 'partneri'); ?>
        </div>
    </div>
</main>

<?php
get_footer();
