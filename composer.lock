{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "9b00f3c8bae3fe8260fb9fa0180f856b", "packages": [], "packages-dev": [{"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/composer-installer.git", "reference": "4be43904336affa5c2f70744a348312336afd0da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/composer-installer/zipball/4be43904336affa5c2f70744a348312336afd0da", "reference": "4be43904336affa5c2f70744a348312336afd0da", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "ext-json": "*", "ext-zip": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0", "yoast/phpunit-polyfills": "^1.0"}, "type": "composer-plugin", "extra": {"class": "PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/PHPCSStandards/composer-installer/issues", "source": "https://github.com/PHPCSStandards/composer-installer"}, "time": "2023-01-05T11:28:13+00:00"}, {"name": "eftec/bladeone", "version": "3.52", "source": {"type": "git", "url": "https://github.com/EFTEC/BladeOne.git", "reference": "a19bf66917de0b29836983db87a455a4f6e32148"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/EFTEC/BladeOne/zipball/a19bf66917de0b29836983db87a455a4f6e32148", "reference": "a19bf66917de0b29836983db87a455a4f6e32148", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16.1", "phpunit/phpunit": "^5.7", "squizlabs/php_codesniffer": "^3.5.4"}, "suggest": {"eftec/bladeonehtml": "Extension to create forms", "ext-mbstring": "This extension is used if it's active"}, "type": "library", "autoload": {"psr-4": {"eftec\\bladeone\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The standalone version Blade Template Engine from Laravel in a single php file", "homepage": "https://github.com/EFTEC/BladeOne", "keywords": ["blade", "php", "template", "templating", "view"], "support": {"issues": "https://github.com/EFTEC/BladeOne/issues", "source": "https://github.com/EFTEC/BladeOne/tree/3.52"}, "time": "2021-04-17T13:49:01+00:00"}, {"name": "gettext/gettext", "version": "v4.8.12", "source": {"type": "git", "url": "https://github.com/php-gettext/Gettext.git", "reference": "11af89ee6c087db3cf09ce2111a150bca5c46e12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Gettext/zipball/11af89ee6c087db3cf09ce2111a150bca5c46e12", "reference": "11af89ee6c087db3cf09ce2111a150bca5c46e12", "shasum": ""}, "require": {"gettext/languages": "^2.3", "php": ">=5.4.0"}, "require-dev": {"illuminate/view": "^5.0.x-dev", "phpunit/phpunit": "^4.8|^5.7|^6.5", "squizlabs/php_codesniffer": "^3.0", "symfony/yaml": "~2", "twig/extensions": "*", "twig/twig": "^1.31|^2.0"}, "suggest": {"illuminate/view": "Is necessary if you want to use the Blade extractor", "symfony/yaml": "Is necessary if you want to use the Yaml extractor/generator", "twig/extensions": "Is necessary if you want to use the Twig extractor", "twig/twig": "Is necessary if you want to use the Twig extractor"}, "type": "library", "autoload": {"psr-4": {"Gettext\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "PHP gettext manager", "homepage": "https://github.com/oscarotero/Gettext", "keywords": ["JS", "gettext", "i18n", "mo", "po", "translation"], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/Gettext/issues", "source": "https://github.com/php-gettext/Gettext/tree/v4.8.12"}, "funding": [{"url": "https://paypal.me/oscarotero", "type": "custom"}, {"url": "https://github.com/oscarotero", "type": "github"}, {"url": "https://www.patreon.com/misteroom", "type": "patreon"}], "time": "2024-05-18T10:25:07+00:00"}, {"name": "gettext/languages", "version": "2.10.0", "source": {"type": "git", "url": "https://github.com/php-gettext/Languages.git", "reference": "4d61d67fe83a2ad85959fe6133d6d9ba7dddd1ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Languages/zipball/4d61d67fe83a2ad85959fe6133d6d9ba7dddd1ab", "reference": "4d61d67fe83a2ad85959fe6133d6d9ba7dddd1ab", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5 || ^8.4"}, "bin": ["bin/export-plural-rules"], "type": "library", "autoload": {"psr-4": {"Gettext\\Languages\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "gettext languages with plural rules", "homepage": "https://github.com/php-gettext/Languages", "keywords": ["cldr", "i18n", "internationalization", "l10n", "language", "languages", "localization", "php", "plural", "plural rules", "plurals", "translate", "translations", "unicode"], "support": {"issues": "https://github.com/php-gettext/Languages/issues", "source": "https://github.com/php-gettext/Languages/tree/2.10.0"}, "funding": [{"url": "https://paypal.me/mlocati", "type": "custom"}, {"url": "https://github.com/mlocati", "type": "github"}], "time": "2022-10-18T15:00:10+00:00"}, {"name": "mck89/peast", "version": "v1.16.3", "source": {"type": "git", "url": "https://github.com/mck89/peast.git", "reference": "645ec21b650bc2aced18285c85f220d22afc1430"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mck89/peast/zipball/645ec21b650bc2aced18285c85f220d22afc1430", "reference": "645ec21b650bc2aced18285c85f220d22afc1430", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.16.3-dev"}}, "autoload": {"psr-4": {"Peast\\": "lib/Peast/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Peast is PHP library that generates AST for JavaScript code", "support": {"issues": "https://github.com/mck89/peast/issues", "source": "https://github.com/mck89/peast/tree/v1.16.3"}, "time": "2024-07-23T14:00:32+00:00"}, {"name": "mustache/mustache", "version": "v2.14.2", "source": {"type": "git", "url": "https://github.com/bobthecow/mustache.php.git", "reference": "e62b7c3849d22ec55f3ec425507bf7968193a6cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/mustache.php/zipball/e62b7c3849d22ec55f3ec425507bf7968193a6cb", "reference": "e62b7c3849d22ec55f3ec425507bf7968193a6cb", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "~1.11", "phpunit/phpunit": "~3.7|~4.0|~5.0"}, "type": "library", "autoload": {"psr-0": {"Mustache": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "A Mustache implementation in PHP.", "homepage": "https://github.com/bobthecow/mustache.php", "keywords": ["mustache", "templating"], "support": {"issues": "https://github.com/bobthecow/mustache.php/issues", "source": "https://github.com/bobthecow/mustache.php/tree/v2.14.2"}, "time": "2022-08-23T13:07:01+00:00"}, {"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": ""}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}, "time": "2019-12-27T09:44:58+00:00"}, {"name": "phpcompatibility/phpcompatibility-paragonie", "version": "1.3.3", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie.git", "reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityParagonie/zipball/293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "paragonie/random_compat": "dev-master", "paragonie/sodium_compat": "dev-master"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A set of rulesets for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by the Paragonie polyfill libraries.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "paragonie", "phpcs", "polyfill", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie"}, "funding": [{"url": "https://github.com/PHPCompatibility", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-04-24T21:30:46+00:00"}, {"name": "phpcompatibility/phpcompatibility-wp", "version": "2.1.6", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityWP.git", "reference": "80ccb1a7640995edf1b87a4409fa584cd5869469"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityWP/zipball/80ccb1a7640995edf1b87a4409fa584cd5869469", "reference": "80ccb1a7640995edf1b87a4409fa584cd5869469", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityWP/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}, "funding": [{"url": "https://github.com/PHPCompatibility", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2025-01-16T22:34:19+00:00"}, {"name": "phpcsstandards/phpcsextra", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSExtra.git", "reference": "11d387c6642b6e4acaf0bd9bf5203b8cca1ec489"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSExtra/zipball/11d387c6642b6e4acaf0bd9bf5203b8cca1ec489", "reference": "11d387c6642b6e4acaf0bd9bf5203b8cca1ec489", "shasum": ""}, "require": {"php": ">=5.4", "phpcsstandards/phpcsutils": "^1.0.9", "squizlabs/php_codesniffer": "^3.8.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "phpcsstandards/phpcsdevtools": "^1.2.1", "phpunit/phpunit": "^4.5 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSExtra/graphs/contributors"}], "description": "A collection of sniffs and standards for use with PHP_CodeSniffer.", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHPCSExtra/issues", "security": "https://github.com/PHPCSStandards/PHPCSExtra/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSExtra"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2023-12-08T16:49:07+00:00"}, {"name": "phpcsstandards/phpcsutils", "version": "1.0.12", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSUtils.git", "reference": "87b233b00daf83fb70f40c9a28692be017ea7c6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSUtils/zipball/87b233b00daf83fb70f40c9a28692be017ea7c6c", "reference": "87b233b00daf83fb70f40c9a28692be017ea7c6c", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^3.10.0 || 4.0.x-dev@dev"}, "require-dev": {"ext-filter": "*", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "yoast/phpunit-polyfills": "^1.1.0 || ^2.0.0"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "autoload": {"classmap": ["PHPCSUtils/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSUtils/graphs/contributors"}], "description": "A suite of utility functions for use with PHP_CodeSniffer", "homepage": "https://phpcsutils.com/", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "phpcs3", "standards", "static analysis", "tokens", "utility"], "support": {"docs": "https://phpcsutils.com/", "issues": "https://github.com/PHPCSStandards/PHPCSUtils/issues", "security": "https://github.com/PHPCSStandards/PHPCSUtils/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSUtils"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-05-20T13:34:27+00:00"}, {"name": "sirbrillig/phpcs-changed", "version": "v2.11.7", "source": {"type": "git", "url": "https://github.com/sirbrillig/phpcs-changed.git", "reference": "71ac60966beb5969f346f4a5defeb2ba025c712e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sirbrillig/phpcs-changed/zipball/71ac60966beb5969f346f4a5defeb2ba025c712e", "reference": "71ac60966beb5969f346f4a5defeb2ba025c712e", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.1 || ^1.0.0", "phpunit/phpunit": "^6.4 || ^9.5", "sirbrillig/phpcs-variable-analysis": "^2.1.3", "squizlabs/php_codesniffer": "^3.2.1", "vimeo/psalm": "^0.2 || ^0.3 || ^1.1 || ^4.24 || ^5.0@beta"}, "bin": ["bin/phpcs-changed"], "type": "library", "autoload": {"files": ["PhpcsChanged/Cli.php", "PhpcsChanged/functions.php"], "psr-4": {"PhpcsChanged\\": "PhpcsChanged/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Run phpcs on files, but only report warnings/errors from lines which were changed.", "support": {"issues": "https://github.com/sirbrillig/phpcs-changed/issues", "source": "https://github.com/sirbrillig/phpcs-changed/tree/v2.11.7"}, "time": "2025-01-14T18:34:53+00:00"}, {"name": "sirbrillig/phpcs-variable-analysis", "version": "v2.11.22", "source": {"type": "git", "url": "https://github.com/sirbrillig/phpcs-variable-analysis.git", "reference": "ffb6f16c6033ec61ed84446b479a31d6529f0eb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sirbrillig/phpcs-variable-analysis/zipball/ffb6f16c6033ec61ed84446b479a31d6529f0eb7", "reference": "ffb6f16c6033ec61ed84446b479a31d6529f0eb7", "shasum": ""}, "require": {"php": ">=5.4.0", "squizlabs/php_codesniffer": "^3.5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || ^1.0", "phpcsstandards/phpcsdevcs": "^1.1", "phpstan/phpstan": "^1.7", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.5 || ^7.0 || ^8.0 || ^9.0 || ^10.5.32 || ^11.3.3", "vimeo/psalm": "^0.2 || ^0.3 || ^1.1 || ^4.24 || ^5.0"}, "type": "phpcodesniffer-standard", "autoload": {"psr-4": {"VariableAnalysis\\": "VariableAnalysis/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A PHPCS sniff to detect problems with variables.", "keywords": ["phpcs", "static analysis"], "support": {"issues": "https://github.com/sirbrillig/phpcs-variable-analysis/issues", "source": "https://github.com/sirbrillig/phpcs-variable-analysis", "wiki": "https://github.com/sirbrillig/phpcs-variable-analysis/wiki"}, "time": "2025-01-06T17:54:24+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.11.2", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "1368f4a58c3c52114b86b1abe8f4098869cb0079"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/1368f4a58c3c52114b86b1abe8f4098869cb0079", "reference": "1368f4a58c3c52114b86b1abe8f4098869cb0079", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-12-11T16:04:26+00:00"}, {"name": "symfony/finder", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "87a71856f2f56e4100373e92529eed3171695cfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/87a71856f2f56e4100373e92529eed3171695cfb", "reference": "87a71856f2f56e4100373e92529eed3171695cfb", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.2.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:17+00:00"}, {"name": "wp-cli/i18n-command", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/wp-cli/i18n-command.git", "reference": "065bb3758fcbff922f1b7a01ab702aab0da79803"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/i18n-command/zipball/065bb3758fcbff922f1b7a01ab702aab0da79803", "reference": "065bb3758fcbff922f1b7a01ab702aab0da79803", "shasum": ""}, "require": {"eftec/bladeone": "3.52", "gettext/gettext": "^4.8", "mck89/peast": "^1.13.11", "wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/scaffold-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^4"}, "suggest": {"ext-json": "Used for reading and generating JSON translation files", "ext-mbstring": "Used for calculating include/exclude matches in code extraction"}, "type": "wp-cli-package", "extra": {"bundled": true, "commands": ["i18n", "i18n make-pot", "i18n make-json", "i18n make-mo", "i18n make-php", "i18n update-po"], "branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"files": ["i18n-command.php"], "psr-4": {"WP_CLI\\I18n\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://pascalbirchler.com/"}], "description": "Provides internationalization tools for WordPress projects.", "homepage": "https://github.com/wp-cli/i18n-command", "support": {"issues": "https://github.com/wp-cli/i18n-command/issues", "source": "https://github.com/wp-cli/i18n-command/tree/v2.6.3"}, "time": "2024-10-01T11:16:25+00:00"}, {"name": "wp-cli/mustangostang-spyc", "version": "0.6.3", "source": {"type": "git", "url": "https://github.com/wp-cli/spyc.git", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/spyc/zipball/6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "4.3.*@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.5.x-dev"}}, "autoload": {"files": ["includes/functions.php"], "psr-4": {"Mustangostang\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "mustangostang", "email": "<EMAIL>"}], "description": "A simple YAML loader/dumper class for PHP (WP-CLI fork)", "homepage": "https://github.com/mustangostang/spyc/", "support": {"source": "https://github.com/wp-cli/spyc/tree/autoload"}, "time": "2017-04-25T11:26:20+00:00"}, {"name": "wp-cli/php-cli-tools", "version": "v0.11.22", "source": {"type": "git", "url": "https://github.com/wp-cli/php-cli-tools.git", "reference": "a6bb94664ca36d0962f9c2ff25591c315a550c51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/php-cli-tools/zipball/a6bb94664ca36d0962f9c2ff25591c315a550c51", "reference": "a6bb94664ca36d0962f9c2ff25591c315a550c51", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "wp-cli/wp-cli-tests": "^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"files": ["lib/cli/cli.php"], "psr-0": {"cli": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Console utilities for PHP", "homepage": "http://github.com/wp-cli/php-cli-tools", "keywords": ["cli", "console"], "support": {"issues": "https://github.com/wp-cli/php-cli-tools/issues", "source": "https://github.com/wp-cli/php-cli-tools/tree/v0.11.22"}, "time": "2023-12-03T19:25:05+00:00"}, {"name": "wp-cli/wp-cli", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/wp-cli/wp-cli.git", "reference": "53f0df112901fcf95099d0f501912a209429b6a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/wp-cli/zipball/53f0df112901fcf95099d0f501912a209429b6a9", "reference": "53f0df112901fcf95099d0f501912a209429b6a9", "shasum": ""}, "require": {"ext-curl": "*", "mustache/mustache": "^2.14.1", "php": "^5.6 || ^7.0 || ^8.0", "symfony/finder": ">2.7", "wp-cli/mustangostang-spyc": "^0.6.3", "wp-cli/php-cli-tools": "~0.11.2"}, "require-dev": {"roave/security-advisories": "dev-latest", "wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.2 || ^2", "wp-cli/extension-command": "^1.1 || ^2", "wp-cli/package-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^4.0.1"}, "suggest": {"ext-readline": "Include for a better --prompt implementation", "ext-zip": "Needed to support extraction of ZIP archives when doing downloads or updates"}, "bin": ["bin/wp", "bin/wp.bat"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.11.x-dev"}}, "autoload": {"psr-0": {"WP_CLI\\": "php/"}, "classmap": ["php/class-wp-cli.php", "php/class-wp-cli-command.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WP-CLI framework", "homepage": "https://wp-cli.org", "keywords": ["cli", "wordpress"], "support": {"docs": "https://make.wordpress.org/cli/handbook/", "issues": "https://github.com/wp-cli/wp-cli/issues", "source": "https://github.com/wp-cli/wp-cli"}, "time": "2024-08-08T03:04:55+00:00"}, {"name": "wp-coding-standards/wpcs", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/9333efcbff231f10dfd9c56bb7b65818b4733ca7", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7", "shasum": ""}, "require": {"ext-filter": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-xmlreader": "*", "php": ">=5.4", "phpcsstandards/phpcsextra": "^1.2.1", "phpcsstandards/phpcsutils": "^1.0.10", "squizlabs/php_codesniffer": "^3.9.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "suggest": {"ext-iconv": "For improved results", "ext-mbstring": "For improved results"}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}, "funding": [{"url": "https://opencollective.com/php_codesniffer", "type": "custom"}], "time": "2024-03-25T16:39:00+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}